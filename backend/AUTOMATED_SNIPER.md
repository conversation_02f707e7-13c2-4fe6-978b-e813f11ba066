# 🎯 Sol Bullet - Fully Automated Sniper Bot

## 🤖 **COMPLETE AUTOMATION - NO MANUAL INTERVENTION REQUIRED**

This is a **fully automated** Solana sniper bot that operates 24/7 without any manual intervention. Once configured, it will:

1. **🔍 MONITOR**: Continuously watch Raydium for new token launches
2. **⚡ FILTER**: Automatically run safety checks on detected tokens  
3. **💰 BUY**: Instantly purchase tokens that pass all filters
4. **📊 TRACK**: Monitor prices every 2 seconds for all positions
5. **📈 SELL**: Automatically sell when profit/loss targets are hit
6. **🔄 REPEAT**: Continuous operation without stopping

## 🚀 **Automated Flow Diagram**

```
New Token Detected → Safety Filters → Auto-Buy → Price Monitoring → Auto-Sell
       ↓                  ↓             ↓            ↓              ↓
   Raydium Pool      Liquidity      Jupiter       Every 2s        Jupiter
   Creation         Honeypot       Swap API      Price Check     Swap API
                    Rug Check                    Take Profit
                    Tax Check                    Stop Loss
```

## ⚡ **Jupiter Integration - Best Prices Guaranteed**

### **All Trades Via Jupiter**
- **🔄 Buy Orders**: SOL → Token via Jupiter aggregator
- **🔄 Sell Orders**: Token → SOL via Jupiter aggregator  
- **📊 Price Discovery**: Real-time prices via Jupiter API
- **🛣️ Optimal Routing**: Jupiter finds best swap routes automatically
- **💰 Best Execution**: Always gets the best available price

### **Jupiter API Endpoints Used**
- `GET /quote` - Get swap quotes for buy/sell
- `POST /swap` - Execute swaps with signed transactions
- `GET /price` - Real-time price monitoring
- `GET /tokens` - Token metadata and validation

## 🎯 **Automated Trading Logic**

### **Token Detection & Filtering**
```typescript
// When new token detected:
1. Check liquidity >= MIN_LIQUIDITY_SOL
2. Validate metadata (name, symbol, creator)
3. Test for honeypot (simulate buy/sell)
4. Check rug pull indicators
5. Validate tax rates
6. If ALL checks pass → AUTO-BUY
```

### **Automatic Buying**
```typescript
// For each user with sniper enabled:
1. Check wallet SOL balance >= buy amount + fees
2. Get Jupiter quote: SOL → Token
3. Execute swap via Jupiter
4. Create position for monitoring
5. Setup take profit & stop loss alerts
6. Start price monitoring
```

### **Automatic Selling**
```typescript
// Every 2 seconds for all positions:
1. Get current token price via Jupiter
2. Calculate profit/loss percentage
3. If profit >= TAKE_PROFIT_PERCENT → AUTO-SELL
4. If loss >= STOP_LOSS_PERCENT → AUTO-SELL
5. Execute sell via Jupiter
6. Close position and send notifications
```

## 🛠️ **Setup for Automated Operation**

### **1. Install & Configure**
```bash
cd sol-bullet/backend
npm install
cp .env.example .env
# Edit .env with your API keys
```

### **2. Setup Database**
```sql
-- Run database/schema.sql in Supabase
```

### **3. Create Test User**
```bash
npm run setup-user
```
This creates a user with wallet and enables automated sniper.

### **4. Start Automated Sniper**
```bash
npm run dev
```

### **5. Fund Wallet & Watch It Work**
- Send SOL to the generated wallet address
- Bot automatically starts monitoring and trading
- Check logs and notifications for activity

## 📊 **Configuration for Automation**

### **Trading Settings**
```env
# How much SOL to spend per token
DEFAULT_BUY_AMOUNT_SOL=0.1

# When to take profits (100% = double your money)
TAKE_PROFIT_PERCENT=100

# When to cut losses (50% = sell if down 50%)
STOP_LOSS_PERCENT=50

# Minimum pool liquidity to consider
MIN_LIQUIDITY_SOL=5
```

### **Safety Filters**
```env
# Minimum token holders required
MIN_HOLDERS=10

# Maximum buy/sell tax allowed
MAX_BUY_TAX_PERCENT=10
MAX_SELL_TAX_PERCENT=10

# Enable safety checks
HONEYPOT_CHECK_ENABLED=true
RUG_PULL_PROTECTION_ENABLED=true
```

## 🔄 **API Endpoints for Automation Control**

### **Sniper Control**
```bash
# Enable automated sniper for user
POST /sniper/start
{ "userId": "user-id" }

# Disable automated sniper for user  
POST /sniper/stop
{ "userId": "user-id" }

# Get sniper status
GET /status
```

### **User Management**
```bash
# Add wallet to user for automated trading
POST /user/wallet
{ "userId": "user-id", "privateKey": "hex-private-key" }

# Get wallet balances
GET /balance/:walletAddress
```

## 📈 **Real-Time Monitoring**

### **Automated Notifications**
- **🟢 Token Detected**: When new token found and analyzed
- **💰 Auto-Buy Executed**: When bot automatically buys token
- **📈 Take Profit Hit**: When bot automatically sells for profit
- **📉 Stop Loss Hit**: When bot automatically sells to cut losses
- **❌ Errors**: Any issues with automated operation

### **Telegram/Discord Integration**
```env
# Telegram notifications
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# Discord notifications
DISCORD_WEBHOOK_URL=your_webhook_url
```

## 🎯 **Automated Operation Example**

```
🔍 NEW TOKEN DETECTED: TokenABC123...
   ├─ Liquidity: 15 SOL ✅ (>= 5 SOL required)
   ├─ Holders: 25 ✅ (>= 10 required)  
   ├─ Honeypot: Safe ✅
   ├─ Rug Check: Safe ✅
   └─ Tax: 5% ✅ (<= 10% allowed)

💰 AUTO-BUY EXECUTING via Jupiter...
   ├─ Amount: 0.1 SOL
   ├─ Route: SOL → USDC → TokenABC
   ├─ Price Impact: 2.1%
   └─ Transaction: abc123...def ✅

📊 POSITION CREATED - MONITORING PRICES...
   ├─ Buy Price: $0.000045
   ├─ Take Profit Target: $0.000090 (+100%)
   ├─ Stop Loss Target: $0.000022 (-50%)
   └─ Monitoring every 2 seconds...

📈 TAKE PROFIT TRIGGERED!
   ├─ Current Price: $0.000091 (+102%)
   ├─ Profit: +102%
   └─ AUTO-SELL EXECUTING via Jupiter...

✅ AUTO-SELL COMPLETED
   ├─ Sold: 2,222,222 tokens
   ├─ Received: 0.202 SOL
   ├─ Profit: +0.102 SOL (+102%)
   └─ Transaction: def456...ghi ✅
```

## 🔧 **Zero-Configuration Operation**

Once setup is complete, the bot operates completely automatically:

- **No manual buying required** - Bot buys automatically
- **No manual selling required** - Bot sells automatically  
- **No monitoring required** - Bot monitors prices automatically
- **No intervention needed** - Bot handles everything

Just fund your wallet with SOL and let the bot work 24/7!

## 🎯 **Quick Start for Automation**

```bash
# 1. Setup
cd sol-bullet/backend
npm install
cp .env.example .env

# 2. Configure .env with your API keys
# 3. Setup database schema in Supabase

# 4. Create automated user
npm run setup-user

# 5. Start automated sniper
npm run dev

# 6. Fund the generated wallet with SOL
# 7. Watch the bot work automatically!
```

## 📊 **Monitoring Your Automated Bot**

- **Logs**: Real-time logs show all automated actions
- **Notifications**: Telegram/Discord alerts for all trades
- **API Status**: `GET /status` shows current operation
- **Database**: All trades and positions tracked in Supabase

The bot is designed for **hands-off operation** - set it up once and let it trade automatically 24/7!
