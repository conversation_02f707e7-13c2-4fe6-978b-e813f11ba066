{"name": "solana-sniper-bot", "version": "1.0.0", "description": "Advanced Solana sniper bot with real-time monitoring and automated trading", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test-setup": "ts-node src/test-setup.ts", "setup-user": "ts-node src/setup-user.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["solana", "sniper", "bot", "trading", "raydium", "jupiter"], "author": "Sol Bullet Team", "license": "MIT", "dependencies": {"@solana/web3.js": "^1.98.4", "@solana/spl-token": "^0.4.8", "@raydium-io/raydium-sdk": "^1.3.1-beta.58", "@jup-ag/api": "^6.0.27", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.4.5", "winston": "^3.11.0", "ws": "^8.16.0", "axios": "^1.6.7", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "node-cron": "^3.0.3", "telegram-bot-api": "^2.0.1", "discord.js": "^14.14.1", "bn.js": "^5.2.1", "decimal.js": "^10.4.3", "@supabase/supabase-js": "^2.39.3"}, "devDependencies": {"@types/node": "^20.11.17", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/ws": "^8.5.10", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/pg": "^8.10.9", "@types/node-cron": "^3.0.11", "@types/jest": "^29.5.12", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.3", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}