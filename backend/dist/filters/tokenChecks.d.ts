import { TokenMetadata, FilterResult, TokenFilter, LiquidityPool } from '../types';
export declare class TokenFilterEngine {
    private filter;
    constructor(filter?: Partial<TokenFilter>);
    filterToken(tokenAddress: string, poolInfo: LiquidityPool, metadata?: TokenMetadata): Promise<FilterResult>;
    private checkBlacklist;
    private checkLiquidity;
    private checkMetadata;
    private checkHolders;
    private checkTaxes;
    private checkHoneypot;
    private checkRugPull;
    private fetchTokenMetadata;
    private normalizeMetadata;
    private getHolderCount;
    private simulateTaxes;
    private simulateHoneypot;
    private checkLiquidityLock;
    private checkCreatorConcentration;
    private checkRecentLargeSells;
    updateFilter(newFilter: Partial<TokenFilter>): void;
    getFilter(): TokenFilter;
}
export default TokenFilterEngine;
//# sourceMappingURL=tokenChecks.d.ts.map