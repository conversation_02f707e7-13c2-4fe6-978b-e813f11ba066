"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenFilterEngine = void 0;
const web3_js_1 = require("@solana/web3.js");
const axios_1 = __importDefault(require("axios"));
const wallet_1 = require("../utils/wallet");
const logger_1 = require("../utils/logger");
const config_1 = __importDefault(require("../config"));
class TokenFilterEngine {
    filter;
    constructor(filter) {
        this.filter = {
            minLiquidity: filter?.minLiquidity || config_1.default.trading.minLiquiditySol,
            minHolders: filter?.minHolders || config_1.default.safety.minHolders,
            maxBuyTax: filter?.maxBuyTax || config_1.default.safety.maxBuyTaxPercent,
            maxSellTax: filter?.maxSellTax || config_1.default.safety.maxSellTaxPercent,
            minLiquidityLockDays: filter?.minLiquidityLockDays || config_1.default.safety.minLiquidityLockDays,
            honeypotCheck: filter?.honeypotCheck ?? config_1.default.safety.honeypotCheckEnabled,
            rugPullCheck: filter?.rugPullCheck ?? config_1.default.safety.rugPullProtectionEnabled,
            metadataCheck: filter?.metadataCheck ?? true,
            blacklistedTokens: filter?.blacklistedTokens || [],
            blacklistedCreators: filter?.blacklistedCreators || [],
        };
    }
    async filterToken(tokenAddress, poolInfo, metadata) {
        const result = {
            passed: false,
            score: 0,
            checks: {
                liquidity: false,
                holders: false,
                taxes: false,
                honeypot: false,
                rugPull: false,
                metadata: false,
                blacklist: false,
            },
        };
        try {
            if (!this.checkBlacklist(tokenAddress, metadata?.creator)) {
                result.reason = 'Token or creator is blacklisted';
                return result;
            }
            result.checks.blacklist = true;
            result.score += 10;
            if (!this.checkLiquidity(poolInfo)) {
                result.reason = `Insufficient liquidity: ${poolInfo.liquidity.sol} SOL < ${this.filter.minLiquidity} SOL`;
                return result;
            }
            result.checks.liquidity = true;
            result.score += 20;
            const tokenMetadata = metadata || await this.fetchTokenMetadata(tokenAddress);
            if (this.filter.metadataCheck && !this.checkMetadata(tokenMetadata)) {
                result.reason = 'Invalid or suspicious metadata';
                return result;
            }
            result.checks.metadata = true;
            result.score += 15;
            if (!await this.checkHolders(tokenAddress, tokenMetadata)) {
                result.reason = `Insufficient holders: ${tokenMetadata?.holders || 0} < ${this.filter.minHolders}`;
                return result;
            }
            result.checks.holders = true;
            result.score += 15;
            if (!await this.checkTaxes(tokenAddress, poolInfo)) {
                result.reason = 'Tax rates exceed maximum allowed';
                return result;
            }
            result.checks.taxes = true;
            result.score += 15;
            if (this.filter.honeypotCheck && !await this.checkHoneypot(tokenAddress, poolInfo)) {
                result.reason = 'Token appears to be a honeypot';
                return result;
            }
            result.checks.honeypot = true;
            result.score += 15;
            if (this.filter.rugPullCheck && !await this.checkRugPull(tokenAddress, poolInfo, tokenMetadata)) {
                result.reason = 'High rug pull risk detected';
                return result;
            }
            result.checks.rugPull = true;
            result.score += 10;
            result.passed = true;
            logger_1.logger.info('Token passed all filters', {
                tokenAddress,
                score: result.score,
                liquidity: poolInfo.liquidity.sol,
            });
        }
        catch (error) {
            (0, logger_1.logError)(error, { tokenAddress, context: 'filterToken' });
            result.reason = `Filter error: ${error.message}`;
        }
        return result;
    }
    checkBlacklist(tokenAddress, creator) {
        if (this.filter.blacklistedTokens.includes(tokenAddress)) {
            return false;
        }
        if (creator && this.filter.blacklistedCreators.includes(creator)) {
            return false;
        }
        return true;
    }
    checkLiquidity(poolInfo) {
        return poolInfo.liquidity.sol >= this.filter.minLiquidity;
    }
    checkMetadata(metadata) {
        if (!metadata)
            return false;
        if (!metadata.name || !metadata.symbol)
            return false;
        const suspiciousPatterns = [
            /test/i,
            /fake/i,
            /scam/i,
            /rug/i,
            /honeypot/i,
            /^[a-f0-9]{8,}$/i,
            /^\$+$/,
            /^[0-9]+$/,
        ];
        const nameSymbolText = `${metadata.name} ${metadata.symbol}`.toLowerCase();
        if (suspiciousPatterns.some(pattern => pattern.test(nameSymbolText))) {
            return false;
        }
        return true;
    }
    async checkHolders(tokenAddress, metadata) {
        try {
            if (metadata?.holders !== undefined) {
                return metadata.holders >= this.filter.minHolders;
            }
            const holderCount = await this.getHolderCount(tokenAddress);
            return holderCount >= this.filter.minHolders;
        }
        catch (error) {
            logger_1.logger.warn('Failed to check holders', { tokenAddress, error: error.message });
            return false;
        }
    }
    async checkTaxes(tokenAddress, poolInfo) {
        try {
            const taxes = await this.simulateTaxes(tokenAddress, poolInfo);
            return taxes.buyTax <= this.filter.maxBuyTax &&
                taxes.sellTax <= this.filter.maxSellTax;
        }
        catch (error) {
            logger_1.logger.warn('Failed to check taxes', { tokenAddress, error: error.message });
            return true;
        }
    }
    async checkHoneypot(tokenAddress, poolInfo) {
        try {
            const simulation = await this.simulateHoneypot(tokenAddress, poolInfo);
            return simulation.canSell && simulation.sellSuccess;
        }
        catch (error) {
            logger_1.logger.warn('Failed to check honeypot', { tokenAddress, error: error.message });
            return true;
        }
    }
    async checkRugPull(tokenAddress, poolInfo, metadata) {
        try {
            const liquidityLocked = await this.checkLiquidityLock(poolInfo);
            if (!liquidityLocked)
                return false;
            const creatorConcentration = await this.checkCreatorConcentration(tokenAddress, metadata?.creator);
            if (creatorConcentration > 50)
                return false;
            const hasRecentLargeSells = await this.checkRecentLargeSells(tokenAddress);
            if (hasRecentLargeSells)
                return false;
            return true;
        }
        catch (error) {
            logger_1.logger.warn('Failed to check rug pull', { tokenAddress, error: error.message });
            return true;
        }
    }
    async fetchTokenMetadata(tokenAddress) {
        try {
            const sources = [
                `https://api.solana.fm/v1/tokens/${tokenAddress}`,
                `https://public-api.solscan.io/token/meta?tokenAddress=${tokenAddress}`,
            ];
            for (const source of sources) {
                try {
                    const response = await axios_1.default.get(source, { timeout: 5000 });
                    if (response.data) {
                        return this.normalizeMetadata(response.data, tokenAddress);
                    }
                }
                catch (error) {
                    continue;
                }
            }
            return undefined;
        }
        catch (error) {
            (0, logger_1.logError)(error, { tokenAddress, context: 'fetchTokenMetadata' });
            return undefined;
        }
    }
    normalizeMetadata(data, tokenAddress) {
        return {
            mint: tokenAddress,
            name: data.name || data.tokenName || '',
            symbol: data.symbol || data.tokenSymbol || '',
            description: data.description || '',
            image: data.image || data.logoURI || '',
            decimals: data.decimals || 9,
            supply: data.supply || data.totalSupply || 0,
            holders: data.holders || data.holderCount || 0,
            verified: data.verified || false,
            tags: data.tags || [],
            createdAt: new Date(data.createdAt || Date.now()),
            creator: data.creator || data.mintAuthority,
        };
    }
    async getHolderCount(tokenAddress) {
        return wallet_1.connectionManager.executeWithRetry(async (connection) => {
            const tokenAccounts = await connection.getProgramAccounts(new web3_js_1.PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'), {
                filters: [
                    { dataSize: 165 },
                    { memcmp: { offset: 0, bytes: tokenAddress } },
                ],
            });
            return tokenAccounts.length;
        });
    }
    async simulateTaxes(tokenAddress, poolInfo) {
        return {
            buyTax: 0,
            sellTax: 0,
        };
    }
    async simulateHoneypot(tokenAddress, poolInfo) {
        return {
            canSell: true,
            sellSuccess: true,
        };
    }
    async checkLiquidityLock(poolInfo) {
        return true;
    }
    async checkCreatorConcentration(tokenAddress, creator) {
        return 0;
    }
    async checkRecentLargeSells(tokenAddress) {
        return false;
    }
    updateFilter(newFilter) {
        this.filter = { ...this.filter, ...newFilter };
    }
    getFilter() {
        return { ...this.filter };
    }
}
exports.TokenFilterEngine = TokenFilterEngine;
exports.default = TokenFilterEngine;
//# sourceMappingURL=tokenChecks.js.map