#!/usr/bin/env ts-node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const web3_js_1 = require("@solana/web3.js");
const config_1 = __importDefault(require("./config"));
const logger_1 = require("./utils/logger");
async function testSetup() {
    console.log('🎯 Sol Bullet Backend Setup Test\n');
    console.log('1. Testing Configuration...');
    try {
        console.log(`   ✅ RPC Endpoint: ${config_1.default.solana.rpcEndpoint}`);
        console.log(`   ✅ Min Liquidity: ${config_1.default.trading.minLiquiditySol} SOL`);
        console.log(`   ✅ Buy Amount: ${config_1.default.trading.defaultBuyAmountSol} SOL`);
        console.log(`   ✅ Database: ${config_1.default.database.supabaseUrl ? 'Supabase' : 'PostgreSQL'}`);
        console.log('   ✅ Configuration loaded successfully\n');
    }
    catch (error) {
        console.log(`   ❌ Configuration error: ${error.message}\n`);
        return;
    }
    console.log('2. Testing Solana Connection...');
    try {
        const connection = new web3_js_1.Connection(config_1.default.solana.rpcEndpoint, 'confirmed');
        const slot = await connection.getSlot();
        const blockTime = await connection.getBlockTime(slot);
        console.log(`   ✅ Connected to Solana`);
        console.log(`   ✅ Current slot: ${slot}`);
        console.log(`   ✅ Block time: ${new Date(blockTime * 1000).toISOString()}`);
        console.log('   ✅ Solana connection successful\n');
    }
    catch (error) {
        console.log(`   ❌ Solana connection failed: ${error.message}\n`);
    }
    console.log('3. Testing Jupiter API...');
    try {
        const response = await fetch(`${config_1.default.jupiter.apiUrl}/tokens`);
        if (response.ok) {
            console.log('   ✅ Jupiter API accessible');
            console.log(`   ✅ API URL: ${config_1.default.jupiter.apiUrl}\n`);
        }
        else {
            console.log(`   ❌ Jupiter API error: ${response.status} ${response.statusText}\n`);
        }
    }
    catch (error) {
        console.log(`   ❌ Jupiter API failed: ${error.message}\n`);
    }
    console.log('4. Testing Database Connection...');
    try {
        if (config_1.default.database.supabaseUrl && config_1.default.database.supabaseServiceKey) {
            const { createClient } = await Promise.resolve().then(() => __importStar(require('@supabase/supabase-js')));
            const supabase = createClient(config_1.default.database.supabaseUrl, config_1.default.database.supabaseServiceKey);
            const { data, error } = await supabase.from('users').select('count').limit(1);
            if (error) {
                console.log(`   ❌ Supabase error: ${error.message}`);
                console.log('   💡 Make sure you\'ve run the database schema from database/schema.sql\n');
            }
            else {
                console.log('   ✅ Supabase connection successful');
                console.log('   ✅ Database schema accessible\n');
            }
        }
        else {
            console.log('   ⚠️  Database not configured (optional for testing)\n');
        }
    }
    catch (error) {
        console.log(`   ❌ Database test failed: ${error.message}\n`);
    }
    console.log('5. Testing Logging System...');
    try {
        logger_1.logger.info('Test log message');
        logger_1.systemLogger.startup({ test: true });
        console.log('   ✅ Logging system working');
        console.log(`   ✅ Log level: ${config_1.default.logging.level}`);
        console.log(`   ✅ Log to file: ${config_1.default.logging.toFile}\n`);
    }
    catch (error) {
        console.log(`   ❌ Logging test failed: ${error.message}\n`);
    }
    console.log('6. Testing Notifications...');
    try {
        if (config_1.default.notifications.telegramBotToken) {
            console.log('   ✅ Telegram configured');
        }
        if (config_1.default.notifications.discordWebhookUrl) {
            console.log('   ✅ Discord configured');
        }
        if (!config_1.default.notifications.telegramBotToken && !config_1.default.notifications.discordWebhookUrl) {
            console.log('   ⚠️  No notifications configured (optional)');
        }
        console.log('');
    }
    catch (error) {
        console.log(`   ❌ Notification test failed: ${error.message}\n`);
    }
    console.log('7. Testing Wallet Operations...');
    try {
        const { generateWallet, getSolBalance } = await Promise.resolve().then(() => __importStar(require('./utils/wallet')));
        const { keypair } = generateWallet();
        console.log(`   ✅ Generated test wallet: ${keypair.publicKey.toString()}`);
        const balance = await getSolBalance(keypair.publicKey);
        console.log(`   ✅ Balance check successful: ${balance} SOL`);
        console.log('   ✅ Wallet operations working\n');
    }
    catch (error) {
        console.log(`   ❌ Wallet test failed: ${error.message}\n`);
    }
    console.log('🎯 Setup Test Complete!');
    console.log('\nNext Steps:');
    console.log('1. If all tests passed, run: npm run dev');
    console.log('2. Check logs in logs/ directory');
    console.log('3. Test API endpoints with curl or Postman');
    console.log('4. Configure frontend to connect to this backend');
    console.log('\nFor production deployment, see DEPLOYMENT.md');
}
testSetup().catch((error) => {
    console.error('Setup test failed:', error);
    process.exit(1);
});
//# sourceMappingURL=test-setup.js.map