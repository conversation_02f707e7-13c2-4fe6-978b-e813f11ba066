import { PublicKey, Keypair } from '@solana/web3.js';
import { Decimal } from 'decimal.js';
export interface WalletData {
    id: string;
    userId: string;
    publicKey: string;
    encryptedPrivateKey: string;
    keypair?: Keypair;
    createdAt: Date;
    updatedAt: Date;
}
export interface UserWallet {
    publicKey: PublicKey;
    keypair: Keypair;
    balance: {
        sol: number;
        tokens: TokenBalance[];
    };
}
export interface TokenBalance {
    mint: string;
    amount: number;
    decimals: number;
    uiAmount: number;
}
export interface TokenInfo {
    mint: string;
    name: string;
    symbol: string;
    decimals: number;
    logoURI?: string;
    verified: boolean;
    tags?: string[];
    extensions?: Record<string, any>;
}
export interface TokenMetadata {
    mint: string;
    name: string;
    symbol: string;
    description?: string;
    image?: string;
    decimals: number;
    supply: number;
    holders: number;
    verified: boolean;
    tags: string[];
    createdAt: Date;
    creator?: string;
}
export interface PoolInfo {
    id: string;
    baseMint: string;
    quoteMint: string;
    lpMint: string;
    baseDecimals: number;
    quoteDecimals: number;
    lpDecimals: number;
    version: number;
    programId: string;
    authority: string;
    openOrders: string;
    targetOrders: string;
    baseVault: string;
    quoteVault: string;
    withdrawQueue: string;
    lpVault: string;
    marketVersion: number;
    marketProgramId: string;
    marketId: string;
    marketAuthority: string;
    marketBaseVault: string;
    marketQuoteVault: string;
    marketBids: string;
    marketAsks: string;
    marketEventQueue: string;
    lookupTableAccount?: string;
}
export interface LiquidityPool {
    poolId: string;
    baseMint: string;
    quoteMint: string;
    baseReserve: Decimal;
    quoteReserve: Decimal;
    lpSupply: Decimal;
    price: Decimal;
    liquidity: {
        sol: number;
        usd: number;
    };
    volume24h: number;
    createdAt: Date;
    isActive: boolean;
}
export interface TradeParams {
    walletAddress: string;
    tokenAddress: string;
    amount?: number;
    slippage?: number;
    priorityFee?: number;
    maxSlippage?: number;
    percentage?: number;
    sellAmountTokens?: number;
}
export interface TradeResult {
    success: boolean;
    signature?: string;
    error?: string;
    amount?: number;
    price?: number;
    slippage?: number;
    fee?: number;
    timestamp: Date;
}
export interface SwapQuote {
    inputMint: string;
    inAmount: string;
    outputMint: string;
    outAmount: string;
    otherAmountThreshold: string;
    swapMode: string;
    slippageBps: number;
    platformFee?: {
        amount: string;
        feeBps: number;
    };
    priceImpactPct: string;
    routePlan: RoutePlan[];
}
export interface RoutePlan {
    swapInfo: {
        ammKey: string;
        label: string;
        inputMint: string;
        outputMint: string;
        inAmount: string;
        outAmount: string;
        feeAmount: string;
        feeMint: string;
    };
    percent: number;
}
export interface TokenFilter {
    minLiquidity: number;
    minHolders: number;
    maxBuyTax: number;
    maxSellTax: number;
    minLiquidityLockDays: number;
    honeypotCheck: boolean;
    rugPullCheck: boolean;
    metadataCheck: boolean;
    blacklistedTokens: string[];
    blacklistedCreators: string[];
}
export interface FilterResult {
    passed: boolean;
    reason?: string;
    score: number;
    checks: {
        liquidity: boolean;
        holders: boolean;
        taxes: boolean;
        honeypot: boolean;
        rugPull: boolean;
        metadata: boolean;
        blacklist: boolean;
    };
}
export interface Transaction {
    id: string;
    userId: string;
    walletAddress: string;
    type: 'buy' | 'sell';
    tokenAddress: string;
    amount: number;
    price: number;
    slippage: number;
    signature: string;
    status: 'pending' | 'confirmed' | 'failed';
    error?: string;
    createdAt: Date;
    confirmedAt?: Date;
}
export interface Position {
    id: string;
    userId: string;
    walletAddress: string;
    tokenAddress: string;
    amount: number;
    averageBuyPrice: number;
    currentPrice: number;
    pnl: number;
    pnlPercent: number;
    status: 'active' | 'closed';
    createdAt: Date;
    closedAt?: Date;
}
export interface PriceAlert {
    id: string;
    userId: string;
    tokenAddress: string;
    type: 'take_profit' | 'stop_loss' | 'trailing_stop';
    targetPrice: number;
    currentPrice: number;
    percentage: number;
    isActive: boolean;
    triggeredAt?: Date;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    timestamp: Date;
}
export interface BuyRequest {
    walletAddress: string;
    tokenAddress?: string;
    amount?: number;
    slippage?: number;
    priorityFee?: number;
    maxSlippage?: number;
}
export interface SellRequest {
    walletAddress: string;
    tokenAddress?: string;
    percentage?: number;
    slippage?: number;
    sellAmountTokens?: number;
}
export interface PoolCreatedEvent {
    poolId: string;
    baseMint: string;
    quoteMint: string;
    baseReserve: string;
    quoteReserve: string;
    timestamp: Date;
    signature: string;
    slot: number;
}
export interface TokenDetectedEvent {
    tokenAddress: string;
    poolId: string;
    liquidity: number;
    timestamp: Date;
    metadata?: TokenMetadata;
}
export interface NotificationPayload {
    type: 'buy' | 'sell' | 'alert' | 'error';
    title: string;
    message: string;
    data?: Record<string, any>;
    timestamp: Date;
}
export interface User {
    id: string;
    email?: string;
    wallets: string[];
    settings: UserSettings;
    createdAt: Date;
    updatedAt: Date;
}
export interface UserSettings {
    trading: {
        buyAmount: number;
        slippage: number;
        autoSell: boolean;
        takeProfitPercent: number;
        stopLossPercent: number;
    };
    safety: TokenFilter;
    notifications: {
        telegram: boolean;
        discord: boolean;
        email: boolean;
    };
}
//# sourceMappingURL=index.d.ts.map