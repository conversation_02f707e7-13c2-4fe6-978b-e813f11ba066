import { PublicKey } from '@solana/web3.js';
export interface Config {
    solana: {
        rpcEndpoint: string;
        wsEndpoint: string;
        backupRpcEndpoints: string[];
        commitment: string;
    };
    helius: {
        apiKey: string;
        webhookUrl: string;
    };
    trading: {
        minLiquiditySol: number;
        defaultBuyAmountSol: number;
        defaultSlippagePercent: number;
        maxSlippagePercent: number;
        priorityFeeSol: number;
        maxPriorityFeeSol: number;
    };
    profitLoss: {
        takeProfitPercent: number;
        stopLossPercent: number;
        trailingStopPercent: number;
        autoSellEnabled: boolean;
        autoSellDelayMs: number;
    };
    safety: {
        minHolders: number;
        maxBuyTaxPercent: number;
        maxSellTaxPercent: number;
        minLiquidityLockDays: number;
        honeypotCheckEnabled: boolean;
        mevProtectionEnabled: boolean;
        rugPullProtectionEnabled: boolean;
    };
    limits: {
        maxTransactionSizeSol: number;
        dailyTransactionLimit: number;
        maxConcurrentTransactions: number;
        transactionRetryAttempts: number;
    };
    database: {
        supabaseUrl?: string;
        supabaseAnonKey?: string;
        supabaseServiceKey?: string;
        databaseUrl?: string;
    };
    security: {
        jwtSecret: string;
        encryptionKey: string;
    };
    api: {
        port: number;
        nodeEnv: string;
        corsOrigin: string;
    };
    logging: {
        level: string;
        toFile: boolean;
        directory: string;
    };
    notifications: {
        telegramBotToken?: string;
        telegramChatId?: string;
        discordWebhookUrl?: string;
    };
    jupiter: {
        apiUrl: string;
        swapApiUrl: string;
    };
    raydium: {
        programId: PublicKey;
        authority: PublicKey;
    };
    performance: {
        poolMonitorIntervalMs: number;
        balanceCheckIntervalMs: number;
        priceCheckIntervalMs: number;
    };
    development: {
        debugMode: boolean;
        simulateTransactions: boolean;
        dryRunMode: boolean;
    };
}
export declare const config: Config;
export default config;
//# sourceMappingURL=index.d.ts.map