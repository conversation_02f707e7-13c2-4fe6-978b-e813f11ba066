"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const web3_js_1 = require("@solana/web3.js");
dotenv_1.default.config();
function getEnvVar(key, defaultValue) {
    const value = process.env[key];
    if (!value && !defaultValue) {
        throw new Error(`Environment variable ${key} is required`);
    }
    return value || defaultValue;
}
function getEnvNumber(key, defaultValue) {
    const value = process.env[key];
    if (!value && defaultValue === undefined) {
        throw new Error(`Environment variable ${key} is required`);
    }
    return value ? parseFloat(value) : defaultValue;
}
function getEnvBoolean(key, defaultValue = false) {
    const value = process.env[key];
    return value ? value.toLowerCase() === 'true' : defaultValue;
}
exports.config = {
    solana: {
        rpcEndpoint: getEnvVar('SOLANA_RPC_ENDPOINT', 'https://api.mainnet-beta.solana.com'),
        wsEndpoint: process.env['SOLANA_WS_ENDPOINT'] || '',
        backupRpcEndpoints: (process.env['BACKUP_RPC_ENDPOINTS'] || 'https://api.mainnet-beta.solana.com').split(',').filter(Boolean),
        commitment: 'confirmed',
    },
    helius: {
        apiKey: process.env['HELIUS_API_KEY'] || '',
        webhookUrl: process.env['HELIUS_WEBHOOK_URL'] || '',
    },
    trading: {
        minLiquiditySol: getEnvNumber('MIN_LIQUIDITY_SOL', 5),
        defaultBuyAmountSol: getEnvNumber('DEFAULT_BUY_AMOUNT_SOL', 0.1),
        defaultSlippagePercent: getEnvNumber('DEFAULT_SLIPPAGE_PERCENT', 5),
        maxSlippagePercent: getEnvNumber('MAX_SLIPPAGE_PERCENT', 15),
        priorityFeeSol: getEnvNumber('PRIORITY_FEE_SOL', 0.001),
        maxPriorityFeeSol: getEnvNumber('MAX_PRIORITY_FEE_SOL', 0.01),
    },
    profitLoss: {
        takeProfitPercent: getEnvNumber('TAKE_PROFIT_PERCENT', 100),
        stopLossPercent: getEnvNumber('STOP_LOSS_PERCENT', 50),
        trailingStopPercent: getEnvNumber('TRAILING_STOP_PERCENT', 10),
        autoSellEnabled: getEnvBoolean('AUTO_SELL_ENABLED', true),
        autoSellDelayMs: getEnvNumber('AUTO_SELL_DELAY_MS', 5000),
    },
    safety: {
        minHolders: getEnvNumber('MIN_HOLDERS', 10),
        maxBuyTaxPercent: getEnvNumber('MAX_BUY_TAX_PERCENT', 10),
        maxSellTaxPercent: getEnvNumber('MAX_SELL_TAX_PERCENT', 10),
        minLiquidityLockDays: getEnvNumber('MIN_LIQUIDITY_LOCK_DAYS', 7),
        honeypotCheckEnabled: getEnvBoolean('HONEYPOT_CHECK_ENABLED', true),
        mevProtectionEnabled: getEnvBoolean('MEV_PROTECTION_ENABLED', true),
        rugPullProtectionEnabled: getEnvBoolean('RUG_PULL_PROTECTION_ENABLED', true),
    },
    limits: {
        maxTransactionSizeSol: getEnvNumber('MAX_TRANSACTION_SIZE_SOL', 10),
        dailyTransactionLimit: getEnvNumber('DAILY_TRANSACTION_LIMIT', 100),
        maxConcurrentTransactions: getEnvNumber('MAX_CONCURRENT_TRANSACTIONS', 3),
        transactionRetryAttempts: getEnvNumber('TRANSACTION_RETRY_ATTEMPTS', 3),
    },
    database: {
        supabaseUrl: process.env['SUPABASE_URL'] || 'https://niyzywbnfzauwjgygxwp.supabase.co',
        supabaseAnonKey: process.env['SUPABASE_ANON_KEY'] || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5peXp5d2JuZnphdXdqZ3lneHdwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUwMzMxMDMsImV4cCI6MjA3MDYwOTEwM30.bsqktWnHx7ZK6lBzS013HbMvUOZKh09VVUfhv9PrNuo',
        supabaseServiceKey: process.env['SUPABASE_SERVICE_KEY'] || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5peXp5d2JuZnphdXdqZ3lneHdwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NTAzMzEwMywiZXhwIjoyMDcwNjA5MTAzfQ.HwMJIbkR-PcH6P6MtK3SnPukLj971D1UwN22oGW9LG4',
        databaseUrl: process.env['DATABASE_URL'] || '',
    },
    security: {
        jwtSecret: getEnvVar('JWT_SECRET'),
        encryptionKey: getEnvVar('ENCRYPTION_KEY'),
    },
    api: {
        port: getEnvNumber('PORT', 3001),
        nodeEnv: getEnvVar('NODE_ENV', 'development'),
        corsOrigin: getEnvVar('CORS_ORIGIN', 'http://localhost:3000'),
    },
    logging: {
        level: getEnvVar('LOG_LEVEL', 'info'),
        toFile: getEnvBoolean('LOG_TO_FILE', true),
        directory: getEnvVar('LOG_DIRECTORY', './logs'),
    },
    notifications: {
        telegramBotToken: process.env['TELEGRAM_BOT_TOKEN'] || '',
        telegramChatId: process.env['TELEGRAM_CHAT_ID'] || '',
        discordWebhookUrl: process.env['DISCORD_WEBHOOK_URL'] || '',
    },
    jupiter: {
        apiUrl: getEnvVar('JUPITER_API_URL', 'https://quote-api.jup.ag/v6'),
        swapApiUrl: getEnvVar('JUPITER_SWAP_API_URL', 'https://quote-api.jup.ag/v6/swap'),
    },
    raydium: {
        programId: new web3_js_1.PublicKey(getEnvVar('RAYDIUM_PROGRAM_ID', '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8')),
        authority: new web3_js_1.PublicKey(getEnvVar('RAYDIUM_AUTHORITY', '5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1')),
    },
    performance: {
        poolMonitorIntervalMs: getEnvNumber('POOL_MONITOR_INTERVAL_MS', 1000),
        balanceCheckIntervalMs: getEnvNumber('BALANCE_CHECK_INTERVAL_MS', 5000),
        priceCheckIntervalMs: getEnvNumber('PRICE_CHECK_INTERVAL_MS', 2000),
    },
    development: {
        debugMode: getEnvBoolean('DEBUG_MODE', false),
        simulateTransactions: getEnvBoolean('SIMULATE_TRANSACTIONS', false),
        dryRunMode: getEnvBoolean('DRY_RUN_MODE', false),
    },
};
exports.default = exports.config;
//# sourceMappingURL=index.js.map