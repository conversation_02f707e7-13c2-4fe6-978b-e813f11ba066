import { WalletData, User, Transaction, Position, UserSettings } from '../types';
export declare class SupabaseManager {
    private supabase;
    constructor();
    createUser(email?: string): Promise<User>;
    getUser(userId: string): Promise<User | null>;
    updateUserSettings(userId: string, settings: UserSettings): Promise<void>;
    createWallet(userId: string, publicKey: string, privateKey: Uint8Array): Promise<WalletData>;
    getWallet(walletId: string): Promise<WalletData | null>;
    getWalletByPublicKey(publicKey: string): Promise<WalletData | null>;
    getUserWallets(userId: string): Promise<WalletData[]>;
    private addWalletToUser;
    saveTransaction(transaction: Omit<Transaction, 'id' | 'createdAt'>): Promise<Transaction>;
    updateTransactionStatus(transactionId: string, status: 'confirmed' | 'failed', error?: string): Promise<void>;
    savePosition(position: Omit<Position, 'id' | 'createdAt'>): Promise<Position>;
    private getDefaultUserSettings;
}
export default SupabaseManager;
//# sourceMappingURL=supabase.d.ts.map