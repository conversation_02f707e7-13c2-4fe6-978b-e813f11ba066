"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseManager = void 0;
const supabase_js_1 = require("@supabase/supabase-js");
const wallet_1 = require("../utils/wallet");
const logger_1 = require("../utils/logger");
const config_1 = __importDefault(require("../config"));
class SupabaseManager {
    supabase;
    constructor() {
        if (!config_1.default.database.supabaseUrl || !config_1.default.database.supabaseServiceKey) {
            throw new Error('Supabase configuration missing');
        }
        this.supabase = (0, supabase_js_1.createClient)(config_1.default.database.supabaseUrl, config_1.default.database.supabaseServiceKey);
    }
    async createUser(email) {
        try {
            const userData = {
                email,
                wallets: [],
                settings: this.getDefaultUserSettings(),
                created_at: new Date(),
                updated_at: new Date(),
            };
            const { data, error } = await this.supabase
                .from('users')
                .insert(userData)
                .select()
                .single();
            if (error)
                throw error;
            return {
                id: data.id,
                email: data.email,
                wallets: data.wallets,
                settings: data.settings,
                createdAt: new Date(data.created_at),
                updatedAt: new Date(data.updated_at),
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'createUser', email });
            throw error;
        }
    }
    async getUser(userId) {
        try {
            const { data, error } = await this.supabase
                .from('users')
                .select('*')
                .eq('id', userId)
                .single();
            if (error) {
                if (error.code === 'PGRST116')
                    return null;
                throw error;
            }
            return {
                id: data.id,
                email: data.email,
                wallets: data.wallets,
                settings: data.settings,
                createdAt: new Date(data.created_at),
                updatedAt: new Date(data.updated_at),
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'getUser', userId });
            throw error;
        }
    }
    async updateUserSettings(userId, settings) {
        try {
            const { error } = await this.supabase
                .from('users')
                .update({
                settings,
                updated_at: new Date(),
            })
                .eq('id', userId);
            if (error)
                throw error;
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'updateUserSettings', userId });
            throw error;
        }
    }
    async createWallet(userId, publicKey, privateKey) {
        try {
            const encryptedPrivateKey = (0, wallet_1.encryptPrivateKey)(privateKey, config_1.default.security.encryptionKey);
            const walletData = {
                user_id: userId,
                public_key: publicKey,
                encrypted_private_key: encryptedPrivateKey,
                created_at: new Date(),
                updated_at: new Date(),
            };
            const { data, error } = await this.supabase
                .from('wallets')
                .insert(walletData)
                .select()
                .single();
            if (error)
                throw error;
            await this.addWalletToUser(userId, data.id);
            return {
                id: data.id,
                userId: data.user_id,
                publicKey: data.public_key,
                encryptedPrivateKey: data.encrypted_private_key,
                createdAt: new Date(data.created_at),
                updatedAt: new Date(data.updated_at),
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'createWallet', userId, publicKey });
            throw error;
        }
    }
    async getWallet(walletId) {
        try {
            const { data, error } = await this.supabase
                .from('wallets')
                .select('*')
                .eq('id', walletId)
                .single();
            if (error) {
                if (error.code === 'PGRST116')
                    return null;
                throw error;
            }
            return {
                id: data.id,
                userId: data.user_id,
                publicKey: data.public_key,
                encryptedPrivateKey: data.encrypted_private_key,
                createdAt: new Date(data.created_at),
                updatedAt: new Date(data.updated_at),
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'getWallet', walletId });
            throw error;
        }
    }
    async getWalletByPublicKey(publicKey) {
        try {
            const { data, error } = await this.supabase
                .from('wallets')
                .select('*')
                .eq('public_key', publicKey)
                .single();
            if (error) {
                if (error.code === 'PGRST116')
                    return null;
                throw error;
            }
            return {
                id: data.id,
                userId: data.user_id,
                publicKey: data.public_key,
                encryptedPrivateKey: data.encrypted_private_key,
                createdAt: new Date(data.created_at),
                updatedAt: new Date(data.updated_at),
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'getWalletByPublicKey', publicKey });
            throw error;
        }
    }
    async getUserWallets(userId) {
        try {
            const { data, error } = await this.supabase
                .from('wallets')
                .select('*')
                .eq('user_id', userId);
            if (error)
                throw error;
            return data.map(wallet => ({
                id: wallet.id,
                userId: wallet.user_id,
                publicKey: wallet.public_key,
                encryptedPrivateKey: wallet.encrypted_private_key,
                createdAt: new Date(wallet.created_at),
                updatedAt: new Date(wallet.updated_at),
            }));
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'getUserWallets', userId });
            throw error;
        }
    }
    async addWalletToUser(userId, walletId) {
        try {
            const user = await this.getUser(userId);
            if (!user)
                throw new Error('User not found');
            const updatedWallets = [...user.wallets, walletId];
            const { error } = await this.supabase
                .from('users')
                .update({
                wallets: updatedWallets,
                updated_at: new Date(),
            })
                .eq('id', userId);
            if (error)
                throw error;
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'addWalletToUser', userId, walletId });
            throw error;
        }
    }
    async saveTransaction(transaction) {
        try {
            const transactionData = {
                ...transaction,
                created_at: new Date(),
            };
            const { data, error } = await this.supabase
                .from('transactions')
                .insert(transactionData)
                .select()
                .single();
            if (error)
                throw error;
            return {
                id: data.id,
                userId: data.user_id,
                walletAddress: data.wallet_address,
                type: data.type,
                tokenAddress: data.token_address,
                amount: data.amount,
                price: data.price,
                slippage: data.slippage,
                signature: data.signature,
                status: data.status,
                error: data.error,
                createdAt: new Date(data.created_at),
                confirmedAt: data.confirmed_at ? new Date(data.confirmed_at) : new Date(),
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'saveTransaction', transaction });
            throw error;
        }
    }
    async updateTransactionStatus(transactionId, status, error) {
        try {
            const updateData = {
                status,
                updated_at: new Date(),
            };
            if (status === 'confirmed') {
                updateData.confirmed_at = new Date();
            }
            if (error) {
                updateData.error = error;
            }
            const { error: updateError } = await this.supabase
                .from('transactions')
                .update(updateData)
                .eq('id', transactionId);
            if (updateError)
                throw updateError;
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'updateTransactionStatus', transactionId, status });
            throw error;
        }
    }
    async savePosition(position) {
        try {
            const positionData = {
                ...position,
                created_at: new Date(),
            };
            const { data, error } = await this.supabase
                .from('positions')
                .insert(positionData)
                .select()
                .single();
            if (error)
                throw error;
            return {
                id: data.id,
                userId: data.user_id,
                walletAddress: data.wallet_address,
                tokenAddress: data.token_address,
                amount: data.amount,
                averageBuyPrice: data.average_buy_price,
                currentPrice: data.current_price,
                pnl: data.pnl,
                pnlPercent: data.pnl_percent,
                status: data.status,
                createdAt: new Date(data.created_at),
                closedAt: data.closed_at ? new Date(data.closed_at) : new Date(),
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'savePosition', position });
            throw error;
        }
    }
    getDefaultUserSettings() {
        return {
            trading: {
                buyAmount: config_1.default.trading.defaultBuyAmountSol,
                slippage: config_1.default.trading.defaultSlippagePercent,
                autoSell: config_1.default.profitLoss.autoSellEnabled,
                takeProfitPercent: config_1.default.profitLoss.takeProfitPercent,
                stopLossPercent: config_1.default.profitLoss.stopLossPercent,
            },
            safety: {
                minLiquidity: config_1.default.trading.minLiquiditySol,
                minHolders: config_1.default.safety.minHolders,
                maxBuyTax: config_1.default.safety.maxBuyTaxPercent,
                maxSellTax: config_1.default.safety.maxSellTaxPercent,
                minLiquidityLockDays: config_1.default.safety.minLiquidityLockDays,
                honeypotCheck: config_1.default.safety.honeypotCheckEnabled,
                rugPullCheck: config_1.default.safety.rugPullProtectionEnabled,
                metadataCheck: true,
                blacklistedTokens: [],
                blacklistedCreators: [],
            },
            notifications: {
                telegram: false,
                discord: false,
                email: false,
            },
        };
    }
}
exports.SupabaseManager = SupabaseManager;
exports.default = SupabaseManager;
//# sourceMappingURL=supabase.js.map