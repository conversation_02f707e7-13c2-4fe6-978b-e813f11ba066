"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const web3_js_1 = require("@solana/web3.js");
const wallet_1 = require("./utils/wallet");
const autoSniper_1 = require("./engine/autoSniper");
const userManager_1 = require("./utils/userManager");
const logger_1 = require("./utils/logger");
const config_1 = __importDefault(require("./config"));
const app = (0, express_1.default)();
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: config_1.default.api.corsOrigin,
    credentials: true,
}));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true }));
app.use((req, _res, next) => {
    logger_1.logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        body: req.method === 'POST' ? req.body : undefined,
    });
    next();
});
app.get('/health', (_req, res) => {
    res.json({
        success: true,
        message: '🎯 Sol Bullet Automated Sniper Bot is running',
        timestamp: new Date(),
        version: '1.0.0',
        sniper: autoSniper_1.autoSniperEngine.getStatus(),
    });
});
app.get('/balance/:wallet', async (req, res) => {
    try {
        const { wallet } = req.params;
        if (!(0, wallet_1.isValidPublicKey)(wallet)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid wallet address',
            });
        }
        const publicKey = new web3_js_1.PublicKey(wallet);
        const [solBalance, tokenBalances] = await Promise.all([
            (0, wallet_1.getSolBalance)(publicKey),
            (0, wallet_1.getTokenBalances)(publicKey),
        ]);
        const response = {
            success: true,
            data: {
                wallet,
                balances: {
                    sol: solBalance,
                    tokens: tokenBalances,
                },
            },
            timestamp: new Date(),
        };
        return res.json(response);
    }
    catch (error) {
        (0, logger_1.logError)(error, { context: 'getBalance', wallet: req.params.wallet });
        return res.status(500).json({
            success: false,
            error: 'Failed to fetch balance',
            message: error.message,
        });
    }
});
app.post('/sniper/start', async (req, res) => {
    try {
        const { userId } = req.body;
        if (!userId) {
            return res.status(400).json({
                success: false,
                error: 'User ID is required',
            });
        }
        await userManager_1.userManager.enableSniperForUser(userId);
        return res.json({
            success: true,
            message: '🎯 Automated sniper enabled for user',
            data: { userId },
            timestamp: new Date(),
        });
    }
    catch (error) {
        (0, logger_1.logError)(error, { context: 'startSniperEndpoint', body: req.body });
        return res.status(500).json({
            success: false,
            error: 'Failed to start sniper',
            message: error.message,
        });
    }
});
app.post('/sniper/stop', async (req, res) => {
    try {
        const { userId } = req.body;
        if (!userId) {
            return res.status(400).json({
                success: false,
                error: 'User ID is required',
            });
        }
        await userManager_1.userManager.disableSniperForUser(userId);
        return res.json({
            success: true,
            message: '🛑 Automated sniper disabled for user',
            data: { userId },
            timestamp: new Date(),
        });
    }
    catch (error) {
        (0, logger_1.logError)(error, { context: 'stopSniperEndpoint', body: req.body });
        return res.status(500).json({
            success: false,
            error: 'Failed to stop sniper',
            message: error.message,
        });
    }
});
app.post('/user/wallet', async (req, res) => {
    try {
        const { userId, privateKey } = req.body;
        if (!userId) {
            return res.status(400).json({
                success: false,
                error: 'User ID is required',
            });
        }
        const walletData = await userManager_1.userManager.addWalletToUser(userId, privateKey);
        return res.json({
            success: true,
            message: '💰 Wallet added to user for automated trading',
            data: {
                walletId: walletData.id,
                publicKey: walletData.publicKey,
            },
            timestamp: new Date(),
        });
    }
    catch (error) {
        (0, logger_1.logError)(error, { context: 'addWalletEndpoint', body: req.body });
        return res.status(500).json({
            success: false,
            error: 'Failed to add wallet',
            message: error.message,
        });
    }
});
app.post('/webhook/helius', async (_req, res) => {
    try {
        res.json({
            success: true,
            message: 'Webhook received - automated processing active'
        });
    }
    catch (error) {
        (0, logger_1.logError)(error, { context: 'heliusWebhook' });
        res.status(500).json({ success: false, error: 'Webhook processing failed' });
    }
});
app.get('/status', (_req, res) => {
    const sniperStatus = autoSniper_1.autoSniperEngine.getStatus();
    res.json({
        success: true,
        message: '🎯 Automated Sniper Status',
        data: {
            ...sniperStatus,
            performance: {
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage(),
            },
            jupiterIntegration: {
                apiUrl: config_1.default.jupiter.apiUrl,
                swapApiUrl: config_1.default.jupiter.swapApiUrl,
                enabled: true,
            },
        },
        timestamp: new Date(),
    });
});
app.use((error, req, res, _next) => {
    (0, logger_1.logError)(error, {
        context: 'expressErrorHandler',
        method: req.method,
        path: req.path,
        body: req.body,
    });
    res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: config_1.default.development.debugMode ? error.message : 'Something went wrong',
    });
});
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        message: `${req.method} ${req.originalUrl} is not a valid endpoint`,
    });
});
process.on('SIGTERM', async () => {
    logger_1.systemLogger.shutdown({ reason: 'SIGTERM' });
    await autoSniper_1.autoSniperEngine.stop();
    process.exit(0);
});
process.on('SIGINT', async () => {
    logger_1.systemLogger.shutdown({ reason: 'SIGINT' });
    await autoSniper_1.autoSniperEngine.stop();
    process.exit(0);
});
async function startServer() {
    try {
        logger_1.logger.info('🎯 STARTING SOL BULLET AUTOMATED SNIPER BOT');
        await autoSniper_1.autoSniperEngine.start();
        app.listen(config_1.default.api.port, () => {
            logger_1.systemLogger.startup({
                port: config_1.default.api.port,
                nodeEnv: config_1.default.api.nodeEnv,
                pid: process.pid,
                sniperMode: 'AUTOMATED',
            });
            logger_1.logger.info(`🎯 Sol Bullet AUTOMATED Sniper Bot running on port ${config_1.default.api.port}`);
            logger_1.logger.info(`🚀 AUTOMATED MODE: Bot will automatically detect and trade tokens`);
            logger_1.logger.info(`📊 Jupiter Integration: ${config_1.default.jupiter.apiUrl}`);
            logger_1.logger.info(`💰 Auto-Buy Amount: ${config_1.default.trading.defaultBuyAmountSol} SOL`);
            logger_1.logger.info(`📈 Take Profit: ${config_1.default.profitLoss.takeProfitPercent}%`);
            logger_1.logger.info(`📉 Stop Loss: ${config_1.default.profitLoss.stopLossPercent}%`);
            logger_1.logger.info(`Environment: ${config_1.default.api.nodeEnv}`);
            logger_1.logger.info(`Debug mode: ${config_1.default.development.debugMode}`);
            logger_1.logger.info(`Dry run mode: ${config_1.default.development.dryRunMode}`);
            console.log('\n🎯 SOL BULLET AUTOMATED SNIPER BOT READY!');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log('🔍 MONITORING: Raydium pools for new token launches');
            console.log('⚡ AUTO-BUY: Tokens that pass safety filters');
            console.log('📈 AUTO-SELL: When profit/loss targets are hit');
            console.log('🔄 JUPITER: All trades executed via Jupiter for best prices');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
        });
    }
    catch (error) {
        (0, logger_1.logError)(error, { context: 'startServer' });
        process.exit(1);
    }
}
startServer().catch((error) => {
    (0, logger_1.logError)(error, { context: 'serverStartup' });
    process.exit(1);
});
exports.default = app;
//# sourceMappingURL=server.js.map