"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sniperBot = exports.SniperBot = void 0;
const events_1 = require("events");
const web3_js_1 = require("@solana/web3.js");
const node_cron_1 = __importDefault(require("node-cron"));
const raydium_1 = __importDefault(require("./listeners/raydium"));
const tokenChecks_1 = __importDefault(require("./filters/tokenChecks"));
const buy_1 = __importDefault(require("./actions/buy"));
const sell_1 = __importDefault(require("./actions/sell"));
const supabase_1 = __importDefault(require("./database/supabase"));
const wallet_1 = require("./utils/wallet");
const userManager_1 = require("./utils/userManager");
const notifications_1 = require("./utils/notifications");
const logger_1 = require("./utils/logger");
const config_1 = __importDefault(require("./config"));
class SniperBot extends events_1.EventEmitter {
    poolMonitor;
    tokenFilter;
    buyExecutor;
    sellExecutor;
    database;
    activePositions = new Map();
    priceAlerts = new Map();
    isRunning = false;
    constructor() {
        super();
        this.poolMonitor = new raydium_1.default();
        this.tokenFilter = new tokenChecks_1.default();
        this.buyExecutor = new buy_1.default();
        this.sellExecutor = new sell_1.default();
        this.database = new supabase_1.default();
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        this.poolMonitor.on('tokenDetected', this.handleTokenDetected.bind(this));
        this.setupPriceMonitoring();
    }
    async start() {
        if (this.isRunning) {
            logger_1.logger.warn('Sniper bot already running');
            return;
        }
        try {
            this.isRunning = true;
            await this.poolMonitor.startMonitoring();
            await this.loadActivePositions();
            await this.loadPriceAlerts();
            logger_1.logger.info('🎯 Sniper bot started successfully');
            await notifications_1.notificationManager.sendNotification({
                type: 'alert',
                title: 'Sniper Bot Started',
                message: 'Sol Bullet sniper bot is now monitoring for new tokens',
                timestamp: new Date(),
            });
        }
        catch (error) {
            this.isRunning = false;
            (0, logger_1.logError)(error, { context: 'sniperStart' });
            throw error;
        }
    }
    async stop() {
        this.isRunning = false;
        await this.poolMonitor.stopMonitoring();
        logger_1.logger.info('🛑 Sniper bot stopped');
        await notifications_1.notificationManager.sendNotification({
            type: 'alert',
            title: 'Sniper Bot Stopped',
            message: 'Sol Bullet sniper bot has been stopped',
            timestamp: new Date(),
        });
    }
    async handleTokenDetected(event) {
        try {
            logger_1.logger.info('🔍 New token detected, running filters...', {
                tokenAddress: event.tokenAddress,
                poolId: event.poolId,
                liquidity: event.liquidity,
            });
            const liquidityPool = {
                poolId: event.poolId,
                baseMint: event.tokenAddress,
                quoteMint: 'So11111111111111111111111111111111111111112',
                baseReserve: new (await Promise.resolve().then(() => __importStar(require('decimal.js')))).Decimal(0),
                quoteReserve: new (await Promise.resolve().then(() => __importStar(require('decimal.js')))).Decimal(event.liquidity),
                lpSupply: new (await Promise.resolve().then(() => __importStar(require('decimal.js')))).Decimal(0),
                price: new (await Promise.resolve().then(() => __importStar(require('decimal.js')))).Decimal(0),
                liquidity: {
                    sol: event.liquidity,
                    usd: event.liquidity * 100,
                },
                volume24h: 0,
                createdAt: new Date(),
                isActive: true,
            };
            const filterResult = await this.tokenFilter.filterToken(event.tokenAddress, liquidityPool, event.metadata);
            if (!filterResult.passed) {
                logger_1.logger.info('❌ Token filtered out', {
                    tokenAddress: event.tokenAddress,
                    reason: filterResult.reason,
                    score: filterResult.score,
                });
                logger_1.tradeLogger.filtered({
                    tokenAddress: event.tokenAddress,
                    reason: filterResult.reason,
                    score: filterResult.score,
                    checks: filterResult.checks,
                });
                await notifications_1.notificationManager.notifyTokenDetected({
                    tokenAddress: event.tokenAddress,
                    poolId: event.poolId,
                    liquidity: event.liquidity,
                    action: 'skipped',
                    reason: filterResult.reason,
                });
                return;
            }
            logger_1.logger.info('✅ Token passed all filters, executing buys...', {
                tokenAddress: event.tokenAddress,
                score: filterResult.score,
            });
            await this.executeBuysForAllUsers(event.tokenAddress);
            await notifications_1.notificationManager.notifyTokenDetected({
                tokenAddress: event.tokenAddress,
                poolId: event.poolId,
                liquidity: event.liquidity,
                action: 'buying',
            });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'handleTokenDetected', event });
        }
    }
    async executeBuysForAllUsers(tokenAddress) {
        try {
            logger_1.logger.info('🚀 EXECUTING AUTOMATIC BUYS FOR ALL USERS', { tokenAddress });
            const activeUsers = await this.getActiveUsers();
            const buyPromises = activeUsers.map(async (user) => {
                try {
                    const wallets = await this.database.getUserWallets(user.id);
                    for (const walletData of wallets) {
                        try {
                            const userWallet = await (0, wallet_1.getUserWallet)(walletData);
                            if (userWallet.balance.sol < user.settings.trading.buyAmount + 0.01) {
                                logger_1.logger.warn('Insufficient balance for auto-buy', {
                                    userId: user.id,
                                    walletAddress: userWallet.publicKey.toString(),
                                    balance: userWallet.balance.sol,
                                    required: user.settings.trading.buyAmount,
                                });
                                continue;
                            }
                            logger_1.logger.info('🎯 EXECUTING AUTO-BUY', {
                                userId: user.id,
                                walletAddress: userWallet.publicKey.toString(),
                                tokenAddress,
                                amount: user.settings.trading.buyAmount,
                            });
                            const result = await this.buyExecutor.quickBuy(userWallet, tokenAddress, user.settings.trading.buyAmount);
                            if (result.success) {
                                logger_1.logger.info('✅ AUTO-BUY SUCCESSFUL', {
                                    userId: user.id,
                                    tokenAddress,
                                    signature: result.signature,
                                    amount: result.amount,
                                    price: result.price,
                                });
                                await this.database.saveTransaction({
                                    userId: user.id,
                                    walletAddress: userWallet.publicKey.toString(),
                                    type: 'buy',
                                    tokenAddress,
                                    amount: result.amount || 0,
                                    price: result.price || 0,
                                    slippage: result.slippage || 0,
                                    signature: result.signature || '',
                                    status: 'confirmed',
                                });
                                await this.createPosition(user.id, userWallet, tokenAddress, result);
                                await this.setupPriceAlerts(user.id, tokenAddress, result.price || 0);
                                await notifications_1.notificationManager.notifyBuy({
                                    tokenAddress,
                                    amount: result.amount || 0,
                                    price: result.price || 0,
                                    signature: result.signature || '',
                                });
                            }
                            else {
                                logger_1.logger.error('❌ AUTO-BUY FAILED', {
                                    userId: user.id,
                                    tokenAddress,
                                    error: result.error,
                                });
                                await this.database.saveTransaction({
                                    userId: user.id,
                                    walletAddress: userWallet.publicKey.toString(),
                                    type: 'buy',
                                    tokenAddress,
                                    amount: 0,
                                    price: 0,
                                    slippage: 0,
                                    signature: '',
                                    status: 'failed',
                                    error: result.error,
                                });
                            }
                        }
                        catch (walletError) {
                            (0, logger_1.logError)(walletError, {
                                context: 'executeBuyForUserWallet',
                                userId: user.id,
                                walletId: walletData.id
                            });
                        }
                    }
                }
                catch (userError) {
                    (0, logger_1.logError)(userError, { context: 'executeBuyForUser', userId: user.id });
                }
            });
            const chunks = this.chunkArray(buyPromises, config_1.default.limits.maxConcurrentTransactions);
            for (const chunk of chunks) {
                await Promise.allSettled(chunk);
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            logger_1.logger.info('🎯 AUTO-BUY EXECUTION COMPLETED', {
                tokenAddress,
                totalUsers: activeUsers.length,
                executionTime: Date.now() - Date.now(),
            });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'executeBuysForAllUsers', tokenAddress });
        }
    }
    async createPosition(userId, wallet, tokenAddress, buyResult) {
        try {
            const position = {
                userId,
                walletAddress: wallet.publicKey.toString(),
                tokenAddress,
                amount: buyResult.amount,
                averageBuyPrice: buyResult.price,
                currentPrice: buyResult.price,
                pnl: 0,
                pnlPercent: 0,
                status: 'active',
            };
            const savedPosition = await this.database.savePosition(position);
            this.activePositions.set(`${userId}-${tokenAddress}`, savedPosition);
            logger_1.logger.info('Position created', {
                userId,
                tokenAddress,
                amount: buyResult.amount,
                price: buyResult.price,
            });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'createPosition', userId, tokenAddress });
        }
    }
    async setupPriceAlerts(userId, tokenAddress, buyPrice) {
        try {
            const alerts = [];
            if (config_1.default.profitLoss.takeProfitPercent > 0) {
                const takeProfitPrice = buyPrice * (1 + config_1.default.profitLoss.takeProfitPercent / 100);
                alerts.push({
                    id: `${userId}-${tokenAddress}-tp`,
                    userId,
                    tokenAddress,
                    type: 'take_profit',
                    targetPrice: takeProfitPrice,
                    currentPrice: buyPrice,
                    percentage: config_1.default.profitLoss.takeProfitPercent,
                    isActive: true,
                });
            }
            if (config_1.default.profitLoss.stopLossPercent > 0) {
                const stopLossPrice = buyPrice * (1 - config_1.default.profitLoss.stopLossPercent / 100);
                alerts.push({
                    id: `${userId}-${tokenAddress}-sl`,
                    userId,
                    tokenAddress,
                    type: 'stop_loss',
                    targetPrice: stopLossPrice,
                    currentPrice: buyPrice,
                    percentage: -config_1.default.profitLoss.stopLossPercent,
                    isActive: true,
                });
            }
            this.priceAlerts.set(`${userId}-${tokenAddress}`, alerts);
            logger_1.logger.info('Price alerts setup', {
                userId,
                tokenAddress,
                alertCount: alerts.length,
            });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'setupPriceAlerts', userId, tokenAddress });
        }
    }
    setupPriceMonitoring() {
        node_cron_1.default.schedule('*/2 * * * * *', async () => {
            if (!this.isRunning)
                return;
            try {
                await this.checkPriceAlerts();
            }
            catch (error) {
                (0, logger_1.logError)(error, { context: 'priceMonitoring' });
            }
        });
    }
    async checkPriceAlerts() {
        for (const [key, alerts] of this.priceAlerts.entries()) {
            const [userId, tokenAddress] = key.split('-');
            try {
                const currentPrice = await this.getCurrentTokenPrice(tokenAddress);
                if (!currentPrice)
                    continue;
                for (const alert of alerts) {
                    if (!alert.isActive)
                        continue;
                    const shouldTrigger = this.shouldTriggerAlert(alert, currentPrice);
                    if (shouldTrigger) {
                        await this.triggerPriceAlert(alert, currentPrice);
                    }
                }
            }
            catch (error) {
                (0, logger_1.logError)(error, { context: 'checkPriceAlert', userId, tokenAddress });
            }
        }
    }
    async getCurrentTokenPrice(tokenAddress) {
        try {
            const response = await fetch(`${config_1.default.jupiter.apiUrl}/price?ids=${tokenAddress}&vsToken=So11111111111111111111111111111111111111112`);
            if (!response.ok) {
                throw new Error(`Price API error: ${response.status}`);
            }
            const data = await response.json();
            const priceData = data.data?.[tokenAddress];
            if (!priceData) {
                return this.getPriceViaQuote(tokenAddress);
            }
            return parseFloat(priceData.price);
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'getCurrentTokenPrice', tokenAddress });
            return null;
        }
    }
    async getPriceViaQuote(tokenAddress) {
        try {
            const response = await fetch(`${config_1.default.jupiter.apiUrl}/quote?` + new URLSearchParams({
                inputMint: tokenAddress,
                outputMint: 'So11111111111111111111111111111111111111112',
                amount: '1000000',
                slippageBps: '100',
            }));
            if (!response.ok)
                return null;
            const quote = await response.json();
            const inputAmount = parseFloat(quote.inAmount);
            const outputAmount = parseFloat(quote.outAmount);
            if (inputAmount > 0 && outputAmount > 0) {
                return outputAmount / inputAmount;
            }
            return null;
        }
        catch (error) {
            return null;
        }
    }
    shouldTriggerAlert(alert, currentPrice) {
        switch (alert.type) {
            case 'take_profit':
                return currentPrice >= alert.targetPrice;
            case 'stop_loss':
                return currentPrice <= alert.targetPrice;
            case 'trailing_stop':
                return false;
            default:
                return false;
        }
    }
    async triggerPriceAlert(alert, currentPrice) {
        try {
            alert.isActive = false;
            alert.triggeredAt = new Date();
            logger_1.logger.info('🚨 PRICE ALERT TRIGGERED - EXECUTING AUTO-SELL', {
                userId: alert.userId,
                tokenAddress: alert.tokenAddress,
                type: alert.type,
                currentPrice,
                targetPrice: alert.targetPrice,
            });
            if (config_1.default.profitLoss.autoSellEnabled) {
                await this.executeAutoSell(alert, currentPrice);
            }
            await notifications_1.notificationManager.notifyPriceAlert({
                tokenAddress: alert.tokenAddress,
                type: alert.type,
                currentPrice,
                targetPrice: alert.targetPrice,
                percentage: ((currentPrice - alert.targetPrice) / alert.targetPrice) * 100,
            });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'triggerPriceAlert', alert });
        }
    }
    async executeAutoSell(alert, currentPrice) {
        try {
            const user = await this.database.getUser(alert.userId);
            if (!user) {
                logger_1.logger.error('User not found for auto-sell', { userId: alert.userId });
                return;
            }
            const wallets = await this.database.getUserWallets(alert.userId);
            const walletData = wallets.find(w => this.activePositions.has(`${alert.userId}-${alert.tokenAddress}`));
            if (!walletData) {
                logger_1.logger.error('Wallet not found for auto-sell', { userId: alert.userId });
                return;
            }
            const userWallet = await (0, wallet_1.getUserWallet)(walletData);
            const tokenBalance = await (0, wallet_1.getTokenBalance)(userWallet.publicKey, new web3_js_1.PublicKey(alert.tokenAddress));
            if (tokenBalance <= 0) {
                logger_1.logger.warn('No token balance for auto-sell', {
                    userId: alert.userId,
                    tokenAddress: alert.tokenAddress,
                });
                return;
            }
            if (config_1.default.profitLoss.autoSellDelayMs > 0) {
                logger_1.logger.info(`⏳ Auto-sell delay: ${config_1.default.profitLoss.autoSellDelayMs}ms`);
                await new Promise(resolve => setTimeout(resolve, config_1.default.profitLoss.autoSellDelayMs));
            }
            logger_1.logger.info('🔴 EXECUTING AUTO-SELL', {
                userId: alert.userId,
                tokenAddress: alert.tokenAddress,
                balance: tokenBalance,
                triggerType: alert.type,
                currentPrice,
            });
            const sellResult = await this.sellExecutor.quickSell(userWallet, alert.tokenAddress, 100);
            if (sellResult.success) {
                const position = this.activePositions.get(`${alert.userId}-${alert.tokenAddress}`);
                const buyPrice = position?.averageBuyPrice || 0;
                const pnlPercent = buyPrice > 0 ? ((currentPrice - buyPrice) / buyPrice) * 100 : 0;
                logger_1.logger.info('✅ AUTO-SELL SUCCESSFUL', {
                    userId: alert.userId,
                    tokenAddress: alert.tokenAddress,
                    signature: sellResult.signature,
                    amount: sellResult.amount,
                    price: sellResult.price,
                    pnl: pnlPercent,
                });
                await this.database.saveTransaction({
                    userId: alert.userId,
                    walletAddress: userWallet.publicKey.toString(),
                    type: 'sell',
                    tokenAddress: alert.tokenAddress,
                    amount: sellResult.amount || 0,
                    price: sellResult.price || 0,
                    slippage: sellResult.slippage || 0,
                    signature: sellResult.signature || '',
                    status: 'confirmed',
                });
                if (position) {
                    position.status = 'closed';
                    position.closedAt = new Date();
                    position.currentPrice = currentPrice;
                    position.pnl = (currentPrice - position.averageBuyPrice) * position.amount;
                    position.pnlPercent = pnlPercent;
                    this.activePositions.delete(`${alert.userId}-${alert.tokenAddress}`);
                }
                await notifications_1.notificationManager.notifySell({
                    tokenAddress: alert.tokenAddress,
                    amount: sellResult.amount || 0,
                    price: sellResult.price || 0,
                    pnl: pnlPercent,
                    signature: sellResult.signature || '',
                });
                this.priceAlerts.delete(`${alert.userId}-${alert.tokenAddress}`);
            }
            else {
                logger_1.logger.error('❌ AUTO-SELL FAILED', {
                    userId: alert.userId,
                    tokenAddress: alert.tokenAddress,
                    error: sellResult.error,
                });
                await this.database.saveTransaction({
                    userId: alert.userId,
                    walletAddress: userWallet.publicKey.toString(),
                    type: 'sell',
                    tokenAddress: alert.tokenAddress,
                    amount: 0,
                    price: 0,
                    slippage: 0,
                    signature: '',
                    status: 'failed',
                    error: sellResult.error,
                });
                alert.isActive = true;
                alert.triggeredAt = undefined;
            }
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'executeAutoSell', alert });
        }
    }
    async loadActivePositions() {
        try {
            logger_1.logger.info('Active positions loaded', { count: this.activePositions.size });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'loadActivePositions' });
        }
    }
    async loadPriceAlerts() {
        try {
            logger_1.logger.info('Price alerts loaded', { count: this.priceAlerts.size });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'loadPriceAlerts' });
        }
    }
    async getActiveUsers() {
        try {
            return await userManager_1.userManager.getActiveSniperUsers();
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'getActiveUsers' });
            return [];
        }
    }
    chunkArray(array, chunkSize) {
        const chunks = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }
    async addUserWallet(userId, privateKeyHex) {
        try {
            logger_1.logger.info('Adding user wallet', { userId });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'addUserWallet', userId });
            throw error;
        }
    }
    async removeUserWallet(userId, walletAddress) {
        try {
            logger_1.logger.info('Removing user wallet', { userId, walletAddress });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'removeUserWallet', userId, walletAddress });
            throw error;
        }
    }
    getStatus() {
        return {
            isRunning: this.isRunning,
            activePositions: this.activePositions.size,
            priceAlerts: Array.from(this.priceAlerts.values()).flat().length,
            config: {
                minLiquidity: config_1.default.trading.minLiquiditySol,
                autoSellEnabled: config_1.default.profitLoss.autoSellEnabled,
                takeProfitPercent: config_1.default.profitLoss.takeProfitPercent,
                stopLossPercent: config_1.default.profitLoss.stopLossPercent,
            },
        };
    }
}
exports.SniperBot = SniperBot;
exports.sniperBot = new SniperBot();
exports.default = exports.sniperBot;
//# sourceMappingURL=sniper.js.map