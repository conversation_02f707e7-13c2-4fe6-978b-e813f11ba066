"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.autoSniperEngine = exports.AutoSniperEngine = void 0;
const events_1 = require("events");
const web3_js_1 = require("@solana/web3.js");
const node_cron_1 = __importDefault(require("node-cron"));
const raydium_1 = __importDefault(require("../listeners/raydium"));
const tokenChecks_1 = __importDefault(require("../filters/tokenChecks"));
const buy_1 = __importDefault(require("../actions/buy"));
const sell_1 = __importDefault(require("../actions/sell"));
const supabase_1 = __importDefault(require("../database/supabase"));
const wallet_1 = require("../utils/wallet");
const notifications_1 = require("../utils/notifications");
const logger_1 = require("../utils/logger");
const config_1 = __importDefault(require("../config"));
class AutoSniperEngine extends events_1.EventEmitter {
    poolMonitor;
    tokenFilter;
    buyExecutor;
    sellExecutor;
    database;
    activePositions = new Map();
    priceMonitoringTokens = new Set();
    isRunning = false;
    sniperUsers = [];
    constructor() {
        super();
        this.poolMonitor = new raydium_1.default();
        this.tokenFilter = new tokenChecks_1.default();
        this.buyExecutor = new buy_1.default();
        this.sellExecutor = new sell_1.default();
        this.database = new supabase_1.default();
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        this.poolMonitor.on('tokenDetected', this.handleNewTokenDetected.bind(this));
        this.setupContinuousPriceMonitoring();
    }
    async start() {
        if (this.isRunning) {
            logger_1.logger.warn('Auto-sniper already running');
            return;
        }
        try {
            this.isRunning = true;
            logger_1.logger.info('🎯 STARTING AUTOMATED SNIPER ENGINE');
            await this.loadSniperUsers();
            await this.poolMonitor.startMonitoring();
            await this.loadActivePositions();
            logger_1.logger.info('🚀 AUTOMATED SNIPER ENGINE STARTED', {
                activeUsers: this.sniperUsers.length,
                monitoringPositions: this.activePositions.size,
            });
            await notifications_1.notificationManager.sendNotification({
                type: 'alert',
                title: '🎯 Auto-Sniper Started',
                message: `Monitoring ${this.sniperUsers.length} users for new token opportunities`,
                data: {
                    'Active Users': this.sniperUsers.length,
                    'Min Liquidity': `${config_1.default.trading.minLiquiditySol} SOL`,
                    'Buy Amount': `${config_1.default.trading.defaultBuyAmountSol} SOL`,
                    'Take Profit': `${config_1.default.profitLoss.takeProfitPercent}%`,
                    'Stop Loss': `${config_1.default.profitLoss.stopLossPercent}%`,
                },
                timestamp: new Date(),
            });
        }
        catch (error) {
            this.isRunning = false;
            (0, logger_1.logError)(error, { context: 'autoSniperStart' });
            throw error;
        }
    }
    async stop() {
        this.isRunning = false;
        await this.poolMonitor.stopMonitoring();
        logger_1.logger.info('🛑 AUTOMATED SNIPER ENGINE STOPPED');
        await notifications_1.notificationManager.sendNotification({
            type: 'alert',
            title: '🛑 Auto-Sniper Stopped',
            message: 'Automated sniper engine has been stopped',
            timestamp: new Date(),
        });
    }
    async handleNewTokenDetected(event) {
        try {
            logger_1.logger.info('🔍 NEW TOKEN DETECTED - STARTING AUTOMATED ANALYSIS', {
                tokenAddress: event.tokenAddress,
                poolId: event.poolId,
                liquidity: event.liquidity,
            });
            const filterResult = await this.runTokenFilters(event);
            if (!filterResult.passed) {
                logger_1.logger.info('❌ TOKEN REJECTED BY FILTERS', {
                    tokenAddress: event.tokenAddress,
                    reason: filterResult.reason,
                    score: filterResult.score,
                });
                await notifications_1.notificationManager.notifyTokenDetected({
                    tokenAddress: event.tokenAddress,
                    poolId: event.poolId,
                    liquidity: event.liquidity,
                    action: 'skipped',
                    reason: filterResult.reason,
                });
                return;
            }
            logger_1.logger.info('✅ TOKEN PASSED ALL FILTERS - EXECUTING AUTOMATED BUYS', {
                tokenAddress: event.tokenAddress,
                score: filterResult.score,
                liquidity: event.liquidity,
            });
            await this.executeAutomaticBuys(event.tokenAddress);
            this.startPriceMonitoring(event.tokenAddress);
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'handleNewTokenDetected', event });
        }
    }
    async executeAutomaticBuys(tokenAddress) {
        try {
            logger_1.logger.info('🚀 EXECUTING AUTOMATIC BUYS FOR ALL SNIPER USERS', {
                tokenAddress,
                userCount: this.sniperUsers.length
            });
            const buyPromises = this.sniperUsers.map(async (user) => {
                try {
                    const wallets = await this.database.getUserWallets(user.id);
                    for (const walletData of wallets) {
                        try {
                            const userWallet = await (0, wallet_1.getUserWallet)(walletData);
                            const requiredAmount = user.settings.trading.buyAmount + 0.01;
                            if (userWallet.balance.sol < requiredAmount) {
                                logger_1.logger.warn('⚠️ INSUFFICIENT BALANCE FOR AUTO-BUY', {
                                    userId: user.id,
                                    walletAddress: userWallet.publicKey.toString(),
                                    balance: userWallet.balance.sol,
                                    required: requiredAmount,
                                });
                                continue;
                            }
                            logger_1.logger.info('💰 EXECUTING AUTO-BUY VIA JUPITER', {
                                userId: user.id,
                                walletAddress: userWallet.publicKey.toString(),
                                tokenAddress,
                                amount: user.settings.trading.buyAmount,
                            });
                            const buyResult = await this.buyExecutor.quickBuy(userWallet, tokenAddress, user.settings.trading.buyAmount);
                            if (buyResult.success) {
                                logger_1.logger.info('✅ AUTO-BUY SUCCESSFUL VIA JUPITER', {
                                    userId: user.id,
                                    tokenAddress,
                                    signature: buyResult.signature,
                                    amount: buyResult.amount,
                                    price: buyResult.price,
                                    slippage: buyResult.slippage,
                                });
                                await this.database.saveTransaction({
                                    userId: user.id,
                                    walletAddress: userWallet.publicKey.toString(),
                                    type: 'buy',
                                    tokenAddress,
                                    amount: buyResult.amount || 0,
                                    price: buyResult.price || 0,
                                    slippage: buyResult.slippage || 0,
                                    signature: buyResult.signature || '',
                                    status: 'confirmed',
                                });
                                await this.createPositionForAutoSell(user.id, userWallet, tokenAddress, buyResult);
                                await this.setupAutoSellAlerts(user.id, tokenAddress, buyResult.price || 0, user.settings);
                            }
                            else {
                                logger_1.logger.error('❌ AUTO-BUY FAILED', {
                                    userId: user.id,
                                    tokenAddress,
                                    error: buyResult.error,
                                });
                                await this.database.saveTransaction({
                                    userId: user.id,
                                    walletAddress: userWallet.publicKey.toString(),
                                    type: 'buy',
                                    tokenAddress,
                                    amount: 0,
                                    price: 0,
                                    slippage: 0,
                                    signature: '',
                                    status: 'failed',
                                    error: buyResult.error || 'Unknown error',
                                });
                            }
                        }
                        catch (walletError) {
                            (0, logger_1.logError)(walletError, {
                                context: 'executeAutoBuyForWallet',
                                userId: user.id,
                                walletId: walletData.id
                            });
                        }
                    }
                }
                catch (userError) {
                    (0, logger_1.logError)(userError, { context: 'executeAutoBuyForUser', userId: user.id });
                }
            });
            const chunks = this.chunkArray(buyPromises, config_1.default.limits.maxConcurrentTransactions);
            for (const chunk of chunks) {
                await Promise.allSettled(chunk);
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            logger_1.logger.info('🎯 AUTOMATIC BUY EXECUTION COMPLETED', {
                tokenAddress,
                totalUsers: this.sniperUsers.length,
            });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'executeAutomaticBuys', tokenAddress });
        }
    }
    setupContinuousPriceMonitoring() {
        node_cron_1.default.schedule('*/2 * * * * *', async () => {
            if (!this.isRunning)
                return;
            try {
                await this.monitorPricesForAutoSell();
            }
            catch (error) {
                (0, logger_1.logError)(error, { context: 'continuousPriceMonitoring' });
            }
        });
        logger_1.logger.info('📊 CONTINUOUS PRICE MONITORING SETUP - CHECKING EVERY 2 SECONDS');
    }
    async monitorPricesForAutoSell() {
        if (this.priceMonitoringTokens.size === 0)
            return;
        const monitoringPromises = Array.from(this.priceMonitoringTokens).map(async (tokenAddress) => {
            try {
                const currentPrice = await this.getCurrentTokenPriceViaJupiter(tokenAddress);
                if (!currentPrice)
                    return;
                for (const [, position] of this.activePositions.entries()) {
                    if (position.tokenAddress === tokenAddress) {
                        await this.checkPositionForAutoSell(position, currentPrice);
                    }
                }
            }
            catch (error) {
                (0, logger_1.logError)(error, { context: 'monitorTokenPrice', tokenAddress });
            }
        });
        await Promise.allSettled(monitoringPromises);
    }
    async getCurrentTokenPriceViaJupiter(tokenAddress) {
        try {
            const priceResponse = await fetch(`${config_1.default.jupiter.apiUrl}/price?ids=${tokenAddress}&vsToken=So11111111111111111111111111111111111111112`);
            if (priceResponse.ok) {
                const priceData = await priceResponse.json();
                const price = priceData.data?.[tokenAddress]?.price;
                if (price)
                    return parseFloat(price);
            }
            const quoteResponse = await fetch(`${config_1.default.jupiter.apiUrl}/quote?` + new URLSearchParams({
                inputMint: tokenAddress,
                outputMint: 'So11111111111111111111111111111111111111112',
                amount: '1000000',
                slippageBps: '100',
            }));
            if (quoteResponse.ok) {
                const quote = await quoteResponse.json();
                const inputAmount = parseFloat(quote.inAmount);
                const outputAmount = parseFloat(quote.outAmount);
                if (inputAmount > 0 && outputAmount > 0) {
                    return outputAmount / inputAmount;
                }
            }
            return null;
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'getCurrentTokenPriceViaJupiter', tokenAddress });
            return null;
        }
    }
    async checkPositionForAutoSell(position, currentPrice) {
        try {
            const buyPrice = position.averageBuyPrice;
            const priceChangePercent = ((currentPrice - buyPrice) / buyPrice) * 100;
            position.currentPrice = currentPrice;
            position.pnl = (currentPrice - buyPrice) * position.amount;
            position.pnlPercent = priceChangePercent;
            const user = await this.database.getUser(position.userId);
            if (!user)
                return;
            const { takeProfitPercent, stopLossPercent } = user.settings.trading;
            if (priceChangePercent >= takeProfitPercent) {
                logger_1.logger.info('📈 TAKE PROFIT TRIGGERED - EXECUTING AUTO-SELL', {
                    userId: position.userId,
                    tokenAddress: position.tokenAddress,
                    buyPrice,
                    currentPrice,
                    profitPercent: priceChangePercent,
                    targetPercent: takeProfitPercent,
                });
                await this.executeAutoSellForPosition(position, 'take_profit', currentPrice);
                return;
            }
            if (priceChangePercent <= -stopLossPercent) {
                logger_1.logger.info('📉 STOP LOSS TRIGGERED - EXECUTING AUTO-SELL', {
                    userId: position.userId,
                    tokenAddress: position.tokenAddress,
                    buyPrice,
                    currentPrice,
                    lossPercent: priceChangePercent,
                    targetPercent: -stopLossPercent,
                });
                await this.executeAutoSellForPosition(position, 'stop_loss', currentPrice);
                return;
            }
            logger_1.logger.debug('📊 Price monitored', {
                tokenAddress: position.tokenAddress,
                buyPrice,
                currentPrice,
                changePercent: priceChangePercent,
            });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'checkPositionForAutoSell', position });
        }
    }
    async executeAutoSellForPosition(position, triggerType, currentPrice) {
        try {
            const walletData = await this.database.getWalletByPublicKey(position.walletAddress);
            if (!walletData) {
                logger_1.logger.error('Wallet not found for auto-sell', { position });
                return;
            }
            const userWallet = await (0, wallet_1.getUserWallet)(walletData);
            const tokenBalance = await (0, wallet_1.getTokenBalance)(userWallet.publicKey, new web3_js_1.PublicKey(position.tokenAddress));
            if (tokenBalance <= 0) {
                logger_1.logger.warn('No token balance for auto-sell', {
                    userId: position.userId,
                    tokenAddress: position.tokenAddress,
                });
                this.activePositions.delete(`${position.userId}-${position.tokenAddress}`);
                this.priceMonitoringTokens.delete(position.tokenAddress);
                return;
            }
            logger_1.logger.info('🔴 EXECUTING AUTO-SELL VIA JUPITER', {
                userId: position.userId,
                tokenAddress: position.tokenAddress,
                triggerType,
                balance: tokenBalance,
                currentPrice,
                buyPrice: position.averageBuyPrice,
            });
            const sellResult = await this.sellExecutor.quickSell(userWallet, position.tokenAddress, 100);
            if (sellResult.success) {
                const pnlPercent = ((currentPrice - position.averageBuyPrice) / position.averageBuyPrice) * 100;
                logger_1.logger.info('✅ AUTO-SELL SUCCESSFUL VIA JUPITER', {
                    userId: position.userId,
                    tokenAddress: position.tokenAddress,
                    signature: sellResult.signature,
                    amount: sellResult.amount,
                    price: sellResult.price,
                    pnl: pnlPercent,
                    triggerType,
                });
                await this.database.saveTransaction({
                    userId: position.userId,
                    walletAddress: userWallet.publicKey.toString(),
                    type: 'sell',
                    tokenAddress: position.tokenAddress,
                    amount: sellResult.amount || 0,
                    price: sellResult.price || 0,
                    slippage: sellResult.slippage || 0,
                    signature: sellResult.signature || '',
                    status: 'confirmed',
                });
                position.status = 'closed';
                position.closedAt = new Date();
                position.currentPrice = currentPrice;
                position.pnl = (currentPrice - position.averageBuyPrice) * position.amount;
                position.pnlPercent = pnlPercent;
                this.activePositions.delete(`${position.userId}-${position.tokenAddress}`);
                const hasOtherPositions = Array.from(this.activePositions.values())
                    .some(p => p.tokenAddress === position.tokenAddress);
                if (!hasOtherPositions) {
                    this.priceMonitoringTokens.delete(position.tokenAddress);
                }
                await notifications_1.notificationManager.notifySell({
                    tokenAddress: position.tokenAddress,
                    amount: sellResult.amount || 0,
                    price: sellResult.price || 0,
                    pnl: pnlPercent,
                    signature: sellResult.signature || '',
                });
            }
            else {
                logger_1.logger.error('❌ AUTO-SELL FAILED', {
                    userId: position.userId,
                    tokenAddress: position.tokenAddress,
                    error: sellResult.error,
                    triggerType,
                });
                await this.database.saveTransaction({
                    userId: position.userId,
                    walletAddress: userWallet.publicKey.toString(),
                    type: 'sell',
                    tokenAddress: position.tokenAddress,
                    amount: 0,
                    price: 0,
                    slippage: 0,
                    signature: '',
                    status: 'failed',
                    error: sellResult.error || 'Unknown error',
                });
            }
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'executeAutoSellForPosition', position, triggerType });
        }
    }
    async runTokenFilters(event) {
        const liquidityPool = {
            poolId: event.poolId,
            baseMint: event.tokenAddress,
            quoteMint: 'So11111111111111111111111111111111111111112',
            liquidity: { sol: event.liquidity, usd: event.liquidity * 100 },
        };
        return await this.tokenFilter.filterToken(event.tokenAddress, liquidityPool, event.metadata);
    }
    async createPositionForAutoSell(userId, wallet, tokenAddress, buyResult) {
        const position = {
            userId,
            walletAddress: wallet.publicKey.toString(),
            tokenAddress,
            amount: buyResult.amount,
            averageBuyPrice: buyResult.price,
            currentPrice: buyResult.price,
            pnl: 0,
            pnlPercent: 0,
            status: 'active',
        };
        const savedPosition = await this.database.savePosition(position);
        this.activePositions.set(`${userId}-${tokenAddress}`, savedPosition);
    }
    async setupAutoSellAlerts(userId, tokenAddress, buyPrice, userSettings) {
        logger_1.logger.info('🎯 AUTO-SELL MONITORING SETUP', {
            userId,
            tokenAddress,
            buyPrice,
            takeProfitTarget: userSettings.trading.takeProfitPercent,
            stopLossTarget: userSettings.trading.stopLossPercent,
        });
    }
    startPriceMonitoring(tokenAddress) {
        this.priceMonitoringTokens.add(tokenAddress);
        logger_1.logger.info('📊 STARTED PRICE MONITORING FOR AUTO-SELL', { tokenAddress });
    }
    async loadSniperUsers() {
        try {
            this.sniperUsers = [];
            logger_1.logger.info('Sniper users loaded', { count: this.sniperUsers.length });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'loadSniperUsers' });
        }
    }
    async loadActivePositions() {
        try {
            logger_1.logger.info('Active positions loaded', { count: this.activePositions.size });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'loadActivePositions' });
        }
    }
    chunkArray(array, chunkSize) {
        const chunks = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }
    getStatus() {
        return {
            isRunning: this.isRunning,
            activeUsers: this.sniperUsers.length,
            activePositions: this.activePositions.size,
            monitoringTokens: this.priceMonitoringTokens.size,
            config: {
                minLiquidity: config_1.default.trading.minLiquiditySol,
                buyAmount: config_1.default.trading.defaultBuyAmountSol,
                takeProfitPercent: config_1.default.profitLoss.takeProfitPercent,
                stopLossPercent: config_1.default.profitLoss.stopLossPercent,
                autoSellEnabled: config_1.default.profitLoss.autoSellEnabled,
            },
        };
    }
}
exports.AutoSniperEngine = AutoSniperEngine;
exports.autoSniperEngine = new AutoSniperEngine();
exports.default = exports.autoSniperEngine;
//# sourceMappingURL=autoSniper.js.map