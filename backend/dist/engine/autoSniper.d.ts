import { EventEmitter } from 'events';
export declare class AutoSniperEngine extends EventEmitter {
    private poolMonitor;
    private tokenFilter;
    private buyExecutor;
    private sellExecutor;
    private database;
    private activePositions;
    private priceMonitoringTokens;
    private isRunning;
    private sniperUsers;
    constructor();
    private setupEventHandlers;
    start(): Promise<void>;
    stop(): Promise<void>;
    private handleNewTokenDetected;
    private executeAutomaticBuys;
    private setupContinuousPriceMonitoring;
    private monitorPricesForAutoSell;
    private getCurrentTokenPriceViaJupiter;
    private checkPositionForAutoSell;
    private executeAutoSellForPosition;
    private runTokenFilters;
    private createPositionForAutoSell;
    private setupAutoSellAlerts;
    private startPriceMonitoring;
    private loadSniperUsers;
    private loadActivePositions;
    private chunkArray;
    getStatus(): any;
}
export declare const autoSniperEngine: AutoSniperEngine;
export default autoSniperEngine;
//# sourceMappingURL=autoSniper.d.ts.map