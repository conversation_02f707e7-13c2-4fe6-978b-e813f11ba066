import { TradeParams, TradeResult, UserWallet } from '../types';
export declare class SellExecutor {
    private connection;
    constructor();
    executeSell(wallet: UserWallet, params: TradeParams): Promise<TradeResult>;
    private validateSellParams;
    private calculateSellAmount;
    private getSwapQuote;
    private executeSwap;
    private getTokenInfo;
    quickSell(wallet: UserWallet, tokenAddress: string, percentage?: number): Promise<TradeResult>;
    emergencySell(wallet: UserWallet, tokenAddress: string, percentage?: number): Promise<TradeResult>;
}
export default SellExecutor;
//# sourceMappingURL=sell.d.ts.map