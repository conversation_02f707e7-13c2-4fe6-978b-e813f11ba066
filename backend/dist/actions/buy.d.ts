import { TradeParams, TradeResult, UserWallet } from '../types';
export declare class BuyExecutor {
    private connection;
    constructor();
    executeBuy(wallet: UserWallet, params: TradeParams): Promise<TradeResult>;
    private validateBuyParams;
    private getSwapQuote;
    private executeSwap;
    quickBuy(wallet: UserWallet, tokenAddress: string, customAmount?: number): Promise<TradeResult>;
}
export default BuyExecutor;
//# sourceMappingURL=buy.d.ts.map