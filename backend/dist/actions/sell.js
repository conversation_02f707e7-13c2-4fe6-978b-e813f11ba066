"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SellExecutor = void 0;
const web3_js_1 = require("@solana/web3.js");
const axios_1 = __importDefault(require("axios"));
const wallet_1 = require("../utils/wallet");
const notifications_1 = require("../utils/notifications");
const logger_1 = require("../utils/logger");
const config_1 = __importDefault(require("../config"));
class SellExecutor {
    connection;
    constructor() {
        this.connection = new web3_js_1.Connection(config_1.default.solana.rpcEndpoint, 'confirmed');
    }
    async executeSell(wallet, params) {
        const startTime = Date.now();
        try {
            this.validateSellParams(params);
            const tokenMint = new web3_js_1.PublicKey(params.tokenAddress);
            const tokenBalance = await (0, wallet_1.getTokenBalance)(wallet.publicKey, tokenMint);
            if (tokenBalance <= 0) {
                throw new Error(`No token balance found for ${params.tokenAddress}`);
            }
            const sellAmount = this.calculateSellAmount(tokenBalance, params);
            if (sellAmount <= 0) {
                throw new Error('Calculated sell amount is 0');
            }
            const quote = await this.getSwapQuote(params, sellAmount);
            if (!quote) {
                throw new Error('Failed to get swap quote');
            }
            const priceImpact = parseFloat(quote.priceImpactPct);
            const maxSlippage = params.maxSlippage || config_1.default.trading.maxSlippagePercent;
            if (priceImpact > maxSlippage) {
                throw new Error(`Price impact too high: ${priceImpact}% > ${maxSlippage}%`);
            }
            const signature = config_1.default.development.dryRunMode
                ? 'DRY_RUN_SIGNATURE'
                : await this.executeSwap(wallet, quote, params);
            const result = {
                success: true,
                signature,
                amount: sellAmount,
                price: parseFloat(quote.outAmount) / parseFloat(quote.inAmount),
                slippage: priceImpact,
                fee: params.priorityFee || config_1.default.trading.priorityFeeSol,
                timestamp: new Date(),
            };
            logger_1.tradeLogger.sell({
                ...result,
                tokenAddress: params.tokenAddress,
                walletAddress: params.walletAddress,
                executionTime: Date.now() - startTime,
            });
            await notifications_1.notificationManager.notifySell({
                tokenAddress: params.tokenAddress || 'Unknown',
                amount: result.amount || 0,
                price: result.price || 0,
                pnl: 0,
                signature: signature,
            });
            return result;
        }
        catch (error) {
            const errorResult = {
                success: false,
                error: error.message,
                timestamp: new Date(),
            };
            logger_1.tradeLogger.error({
                ...errorResult,
                tokenAddress: params.tokenAddress,
                walletAddress: params.walletAddress,
                executionTime: Date.now() - startTime,
            });
            (0, logger_1.logError)(error, { context: 'executeSell', params });
            return errorResult;
        }
    }
    validateSellParams(params) {
        if (!params.walletAddress) {
            throw new Error('Wallet address is required');
        }
        if (!params.tokenAddress) {
            throw new Error('Token address is required');
        }
        const percentage = params.percentage || 100;
        if (percentage <= 0 || percentage > 100) {
            throw new Error('Sell percentage must be between 1 and 100');
        }
        const slippage = params.slippage || config_1.default.trading.defaultSlippagePercent;
        if (slippage < 0 || slippage > 100) {
            throw new Error('Slippage must be between 0 and 100');
        }
    }
    calculateSellAmount(tokenBalance, params) {
        if (params.sellAmountTokens && params.sellAmountTokens > 0) {
            return Math.min(params.sellAmountTokens, tokenBalance);
        }
        else {
            const percentage = params.percentage || 100;
            return (tokenBalance * percentage) / 100;
        }
    }
    async getSwapQuote(params, sellAmount) {
        try {
            const slippage = params.slippage || config_1.default.trading.defaultSlippagePercent;
            const tokenMint = new web3_js_1.PublicKey(params.tokenAddress);
            const tokenInfo = await this.getTokenInfo(tokenMint);
            const decimals = tokenInfo?.decimals || 9;
            logger_1.logger.info('🔄 GETTING JUPITER SWAP QUOTE FOR SELL', {
                inputMint: params.tokenAddress,
                outputMint: 'SOL',
                amount: sellAmount,
                decimals: decimals,
                slippage: slippage,
            });
            const response = await axios_1.default.get(`${config_1.default.jupiter.apiUrl}/quote`, {
                params: {
                    inputMint: params.tokenAddress,
                    outputMint: 'So11111111111111111111111111111111111111112',
                    amount: Math.floor(sellAmount * Math.pow(10, decimals)),
                    slippageBps: Math.floor(slippage * 100),
                    onlyDirectRoutes: false,
                    asLegacyTransaction: false,
                    platformFeeBps: 0,
                },
                timeout: 10000,
            });
            if (response.data) {
                logger_1.logger.info('✅ JUPITER SELL QUOTE RECEIVED', {
                    inputAmount: response.data.inAmount,
                    outputAmount: response.data.outAmount,
                    priceImpact: response.data.priceImpactPct,
                    route: response.data.routePlan?.length || 0,
                });
            }
            return response.data;
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'getSwapQuote', params, sellAmount });
            return null;
        }
    }
    async executeSwap(wallet, quote, params) {
        try {
            const swapResponse = await axios_1.default.post(`${config_1.default.jupiter.swapApiUrl}`, {
                quoteResponse: quote,
                userPublicKey: wallet.publicKey.toString(),
                wrapAndUnwrapSol: true,
                computeUnitPriceMicroLamports: Math.floor((params.priorityFee || config_1.default.trading.priorityFeeSol) * web3_js_1.LAMPORTS_PER_SOL / 1000),
            });
            const { swapTransaction } = swapResponse.data;
            const transaction = web3_js_1.Transaction.from(Buffer.from(swapTransaction, 'base64'));
            const priorityFee = params.priorityFee || config_1.default.trading.priorityFeeSol;
            if (priorityFee > 0) {
                const priorityFeeIx = web3_js_1.ComputeBudgetProgram.setComputeUnitPrice({
                    microLamports: Math.floor(priorityFee * web3_js_1.LAMPORTS_PER_SOL / 1000),
                });
                transaction.instructions.unshift(priorityFeeIx);
            }
            transaction.sign(wallet.keypair);
            const signature = await wallet_1.connectionManager.executeWithRetry(async (connection) => {
                return (0, web3_js_1.sendAndConfirmTransaction)(connection, transaction, [wallet.keypair], {
                    commitment: 'confirmed',
                    maxRetries: config_1.default.limits.transactionRetryAttempts,
                });
            });
            logger_1.logger.info('✅ JUPITER SELL TRANSACTION CONFIRMED', {
                signature,
                tokenAddress: params.tokenAddress,
                amount: params.percentage || params.sellAmountTokens,
                executionMethod: 'Jupiter Swap'
            });
            return signature;
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'executeSwap', params });
            throw error;
        }
    }
    async getTokenInfo(tokenMint) {
        try {
            return wallet_1.connectionManager.executeWithRetry(async (connection) => {
                const mintInfo = await connection.getParsedAccountInfo(tokenMint);
                const data = mintInfo.value?.data;
                return {
                    decimals: data?.parsed?.info?.decimals || 9,
                };
            });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'getTokenInfo', tokenMint: tokenMint.toString() });
            return null;
        }
    }
    async quickSell(wallet, tokenAddress, percentage = 100) {
        const params = {
            walletAddress: wallet.publicKey.toString(),
            tokenAddress,
            percentage,
            slippage: config_1.default.trading.defaultSlippagePercent,
            priorityFee: config_1.default.trading.priorityFeeSol,
        };
        return this.executeSell(wallet, params);
    }
    async emergencySell(wallet, tokenAddress, percentage = 100) {
        const params = {
            walletAddress: wallet.publicKey.toString(),
            tokenAddress,
            percentage,
            slippage: config_1.default.trading.maxSlippagePercent,
            priorityFee: config_1.default.trading.maxPriorityFeeSol,
        };
        return this.executeSell(wallet, params);
    }
}
exports.SellExecutor = SellExecutor;
exports.default = SellExecutor;
//# sourceMappingURL=sell.js.map