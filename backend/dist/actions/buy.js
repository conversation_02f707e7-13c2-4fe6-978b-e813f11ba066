"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuyExecutor = void 0;
const web3_js_1 = require("@solana/web3.js");
const axios_1 = __importDefault(require("axios"));
const wallet_1 = require("../utils/wallet");
const notifications_1 = require("../utils/notifications");
const logger_1 = require("../utils/logger");
const config_1 = __importDefault(require("../config"));
class BuyExecutor {
    connection;
    constructor() {
        this.connection = new web3_js_1.Connection(config_1.default.solana.rpcEndpoint, 'confirmed');
    }
    async executeBuy(wallet, params) {
        const startTime = Date.now();
        try {
            this.validateBuyParams(params);
            const requiredSol = (params.amount || config_1.default.trading.defaultBuyAmountSol) +
                (params.priorityFee || config_1.default.trading.priorityFeeSol);
            if (wallet.balance.sol < requiredSol) {
                throw new Error(`Insufficient SOL balance: ${wallet.balance.sol} < ${requiredSol}`);
            }
            const quote = await this.getSwapQuote(params);
            if (!quote) {
                throw new Error('Failed to get swap quote');
            }
            const priceImpact = parseFloat(quote.priceImpactPct);
            const maxSlippage = params.maxSlippage || config_1.default.trading.maxSlippagePercent;
            if (priceImpact > maxSlippage) {
                throw new Error(`Price impact too high: ${priceImpact}% > ${maxSlippage}%`);
            }
            const signature = config_1.default.development.dryRunMode
                ? 'DRY_RUN_SIGNATURE'
                : await this.executeSwap(wallet, quote, params);
            const result = {
                success: true,
                signature,
                amount: parseFloat(quote.outAmount),
                price: parseFloat(quote.inAmount) / parseFloat(quote.outAmount),
                slippage: priceImpact,
                fee: params.priorityFee || config_1.default.trading.priorityFeeSol,
                timestamp: new Date(),
            };
            logger_1.tradeLogger.buy({
                ...result,
                tokenAddress: params.tokenAddress,
                walletAddress: params.walletAddress,
                executionTime: Date.now() - startTime,
            });
            await notifications_1.notificationManager.notifyBuy({
                tokenAddress: params.tokenAddress || 'Unknown',
                amount: result.amount || 0,
                price: result.price || 0,
                signature: signature,
            });
            return result;
        }
        catch (error) {
            const errorResult = {
                success: false,
                error: error.message,
                timestamp: new Date(),
            };
            logger_1.tradeLogger.error({
                ...errorResult,
                tokenAddress: params.tokenAddress,
                walletAddress: params.walletAddress,
                executionTime: Date.now() - startTime,
            });
            (0, logger_1.logError)(error, { context: 'executeBuy', params });
            return errorResult;
        }
    }
    validateBuyParams(params) {
        if (!params.walletAddress) {
            throw new Error('Wallet address is required');
        }
        if (!params.tokenAddress) {
            throw new Error('Token address is required');
        }
        const amount = params.amount || config_1.default.trading.defaultBuyAmountSol;
        if (amount <= 0) {
            throw new Error('Buy amount must be greater than 0');
        }
        if (amount > config_1.default.limits.maxTransactionSizeSol) {
            throw new Error(`Buy amount exceeds maximum: ${amount} > ${config_1.default.limits.maxTransactionSizeSol}`);
        }
        const slippage = params.slippage || config_1.default.trading.defaultSlippagePercent;
        if (slippage < 0 || slippage > 100) {
            throw new Error('Slippage must be between 0 and 100');
        }
    }
    async getSwapQuote(params) {
        try {
            const amount = params.amount || config_1.default.trading.defaultBuyAmountSol;
            const slippage = params.slippage || config_1.default.trading.defaultSlippagePercent;
            logger_1.logger.info('🔄 GETTING JUPITER SWAP QUOTE FOR BUY', {
                inputMint: 'SOL',
                outputMint: params.tokenAddress,
                amount: amount,
                slippage: slippage,
            });
            const response = await axios_1.default.get(`${config_1.default.jupiter.apiUrl}/quote`, {
                params: {
                    inputMint: 'So11111111111111111111111111111111111111112',
                    outputMint: params.tokenAddress,
                    amount: Math.floor(amount * web3_js_1.LAMPORTS_PER_SOL),
                    slippageBps: Math.floor(slippage * 100),
                    onlyDirectRoutes: false,
                    asLegacyTransaction: false,
                    platformFeeBps: 0,
                },
                timeout: 10000,
            });
            if (response.data) {
                logger_1.logger.info('✅ JUPITER QUOTE RECEIVED', {
                    inputAmount: response.data.inAmount,
                    outputAmount: response.data.outAmount,
                    priceImpact: response.data.priceImpactPct,
                    route: response.data.routePlan?.length || 0,
                });
            }
            return response.data;
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'getSwapQuote', params });
            return null;
        }
    }
    async executeSwap(wallet, quote, params) {
        try {
            logger_1.logger.info('🔄 EXECUTING JUPITER SWAP FOR BUY', {
                walletAddress: wallet.publicKey.toString(),
                tokenAddress: params.tokenAddress,
                inputAmount: quote.inAmount,
                outputAmount: quote.outAmount,
                priceImpact: quote.priceImpactPct,
            });
            const swapResponse = await axios_1.default.post(`${config_1.default.jupiter.swapApiUrl}`, {
                quoteResponse: quote,
                userPublicKey: wallet.publicKey.toString(),
                wrapAndUnwrapSol: true,
                computeUnitPriceMicroLamports: Math.floor((params.priorityFee || config_1.default.trading.priorityFeeSol) * web3_js_1.LAMPORTS_PER_SOL / 1000),
                prioritizationFeeLamports: Math.floor((params.priorityFee || config_1.default.trading.priorityFeeSol) * web3_js_1.LAMPORTS_PER_SOL),
            });
            const { swapTransaction } = swapResponse.data;
            const transaction = web3_js_1.Transaction.from(Buffer.from(swapTransaction, 'base64'));
            const priorityFee = params.priorityFee || config_1.default.trading.priorityFeeSol;
            if (priorityFee > 0) {
                const priorityFeeIx = web3_js_1.ComputeBudgetProgram.setComputeUnitPrice({
                    microLamports: Math.floor(priorityFee * web3_js_1.LAMPORTS_PER_SOL / 1000),
                });
                transaction.instructions.unshift(priorityFeeIx);
            }
            transaction.sign(wallet.keypair);
            const signature = await wallet_1.connectionManager.executeWithRetry(async (connection) => {
                return (0, web3_js_1.sendAndConfirmTransaction)(connection, transaction, [wallet.keypair], {
                    commitment: 'confirmed',
                    maxRetries: config_1.default.limits.transactionRetryAttempts,
                });
            });
            logger_1.logger.info('✅ JUPITER BUY TRANSACTION CONFIRMED', {
                signature,
                tokenAddress: params.tokenAddress,
                amount: params.amount,
                executionMethod: 'Jupiter Swap'
            });
            return signature;
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'executeSwap', params });
            throw error;
        }
    }
    async quickBuy(wallet, tokenAddress, customAmount) {
        const params = {
            walletAddress: wallet.publicKey.toString(),
            tokenAddress,
            amount: customAmount || config_1.default.trading.defaultBuyAmountSol,
            slippage: config_1.default.trading.defaultSlippagePercent,
            priorityFee: config_1.default.trading.priorityFeeSol,
        };
        return this.executeBuy(wallet, params);
    }
}
exports.BuyExecutor = BuyExecutor;
exports.default = BuyExecutor;
//# sourceMappingURL=buy.js.map