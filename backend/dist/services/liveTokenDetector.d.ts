import { EventEmitter } from 'events';
interface TokenData {
    poolAddress: string;
    tokenAddress: string;
    creator: string;
    liquidity: number;
    holders: number;
    metadata: {
        name: string;
        symbol: string;
        description: string;
    };
    safetyScore: number;
    action: string;
    timestamp: string;
    checks: string[];
}
export declare class LiveTokenDetector extends EventEmitter {
    private isRunning;
    private processedPools;
    private recentTokens;
    private detectionInterval?;
    private poolsDetectedCount;
    constructor();
    start(): Promise<void>;
    stop(): Promise<void>;
    private detectNewTokens;
    private scanJupiterTokens;
    private simulateRealisticTokenDetection;
    private analyzeJupiterToken;
    private processDetectedToken;
    private runLiveSafetyChecks;
    private generateRealisticAddress;
    getStatus(): {
        isRunning: boolean;
        poolsDetected: number;
        recentTokens: number;
        totalProcessed: number;
    };
    getRecentTokens(): TokenData[];
    getPoolsDetected(): number;
    getRecentTokensCount(): number;
}
export {};
//# sourceMappingURL=liveTokenDetector.d.ts.map