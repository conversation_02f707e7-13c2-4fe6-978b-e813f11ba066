{"version": 3, "file": "liveTokenDetector.js", "sourceRoot": "", "sources": ["../../src/services/liveTokenDetector.ts"], "names": [], "mappings": ";;;;;;AAAA,mCAAsC;AAEtC,kDAA0B;AAC1B,4CAAyC;AAyBzC,MAAa,iBAAkB,SAAQ,qBAAY;IACzC,SAAS,GAAG,KAAK,CAAC;IAClB,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC;IACnC,YAAY,GAAgB,EAAE,CAAC;IAE/B,iBAAiB,CAA8B;IAC/C,kBAAkB,GAAG,CAAC,CAAC;IAE/B;QACE,KAAK,EAAE,CAAC;IAEV,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO;QAE3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAChD,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACpD,eAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACvD,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAGlD,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,GAAG,EAAE;YACxC,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC,EAAE,KAAK,CAAC,CAAC;QAGV,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE5B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACtC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;QACrC,CAAC;QACD,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;IACjD,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE5B,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAG/B,MAAM,IAAI,CAAC,+BAA+B,EAAE,CAAC;QAE/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,oCAAoC,EAAE;gBACrE,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClD,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEzC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;oBAC3B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;wBAC5C,eAAM,CAAC,IAAI,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;wBAC5D,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;wBACtC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;wBACvC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;oBAC1D,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,+BAA+B;QAC3C,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACxD,MAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACzD,MAAM,kBAAkB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAE3D,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC;YAC9C,eAAM,CAAC,IAAI,CAAC,kCAAkC,eAAe,EAAE,CAAC,CAAC;YAEjE,MAAM,kBAAkB,GAAG;gBACzB,WAAW,EAAE,eAAe;gBAC5B,SAAS,EAAE,gBAAgB;gBAC3B,SAAS,EAAE,kBAAkB;gBAC7B,SAAS,EAAE;oBACT,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;oBACjC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,MAAM;iBAC3D;gBACD,QAAQ,EAAE;oBACR,IAAI,EAAE,YAAY,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;oBACpD,MAAM,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE;oBAC9C,WAAW,EAAE,iCAAiC;iBAC/C;gBACD,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE;gBACjD,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;aAC5E,CAAC;YAEF,MAAM,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;YACpD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YACzC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAAU;QAC1C,IAAI,CAAC;YACH,MAAM,SAAS,GAAG;gBAChB,WAAW,EAAE,IAAI,CAAC,wBAAwB,EAAE;gBAC5C,SAAS,EAAE,KAAK,CAAC,OAAO;gBACxB,SAAS,EAAE,IAAI,CAAC,wBAAwB,EAAE;gBAC1C,SAAS,EAAE;oBACT,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;oBACjC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,MAAM;iBAC3D;gBACD,QAAQ,EAAE;oBACR,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,wBAAwB;iBAC3D;gBACD,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE;gBACjD,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;aAC5E,CAAC;YAEF,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YAC3C,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QACpF,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,SAAc;QAC/C,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;YACjC,WAAW,EAAE,SAAS,CAAC,WAAW;YAClC,YAAY,EAAE,SAAS,CAAC,SAAS;YACjC,OAAO,EAAE,SAAS,CAAC,SAAS;YAC5B,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,IAAI;YAC7B,MAAM,EAAE,SAAS,CAAC,QAAQ,CAAC,MAAM;YACjC,SAAS,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM;YAC5D,OAAO,EAAE,SAAS,CAAC,WAAW;SAC/B,CAAC,CAAC;QAGH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAE/D,MAAM,SAAS,GAAc;YAC3B,WAAW,EAAE,SAAS,CAAC,WAAW;YAClC,YAAY,EAAE,SAAS,CAAC,SAAS;YACjC,OAAO,EAAE,SAAS,CAAC,SAAS;YAC5B,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,SAAS;YACxC,OAAO,EAAE,SAAS,CAAC,WAAW;YAC9B,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,WAAW,EAAE,YAAY,CAAC,KAAK;YAC/B,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU;YACzD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,MAAM,EAAE,YAAY,CAAC,MAAM;SAC5B,CAAC;QAGF,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACrC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAClC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACrD,CAAC;QAGD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;QAEtC,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;YACxB,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE;gBACnD,IAAI,EAAE,SAAS,CAAC,WAAW;gBAC3B,KAAK,EAAE,SAAS,CAAC,SAAS;gBAC1B,MAAM,EAAE,SAAS;gBACjB,OAAO,EAAE,SAAS,CAAC,SAAS;aAC7B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,SAAc;QAC9C,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,QAAQ,GAAG,GAAG,CAAC;QAGrB,IAAI,SAAS,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC,EAAE,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,gBAAgB,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC;YACvF,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,gBAAgB,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC;QACxF,CAAC;QAGD,IAAI,SAAS,CAAC,WAAW,IAAI,EAAE,EAAE,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,CAAC,WAAW,UAAU,CAAC,CAAC;YAC3D,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,CAAC,WAAW,SAAS,CAAC,CAAC;QAC5D,CAAC;QAGD,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,sBAAsB,SAAS,CAAC,QAAQ,CAAC,IAAI,MAAM,SAAS,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7F,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAChD,CAAC;QAGD,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;YACxB,MAAM,cAAc,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/F,MAAM,CAAC,IAAI,CAAC,gBAAgB,cAAc,EAAE,CAAC,CAAC;YAC9C,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACtC,CAAC;QAGD,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;QAClE,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,UAAU,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC;YACtE,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,UAAU,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,IAAI,EAAE,CAAC;QAE3B,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QACrD,eAAM,CAAC,IAAI,CAAC,oBAAoB,KAAK,IAAI,QAAQ,EAAE,CAAC,CAAC;QAErD,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;IACnC,CAAC;IAEO,wBAAwB;QAC9B,MAAM,KAAK,GAAG,4DAA4D,CAAC;QAC3E,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QACnE,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAGD,SAAS;QACP,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,aAAa,EAAE,IAAI,CAAC,kBAAkB;YACtC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;YACtC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI;SACzC,CAAC;IACJ,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,gBAAgB;QACd,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC;IAED,oBAAoB;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;IAClC,CAAC;CACF;AA7QD,8CA6QC"}