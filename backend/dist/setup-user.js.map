{"version": 3, "file": "setup-user.js", "sourceRoot": "", "sources": ["../src/setup-user.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,qDAAkD;AAClD,2CAAgD;AAChD,sDAA8B;AAE9B,KAAK,UAAU,aAAa;IAC1B,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;IAE3E,IAAI,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,MAAM,WAAW,GAAG,UAAU,IAAI,CAAC,GAAG,EAAE,gBAAgB,CAAC;QACzD,MAAM,IAAI,GAAG,MAAM,yBAAW,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QAG7C,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,uBAAc,GAAE,CAAC;QAGrC,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,0BAA0B,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,CAAC,mCAAmC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAG9E,MAAM,UAAU,GAAG,MAAM,yBAAW,CAAC,eAAe,CAClD,IAAI,CAAC,EAAE,EACP,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAC/C,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,8BAA8B,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC;QAG3D,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAC9D,MAAM,yBAAW,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAAE,EAAE;YACnD,SAAS,EAAE,gBAAM,CAAC,OAAO,CAAC,mBAAmB;YAC7C,QAAQ,EAAE,gBAAM,CAAC,OAAO,CAAC,sBAAsB;YAC/C,QAAQ,EAAE,IAAI;YACd,iBAAiB,EAAE,gBAAM,CAAC,UAAU,CAAC,iBAAiB;YACtD,eAAe,EAAE,gBAAM,CAAC,UAAU,CAAC,eAAe;SACnD,CAAC,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,wBAAwB,gBAAM,CAAC,OAAO,CAAC,mBAAmB,MAAM,CAAC,CAAC;QAC9E,OAAO,CAAC,GAAG,CAAC,yBAAyB,gBAAM,CAAC,UAAU,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC7E,OAAO,CAAC,GAAG,CAAC,uBAAuB,gBAAM,CAAC,UAAU,CAAC,eAAe,GAAG,CAAC,CAAC;QAGzE,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,MAAM,yBAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAGtD,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,cAAc,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,oBAAoB,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAChC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QACxD,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;QACrE,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;QACrE,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAGpD,MAAM,QAAQ,GAAG;YACf,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,UAAU,CAAC,EAAE;YACvB,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE;YACvC,eAAe,EAAE,eAAe;YAChC,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC7D,QAAQ,EAAE;gBACR,SAAS,EAAE,gBAAM,CAAC,OAAO,CAAC,mBAAmB;gBAC7C,iBAAiB,EAAE,gBAAM,CAAC,UAAU,CAAC,iBAAiB;gBACtD,eAAe,EAAE,gBAAM,CAAC,UAAU,CAAC,eAAe;aACnD;SACF,CAAC;QAGF,MAAM,EAAE,GAAG,wDAAa,IAAI,GAAC,CAAC;QAC9B,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CACzB,iBAAiB,EACjB,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAClC,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAEzD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QACxC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAGD,aAAa,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;IAC9B,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IACrC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}