import { EventEmitter } from 'events';
export declare class RaydiumPoolMonitor extends EventEmitter {
    private wsConnection?;
    private isMonitoring;
    private monitoredPools;
    private heliusWebhookSetup;
    constructor();
    private setupEventHandlers;
    startMonitoring(): Promise<void>;
    stopMonitoring(): Promise<void>;
    private setupHeliusWebhooks;
    private startWebSocketMonitoring;
    private subscribeToRaydiumProgram;
    private handleWebSocketMessage;
    private startPollingMonitoring;
    private pollForNewPools;
    private processAccountUpdate;
    private processNewPool;
    private parsePoolInfo;
    private createLiquidityPool;
    private isNewToken;
    private handlePoolCreated;
    private handleTokenDetected;
    handleHeliusWebhook(payload: any): Promise<void>;
    private isRaydiumPoolCreation;
    private processHeliusPoolCreation;
}
export default RaydiumPoolMonitor;
//# sourceMappingURL=raydium.d.ts.map