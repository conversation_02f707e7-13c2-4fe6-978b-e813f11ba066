"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RaydiumPoolMonitor = void 0;
const web3_js_1 = require("@solana/web3.js");
const ws_1 = __importDefault(require("ws"));
const axios_1 = __importDefault(require("axios"));
const events_1 = require("events");
const decimal_js_1 = require("decimal.js");
const wallet_1 = require("../utils/wallet");
const logger_1 = require("../utils/logger");
const config_1 = __importDefault(require("../config"));
class RaydiumPoolMonitor extends events_1.EventEmitter {
    wsConnection;
    isMonitoring = false;
    monitoredPools = new Set();
    heliusWebhookSetup = false;
    constructor() {
        super();
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        this.on('poolCreated', this.handlePoolCreated.bind(this));
        this.on('tokenDetected', this.handleTokenDetected.bind(this));
    }
    async startMonitoring() {
        if (this.isMonitoring) {
            logger_1.logger.warn('Pool monitoring already active');
            return;
        }
        try {
            this.isMonitoring = true;
            await this.setupHeliusWebhooks();
            await this.startWebSocketMonitoring();
            this.startPollingMonitoring();
            logger_1.poolLogger.monitored({ status: 'started', methods: ['helius', 'websocket', 'polling'] });
            logger_1.logger.info('Raydium pool monitoring started');
        }
        catch (error) {
            this.isMonitoring = false;
            (0, logger_1.logError)(error, { context: 'startMonitoring' });
            throw error;
        }
    }
    async stopMonitoring() {
        this.isMonitoring = false;
        if (this.wsConnection) {
            this.wsConnection.close();
            this.wsConnection = undefined;
        }
        logger_1.logger.info('Raydium pool monitoring stopped');
    }
    async setupHeliusWebhooks() {
        if (!config_1.default.helius.apiKey || this.heliusWebhookSetup)
            return;
        try {
            const webhookUrl = `${config_1.default.helius.webhookUrl}/raydium-pools`;
            const response = await axios_1.default.post(`https://api.helius.xyz/v0/webhooks?api-key=${config_1.default.helius.apiKey}`, {
                webhookURL: webhookUrl,
                transactionTypes: ['Any'],
                accountAddresses: [config_1.default.raydium.programId.toString()],
                webhookType: 'enhanced',
            });
            if (response.data.success) {
                this.heliusWebhookSetup = true;
                logger_1.logger.info('Helius webhook setup successful', { webhookId: response.data.webhookID });
            }
        }
        catch (error) {
            logger_1.logger.warn('Failed to setup Helius webhooks, using fallback methods', {
                error: error.message
            });
        }
    }
    async startWebSocketMonitoring() {
        if (!config_1.default.solana.wsEndpoint)
            return;
        try {
            this.wsConnection = new ws_1.default(config_1.default.solana.wsEndpoint);
            this.wsConnection.on('open', () => {
                logger_1.logger.info('WebSocket connection established');
                this.subscribeToRaydiumProgram();
            });
            this.wsConnection.on('message', (data) => {
                this.handleWebSocketMessage(data);
            });
            this.wsConnection.on('error', (error) => {
                (0, logger_1.logError)(error, { context: 'WebSocket' });
            });
            this.wsConnection.on('close', () => {
                logger_1.logger.warn('WebSocket connection closed, attempting reconnect...');
                if (this.isMonitoring) {
                    setTimeout(() => this.startWebSocketMonitoring(), 5000);
                }
            });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'startWebSocketMonitoring' });
        }
    }
    subscribeToRaydiumProgram() {
        if (!this.wsConnection)
            return;
        const subscribeMessage = {
            jsonrpc: '2.0',
            id: 1,
            method: 'programSubscribe',
            params: [
                config_1.default.raydium.programId.toString(),
                {
                    encoding: 'jsonParsed',
                    commitment: 'confirmed',
                },
            ],
        };
        this.wsConnection.send(JSON.stringify(subscribeMessage));
        logger_1.logger.debug('Subscribed to Raydium program updates');
    }
    handleWebSocketMessage(data) {
        try {
            const message = JSON.parse(data.toString());
            if (message.method === 'programNotification') {
                this.processAccountUpdate(message.params.result);
            }
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'handleWebSocketMessage' });
        }
    }
    startPollingMonitoring() {
        const pollInterval = setInterval(async () => {
            if (!this.isMonitoring) {
                clearInterval(pollInterval);
                return;
            }
            try {
                await this.pollForNewPools();
            }
            catch (error) {
                (0, logger_1.logError)(error, { context: 'pollForNewPools' });
            }
        }, config_1.default.performance.poolMonitorIntervalMs);
    }
    async pollForNewPools() {
        try {
            const accounts = await wallet_1.connectionManager.executeWithRetry(async (connection) => {
                return connection.getProgramAccounts(config_1.default.raydium.programId, {
                    commitment: 'confirmed',
                    encoding: 'base64',
                });
            });
            for (const account of accounts) {
                if (!this.monitoredPools.has(account.pubkey.toString())) {
                    await this.processNewPool(account.pubkey.toString(), account.account);
                }
            }
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'pollForNewPools' });
        }
    }
    async processAccountUpdate(result) {
        const { pubkey, account } = result;
        if (!this.monitoredPools.has(pubkey)) {
            await this.processNewPool(pubkey, account);
        }
    }
    async processNewPool(poolAddress, accountInfo) {
        try {
            this.monitoredPools.add(poolAddress);
            const poolInfo = await this.parsePoolInfo(poolAddress, accountInfo);
            if (!poolInfo)
                return;
            const liquidityPool = await this.createLiquidityPool(poolInfo);
            if (!liquidityPool)
                return;
            const poolCreatedEvent = {
                poolId: poolAddress,
                baseMint: poolInfo.baseMint,
                quoteMint: poolInfo.quoteMint,
                baseReserve: liquidityPool.baseReserve.toString(),
                quoteReserve: liquidityPool.quoteReserve.toString(),
                timestamp: new Date(),
                signature: '',
                slot: 0,
            };
            this.emit('poolCreated', poolCreatedEvent);
            if (this.isNewToken(poolInfo)) {
                const tokenDetectedEvent = {
                    tokenAddress: poolInfo.baseMint,
                    poolId: poolAddress,
                    liquidity: liquidityPool.liquidity.sol,
                    timestamp: new Date(),
                };
                this.emit('tokenDetected', tokenDetectedEvent);
            }
            logger_1.poolLogger.created({
                poolId: poolAddress,
                baseMint: poolInfo.baseMint,
                quoteMint: poolInfo.quoteMint,
                liquidity: liquidityPool.liquidity.sol,
            });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'processNewPool', poolAddress });
        }
    }
    async parsePoolInfo(poolAddress, accountInfo) {
        try {
            const data = accountInfo.data;
            return {
                id: poolAddress,
                baseMint: data.baseMint || '',
                quoteMint: data.quoteMint || '',
                lpMint: data.lpMint || '',
                baseDecimals: data.baseDecimals || 9,
                quoteDecimals: data.quoteDecimals || 9,
                lpDecimals: data.lpDecimals || 9,
                version: 4,
                programId: config_1.default.raydium.programId.toString(),
                authority: config_1.default.raydium.authority.toString(),
                openOrders: data.openOrders || '',
                targetOrders: data.targetOrders || '',
                baseVault: data.baseVault || '',
                quoteVault: data.quoteVault || '',
                withdrawQueue: data.withdrawQueue || '',
                lpVault: data.lpVault || '',
                marketVersion: 3,
                marketProgramId: data.marketProgramId || '',
                marketId: data.marketId || '',
                marketAuthority: data.marketAuthority || '',
                marketBaseVault: data.marketBaseVault || '',
                marketQuoteVault: data.marketQuoteVault || '',
                marketBids: data.marketBids || '',
                marketAsks: data.marketAsks || '',
                marketEventQueue: data.marketEventQueue || '',
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'parsePoolInfo', poolAddress });
            return null;
        }
    }
    async createLiquidityPool(poolInfo) {
        try {
            const baseVault = new web3_js_1.PublicKey(poolInfo.baseVault);
            const quoteVault = new web3_js_1.PublicKey(poolInfo.quoteVault);
            const [baseBalance, quoteBalance] = await Promise.all([
                wallet_1.connectionManager.executeWithRetry(async (connection) => {
                    const balance = await connection.getTokenAccountBalance(baseVault);
                    return new decimal_js_1.Decimal(balance.value.amount).div(Math.pow(10, balance.value.decimals));
                }),
                wallet_1.connectionManager.executeWithRetry(async (connection) => {
                    const balance = await connection.getTokenAccountBalance(quoteVault);
                    return new decimal_js_1.Decimal(balance.value.amount).div(Math.pow(10, balance.value.decimals));
                }),
            ]);
            const solLiquidity = poolInfo.quoteMint === 'So11111111111111111111111111111111111111112'
                ? quoteBalance.toNumber()
                : quoteBalance.toNumber();
            return {
                poolId: poolInfo.id,
                baseMint: poolInfo.baseMint,
                quoteMint: poolInfo.quoteMint,
                baseReserve: baseBalance,
                quoteReserve: quoteBalance,
                lpSupply: new decimal_js_1.Decimal(0),
                price: quoteBalance.div(baseBalance),
                liquidity: {
                    sol: solLiquidity,
                    usd: solLiquidity * 100,
                },
                volume24h: 0,
                createdAt: new Date(),
                isActive: true,
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'createLiquidityPool', poolId: poolInfo.id });
            return null;
        }
    }
    isNewToken(poolInfo) {
        const knownTokens = [
            'So11111111111111111111111111111111111111112',
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
            'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
        ];
        return !knownTokens.includes(poolInfo.baseMint) && !knownTokens.includes(poolInfo.quoteMint);
    }
    async handlePoolCreated(event) {
        logger_1.poolLogger.created(event);
    }
    async handleTokenDetected(event) {
        logger_1.logger.info('New token detected', event);
    }
    async handleHeliusWebhook(payload) {
        try {
            for (const transaction of payload) {
                if (this.isRaydiumPoolCreation(transaction)) {
                    await this.processHeliusPoolCreation(transaction);
                }
            }
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'handleHeliusWebhook' });
        }
    }
    isRaydiumPoolCreation(transaction) {
        return transaction.instructions?.some((ix) => ix.programId === config_1.default.raydium.programId.toString());
    }
    async processHeliusPoolCreation(transaction) {
        logger_1.logger.debug('Processing Helius pool creation', { signature: transaction.signature });
    }
}
exports.RaydiumPoolMonitor = RaydiumPoolMonitor;
exports.default = RaydiumPoolMonitor;
//# sourceMappingURL=raydium.js.map