import { User, WalletData, UserSettings } from '../types';
export declare class UserManager {
    private database;
    constructor();
    createUser(email?: string): Promise<User>;
    addWalletToUser(userId: string, privateKeyHex?: string): Promise<WalletData>;
    enableSniperForUser(userId: string): Promise<void>;
    disableSniperForUser(userId: string): Promise<void>;
    updateUserTradingSettings(userId: string, settings: Partial<UserSettings['trading']>): Promise<void>;
    getActiveSniperUsers(): Promise<User[]>;
    getUserBalances(userId: string): Promise<any>;
    private getUserWallet;
}
export declare const userManager: UserManager;
export default userManager;
//# sourceMappingURL=userManager.d.ts.map