"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationManager = exports.NotificationManager = void 0;
const axios_1 = __importDefault(require("axios"));
const config_1 = __importDefault(require("../config"));
const logger_1 = require("./logger");
class NotificationManager {
    telegramBotToken;
    telegramChatId;
    discordWebhookUrl;
    constructor() {
        this.telegramBotToken = config_1.default.notifications.telegramBotToken;
        this.telegramChatId = config_1.default.notifications.telegramChatId;
        this.discordWebhookUrl = config_1.default.notifications.discordWebhookUrl;
    }
    async sendNotification(payload) {
        const promises = [];
        if (this.telegramBotToken && this.telegramChatId) {
            promises.push(this.sendTelegramMessage(payload));
        }
        if (this.discordWebhookUrl) {
            promises.push(this.sendDiscordMessage(payload));
        }
        if (promises.length === 0) {
            logger_1.logger.debug('No notification channels configured');
            return;
        }
        try {
            await Promise.allSettled(promises);
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'sendNotification', payload });
        }
    }
    async sendTelegramMessage(payload) {
        if (!this.telegramBotToken || !this.telegramChatId)
            return;
        try {
            const message = this.formatTelegramMessage(payload);
            const url = `https://api.telegram.org/bot${this.telegramBotToken}/sendMessage`;
            await axios_1.default.post(url, {
                chat_id: this.telegramChatId,
                text: message,
                parse_mode: 'Markdown',
                disable_web_page_preview: true,
            });
            logger_1.logger.debug('Telegram notification sent', { type: payload.type });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'sendTelegramMessage', payload });
        }
    }
    async sendDiscordMessage(payload) {
        if (!this.discordWebhookUrl)
            return;
        try {
            const embed = this.formatDiscordEmbed(payload);
            await axios_1.default.post(this.discordWebhookUrl, {
                embeds: [embed],
            });
            logger_1.logger.debug('Discord notification sent', { type: payload.type });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'sendDiscordMessage', payload });
        }
    }
    formatTelegramMessage(payload) {
        const emoji = this.getEmojiForType(payload.type);
        let message = `${emoji} *${payload.title}*\n\n${payload.message}`;
        if (payload.data) {
            message += '\n\n*Details:*';
            Object.entries(payload.data).forEach(([key, value]) => {
                message += `\n• ${key}: \`${value}\``;
            });
        }
        message += `\n\n_${payload.timestamp.toISOString()}_`;
        return message;
    }
    formatDiscordEmbed(payload) {
        const color = this.getColorForType(payload.type);
        const emoji = this.getEmojiForType(payload.type);
        const embed = {
            title: `${emoji} ${payload.title}`,
            description: payload.message,
            color: color,
            timestamp: payload.timestamp.toISOString(),
            footer: {
                text: 'Sol Bullet Sniper Bot',
            },
        };
        if (payload.data) {
            embed.fields = Object.entries(payload.data).map(([key, value]) => ({
                name: key,
                value: String(value),
                inline: true,
            }));
        }
        return embed;
    }
    getEmojiForType(type) {
        switch (type) {
            case 'buy': return '🟢';
            case 'sell': return '🔴';
            case 'alert': return '⚠️';
            case 'error': return '❌';
            default: return 'ℹ️';
        }
    }
    getColorForType(type) {
        switch (type) {
            case 'buy': return 0x00ff00;
            case 'sell': return 0xff0000;
            case 'alert': return 0xffff00;
            case 'error': return 0xff0000;
            default: return 0x0099ff;
        }
    }
    async notifyBuy(data) {
        await this.sendNotification({
            type: 'buy',
            title: 'Token Purchase Executed',
            message: `Successfully bought ${data.amount} tokens`,
            data: {
                'Token': data.tokenAddress,
                'Amount': data.amount,
                'Price': `$${data.price.toFixed(6)}`,
                'Transaction': data.signature,
            },
            timestamp: new Date(),
        });
    }
    async notifySell(data) {
        const pnlEmoji = data.pnl >= 0 ? '📈' : '📉';
        await this.sendNotification({
            type: 'sell',
            title: 'Token Sale Executed',
            message: `Successfully sold ${data.amount} tokens ${pnlEmoji}`,
            data: {
                'Token': data.tokenAddress,
                'Amount': data.amount,
                'Price': `$${data.price.toFixed(6)}`,
                'P&L': `${data.pnl >= 0 ? '+' : ''}${data.pnl.toFixed(2)}%`,
                'Transaction': data.signature,
            },
            timestamp: new Date(),
        });
    }
    async notifyTokenDetected(data) {
        await this.sendNotification({
            type: 'alert',
            title: 'New Token Detected',
            message: `Token ${data.action === 'buying' ? 'passed filters and buying' : 'filtered out'}`,
            data: {
                'Token': data.tokenAddress,
                'Pool': data.poolId,
                'Liquidity': `${data.liquidity} SOL`,
                'Action': data.action,
                ...(data.reason && { 'Reason': data.reason }),
            },
            timestamp: new Date(),
        });
    }
    async notifyError(error, context) {
        await this.sendNotification({
            type: 'error',
            title: 'System Error',
            message: error.message,
            data: {
                'Error': error.name,
                'Stack': error.stack?.split('\n')[0] || 'No stack trace',
                ...context,
            },
            timestamp: new Date(),
        });
    }
    async notifyPriceAlert(data) {
        const typeText = data.type.replace('_', ' ').toUpperCase();
        await this.sendNotification({
            type: 'alert',
            title: `${typeText} Triggered`,
            message: `Price alert triggered for token`,
            data: {
                'Token': data.tokenAddress,
                'Type': typeText,
                'Current Price': `$${data.currentPrice.toFixed(6)}`,
                'Target Price': `$${data.targetPrice.toFixed(6)}`,
                'Change': `${data.percentage >= 0 ? '+' : ''}${data.percentage.toFixed(2)}%`,
            },
            timestamp: new Date(),
        });
    }
}
exports.NotificationManager = NotificationManager;
exports.notificationManager = new NotificationManager();
exports.default = exports.notificationManager;
//# sourceMappingURL=notifications.js.map