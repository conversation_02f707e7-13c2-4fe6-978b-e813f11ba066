"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userManager = exports.UserManager = void 0;
const supabase_1 = __importDefault(require("../database/supabase"));
const wallet_1 = require("./wallet");
const logger_1 = require("./logger");
class UserManager {
    database;
    constructor() {
        this.database = new supabase_1.default();
    }
    async createUser(email) {
        try {
            const user = await this.database.createUser(email);
            logger_1.logger.info('✅ NEW USER CREATED IN SUPABASE', {
                userId: user.id,
                email: user.email,
            });
            return user;
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'createUser', email });
            throw error;
        }
    }
    async addWalletToUser(userId, privateKeyHex) {
        try {
            let keypair;
            if (privateKeyHex) {
                keypair = (0, wallet_1.importWalletFromPrivateKey)(privateKeyHex);
                logger_1.logger.info('🔑 IMPORTING EXISTING WALLET', { userId });
            }
            else {
                const walletData = (0, wallet_1.generateWallet)();
                keypair = walletData.keypair;
                logger_1.logger.info('💰 GENERATING NEW WALLET', { userId });
            }
            const walletData = await this.database.createWallet(userId, keypair.publicKey.toString(), keypair.secretKey);
            logger_1.logger.info('✅ WALLET SAVED TO SUPABASE', {
                userId,
                walletId: walletData.id,
                publicKey: walletData.publicKey,
            });
            return walletData;
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'addWalletToUser', userId });
            throw error;
        }
    }
    async enableSniperForUser(userId) {
        try {
            const user = await this.database.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            const updatedSettings = {
                ...user.settings,
                trading: {
                    ...user.settings.trading,
                    autoSell: true,
                },
            };
            await this.database.updateUserSettings(userId, updatedSettings);
            logger_1.logger.info('🎯 SNIPER ENABLED IN SUPABASE', { userId });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'enableSniperForUser', userId });
            throw error;
        }
    }
    async disableSniperForUser(userId) {
        try {
            logger_1.logger.info('Sniper disabled for user (mock)', { userId });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'disableSniperForUser', userId });
            throw error;
        }
    }
    async updateUserTradingSettings(userId, settings) {
        try {
            const user = await this.database.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            const updatedSettings = {
                ...user.settings,
                trading: {
                    ...user.settings.trading,
                    ...settings,
                },
            };
            await this.database.updateUserSettings(userId, updatedSettings);
            logger_1.logger.info('⚙️ TRADING SETTINGS UPDATED IN SUPABASE', { userId, settings });
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'updateUserTradingSettings', userId });
            throw error;
        }
    }
    async getActiveSniperUsers() {
        try {
            logger_1.logger.info('📊 QUERYING SUPABASE FOR ACTIVE SNIPER USERS');
            return [];
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'getActiveSniperUsers' });
            return [];
        }
    }
    async getUserBalances(userId) {
        try {
            const wallets = await this.database.getUserWallets(userId);
            const balances = [];
            for (const walletData of wallets) {
                try {
                    const userWallet = await this.getUserWallet(walletData);
                    balances.push({
                        walletId: walletData.id,
                        publicKey: walletData.publicKey,
                        balance: userWallet.balance,
                    });
                }
                catch (error) {
                    (0, logger_1.logError)(error, { context: 'getUserWalletBalance', walletId: walletData.id });
                }
            }
            return balances;
        }
        catch (error) {
            (0, logger_1.logError)(error, { context: 'getUserBalances', userId });
            throw error;
        }
    }
    async getUserWallet(walletData) {
        return {
            publicKey: walletData.publicKey,
            balance: {
                sol: 0,
                tokens: [],
            },
        };
    }
}
exports.UserManager = UserManager;
exports.userManager = new UserManager();
exports.default = exports.userManager;
//# sourceMappingURL=userManager.js.map