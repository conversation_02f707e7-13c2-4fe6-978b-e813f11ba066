{"version": 3, "file": "userManager.js", "sourceRoot": "", "sources": ["../../src/utils/userManager.ts"], "names": [], "mappings": ";;;;;;AAEA,oEAAmD;AACnD,qCAAsE;AACtE,qCAA4C;AAE5C,MAAa,WAAW;IACd,QAAQ,CAAkB;IAElC;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAe,EAAE,CAAC;IACxC,CAAC;IAKD,KAAK,CAAC,UAAU,CAAC,KAAc;QAC7B,IAAI,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAEnD,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;gBAC5C,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,KAAK,EAAE,IAAI,CAAC,KAAK;aAClB,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,KAAc,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CACnB,MAAc,EACd,aAAsB;QAEtB,IAAI,CAAC;YACH,IAAI,OAAgB,CAAC;YAErB,IAAI,aAAa,EAAE,CAAC;gBAElB,OAAO,GAAG,IAAA,mCAA0B,EAAC,aAAa,CAAC,CAAC;gBACpD,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAC1D,CAAC;iBAAM,CAAC;gBAEN,MAAM,UAAU,GAAG,IAAA,uBAAc,GAAE,CAAC;gBACpC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;gBAC7B,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YACtD,CAAC;YAGD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CACjD,MAAM,EACN,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,EAC5B,OAAO,CAAC,SAAS,CAClB,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACxC,MAAM;gBACN,QAAQ,EAAE,UAAU,CAAC,EAAE;gBACvB,SAAS,EAAE,UAAU,CAAC,SAAS;aAChC,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,KAAc,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE,MAAM,EAAE,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,MAAc;QACtC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACjD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACpC,CAAC;YAGD,MAAM,eAAe,GAAiB;gBACpC,GAAG,IAAI,CAAC,QAAQ;gBAChB,OAAO,EAAE;oBACP,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO;oBACxB,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC;YAEF,MAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YAEhE,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,KAAc,EAAE,EAAE,OAAO,EAAE,qBAAqB,EAAE,MAAM,EAAE,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,MAAc;QACvC,IAAI,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,KAAc,EAAE,EAAE,OAAO,EAAE,sBAAsB,EAAE,MAAM,EAAE,CAAC,CAAC;YACtE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,yBAAyB,CAC7B,MAAc,EACd,QAA0C;QAE1C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACjD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACpC,CAAC;YAED,MAAM,eAAe,GAAiB;gBACpC,GAAG,IAAI,CAAC,QAAQ;gBAChB,OAAO,EAAE;oBACP,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO;oBACxB,GAAG,QAAQ;iBACZ;aACF,CAAC;YAEF,MAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YAEhE,eAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,KAAc,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE,MAAM,EAAE,CAAC,CAAC;YAC3E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,oBAAoB;QACxB,IAAI,CAAC;YAIH,eAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YAC5D,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,KAAc,EAAE,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;YAC9D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,MAAc;QAClC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC3D,MAAM,QAAQ,GAAG,EAAE,CAAC;YAEpB,KAAK,MAAM,UAAU,IAAI,OAAO,EAAE,CAAC;gBACjC,IAAI,CAAC;oBACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;oBACxD,QAAQ,CAAC,IAAI,CAAC;wBACZ,QAAQ,EAAE,UAAU,CAAC,EAAE;wBACvB,SAAS,EAAE,UAAU,CAAC,SAAS;wBAC/B,OAAO,EAAE,UAAU,CAAC,OAAO;qBAC5B,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAA,iBAAQ,EAAC,KAAc,EAAE,EAAE,OAAO,EAAE,sBAAsB,EAAE,QAAQ,EAAE,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC;gBACzF,CAAC;YACH,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,KAAc,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE,MAAM,EAAE,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,UAAsB;QAGhD,OAAO;YACL,SAAS,EAAE,UAAU,CAAC,SAAS;YAC/B,OAAO,EAAE;gBACP,GAAG,EAAE,CAAC;gBACN,MAAM,EAAE,EAAE;aACX;SACF,CAAC;IACJ,CAAC;CACF;AAlMD,kCAkMC;AAEY,QAAA,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;AAC7C,kBAAe,mBAAW,CAAC"}