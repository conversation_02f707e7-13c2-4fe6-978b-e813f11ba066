import { Connection, Keypair, PublicKey } from '@solana/web3.js';
import { UserWallet, TokenBalance, WalletData } from '../types';
export declare function encryptPrivateKey(privateKey: Uint8Array, encryptionKey: string): string;
export declare function decryptPrivateKey(encryptedPrivateKey: string, encryptionKey: string): Uint8Array;
export declare function generateWallet(): {
    keypair: Keypair;
    walletData: Partial<WalletData>;
};
export declare function loadWalletFromData(walletData: WalletData): Keypair;
export declare class ConnectionManager {
    private connections;
    private currentIndex;
    constructor();
    getConnection(): Promise<Connection>;
    executeWithRetry<T>(operation: (connection: Connection) => Promise<T>, maxRetries?: number): Promise<T>;
}
export declare const connectionManager: ConnectionManager;
export declare function getSolBalance(publicKey: PublicKey): Promise<number>;
export declare function getTokenBalances(publicKey: PublicKey): Promise<TokenBalance[]>;
export declare function getUserWallet(walletData: WalletData): Promise<UserWallet>;
export declare function getTokenAccountAddress(walletPublicKey: PublicKey, tokenMint: PublicKey): Promise<PublicKey>;
export declare function getTokenBalance(walletPublicKey: PublicKey, tokenMint: PublicKey): Promise<number>;
export declare function isValidPublicKey(publicKey: string): boolean;
export declare function isValidPrivateKey(privateKey: string): boolean;
export declare function importWalletFromPrivateKey(privateKeyHex: string): Keypair;
export declare function exportPrivateKey(keypair: Keypair): string;
export declare function estimateTransactionFee(connection?: Connection): Promise<number>;
declare const _default: {
    generateWallet: typeof generateWallet;
    loadWalletFromData: typeof loadWalletFromData;
    getSolBalance: typeof getSolBalance;
    getTokenBalances: typeof getTokenBalances;
    getUserWallet: typeof getUserWallet;
    getTokenBalance: typeof getTokenBalance;
    isValidPublicKey: typeof isValidPublicKey;
    isValidPrivateKey: typeof isValidPrivateKey;
    importWalletFromPrivateKey: typeof importWalletFromPrivateKey;
    exportPrivateKey: typeof exportPrivateKey;
    connectionManager: ConnectionManager;
};
export default _default;
//# sourceMappingURL=wallet.d.ts.map