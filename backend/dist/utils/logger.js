"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.systemLogger = exports.poolLogger = exports.tradeLogger = exports.logger = void 0;
exports.logWithContext = logWithContext;
exports.logError = logError;
const winston_1 = __importDefault(require("winston"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const config_1 = __importDefault(require("../config"));
if (config_1.default.logging.toFile && !fs_1.default.existsSync(config_1.default.logging.directory)) {
    fs_1.default.mkdirSync(config_1.default.logging.directory, { recursive: true });
}
const logFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json(), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    if (Object.keys(meta).length > 0) {
        log += ` ${JSON.stringify(meta)}`;
    }
    return log;
}));
const transports = [
    new winston_1.default.transports.Console({
        format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple())
    })
];
if (config_1.default.logging.toFile) {
    transports.push(new winston_1.default.transports.File({
        filename: path_1.default.join(config_1.default.logging.directory, 'error.log'),
        level: 'error',
        format: logFormat,
        maxsize: 5242880,
        maxFiles: 5,
    }), new winston_1.default.transports.File({
        filename: path_1.default.join(config_1.default.logging.directory, 'combined.log'),
        format: logFormat,
        maxsize: 5242880,
        maxFiles: 5,
    }), new winston_1.default.transports.File({
        filename: path_1.default.join(config_1.default.logging.directory, 'trades.log'),
        level: 'info',
        format: logFormat,
        maxsize: 10485760,
        maxFiles: 10,
    }));
}
exports.logger = winston_1.default.createLogger({
    level: config_1.default.logging.level,
    format: logFormat,
    transports,
    exitOnError: false,
});
exports.tradeLogger = {
    buy: (data) => exports.logger.info('BUY_EXECUTED', data),
    sell: (data) => exports.logger.info('SELL_EXECUTED', data),
    detected: (data) => exports.logger.info('TOKEN_DETECTED', data),
    filtered: (data) => exports.logger.info('TOKEN_FILTERED', data),
    error: (data) => exports.logger.error('TRADE_ERROR', data),
};
exports.poolLogger = {
    created: (data) => exports.logger.info('POOL_CREATED', data),
    monitored: (data) => exports.logger.debug('POOL_MONITORED', data),
    error: (data) => exports.logger.error('POOL_ERROR', data),
};
exports.systemLogger = {
    startup: (data) => exports.logger.info('SYSTEM_STARTUP', data),
    shutdown: (data) => exports.logger.info('SYSTEM_SHUTDOWN', data),
    error: (data) => exports.logger.error('SYSTEM_ERROR', data),
    warning: (data) => exports.logger.warn('SYSTEM_WARNING', data),
};
function logWithContext(level, message, context = {}) {
    exports.logger.log(level, message, {
        ...context,
        timestamp: new Date().toISOString(),
        pid: process.pid,
    });
}
function logError(error, context = {}) {
    exports.logger.error(error.message, {
        ...context,
        stack: error.stack,
        name: error.name,
        timestamp: new Date().toISOString(),
    });
}
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map