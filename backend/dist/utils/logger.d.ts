import winston from 'winston';
export declare const logger: winston.Logger;
export declare const tradeLogger: {
    buy: (data: any) => winston.Logger;
    sell: (data: any) => winston.Logger;
    detected: (data: any) => winston.Logger;
    filtered: (data: any) => winston.Logger;
    error: (data: any) => winston.Logger;
};
export declare const poolLogger: {
    created: (data: any) => winston.Logger;
    monitored: (data: any) => winston.Logger;
    error: (data: any) => winston.Logger;
};
export declare const systemLogger: {
    startup: (data: any) => winston.Logger;
    shutdown: (data: any) => winston.Logger;
    error: (data: any) => winston.Logger;
    warning: (data: any) => winston.Logger;
};
export declare function logWithContext(level: string, message: string, context?: Record<string, any>): void;
export declare function logError(error: Error, context?: Record<string, any>): void;
export default logger;
//# sourceMappingURL=logger.d.ts.map