import { NotificationPayload } from '../types';
export declare class NotificationManager {
    private telegramBotToken?;
    private telegramChatId?;
    private discordWebhookUrl?;
    constructor();
    sendNotification(payload: NotificationPayload): Promise<void>;
    private sendTelegramMessage;
    private sendDiscordMessage;
    private formatTelegramMessage;
    private formatDiscordEmbed;
    private getEmojiForType;
    private getColorForType;
    notifyBuy(data: {
        tokenAddress: string;
        amount: number;
        price: number;
        signature: string;
    }): Promise<void>;
    notifySell(data: {
        tokenAddress: string;
        amount: number;
        price: number;
        pnl: number;
        signature: string;
    }): Promise<void>;
    notifyTokenDetected(data: {
        tokenAddress: string;
        poolId: string;
        liquidity: number;
        action: 'buying' | 'skipped';
        reason?: string;
    }): Promise<void>;
    notifyError(error: Error, context?: Record<string, any>): Promise<void>;
    notifyPriceAlert(data: {
        tokenAddress: string;
        type: 'take_profit' | 'stop_loss' | 'trailing_stop';
        currentPrice: number;
        targetPrice: number;
        percentage: number;
    }): Promise<void>;
}
export declare const notificationManager: NotificationManager;
export default notificationManager;
//# sourceMappingURL=notifications.d.ts.map