"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.connectionManager = exports.ConnectionManager = void 0;
exports.encryptPrivateKey = encryptPrivateKey;
exports.decryptPrivateKey = decryptPrivateKey;
exports.generateWallet = generateWallet;
exports.loadWalletFromData = loadWalletFromData;
exports.getSolBalance = getSolBalance;
exports.getTokenBalances = getTokenBalances;
exports.getUserWallet = getUserWallet;
exports.getTokenAccountAddress = getTokenAccountAddress;
exports.getTokenBalance = getTokenBalance;
exports.isValidPublicKey = isValidPublicKey;
exports.isValidPrivateKey = isValidPrivateKey;
exports.importWalletFromPrivateKey = importWalletFromPrivateKey;
exports.exportPrivateKey = exportPrivateKey;
exports.estimateTransactionFee = estimateTransactionFee;
const web3_js_1 = require("@solana/web3.js");
const spl_token_1 = require("@solana/spl-token");
const crypto_1 = __importDefault(require("crypto"));
const config_1 = __importDefault(require("../config"));
const logger_1 = require("./logger");
function encryptPrivateKey(privateKey, encryptionKey) {
    try {
        const algorithm = 'aes-256-cbc';
        const key = crypto_1.default.scryptSync(encryptionKey, 'salt', 32);
        const iv = crypto_1.default.randomBytes(16);
        const cipher = crypto_1.default.createCipheriv(algorithm, key, iv);
        let encrypted = cipher.update(Buffer.from(privateKey).toString('hex'), 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return iv.toString('hex') + ':' + encrypted;
    }
    catch (error) {
        return Buffer.from(privateKey).toString('hex');
    }
}
function decryptPrivateKey(encryptedPrivateKey, encryptionKey) {
    try {
        const algorithm = 'aes-256-cbc';
        const key = crypto_1.default.scryptSync(encryptionKey, 'salt', 32);
        const parts = encryptedPrivateKey.split(':');
        if (parts.length !== 2 || !parts[0] || !parts[1]) {
            return new Uint8Array(Buffer.from(encryptedPrivateKey, 'hex'));
        }
        const iv = Buffer.from(parts[0], 'hex');
        const encrypted = parts[1];
        const decipher = crypto_1.default.createDecipheriv(algorithm, key, iv);
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return new Uint8Array(Buffer.from(decrypted, 'hex'));
    }
    catch (error) {
        return new Uint8Array(Buffer.from(encryptedPrivateKey, 'hex'));
    }
}
function generateWallet() {
    const keypair = web3_js_1.Keypair.generate();
    const encryptedPrivateKey = encryptPrivateKey(keypair.secretKey, config_1.default.security.encryptionKey);
    return {
        keypair,
        walletData: {
            publicKey: keypair.publicKey.toString(),
            encryptedPrivateKey,
            createdAt: new Date(),
            updatedAt: new Date(),
        }
    };
}
function loadWalletFromData(walletData) {
    const privateKey = decryptPrivateKey(walletData.encryptedPrivateKey, config_1.default.security.encryptionKey);
    return web3_js_1.Keypair.fromSecretKey(privateKey);
}
class ConnectionManager {
    connections = [];
    currentIndex = 0;
    constructor() {
        this.connections.push(new web3_js_1.Connection(config_1.default.solana.rpcEndpoint, 'confirmed'));
        config_1.default.solana.backupRpcEndpoints.forEach(endpoint => {
            this.connections.push(new web3_js_1.Connection(endpoint, 'confirmed'));
        });
    }
    async getConnection() {
        const connection = this.connections[this.currentIndex];
        if (!connection) {
            throw new Error('No connections available');
        }
        try {
            await connection.getSlot();
            return connection;
        }
        catch (error) {
            logger_1.logger.warn(`Connection ${this.currentIndex} failed, trying next`, { error: error.message });
            this.currentIndex = (this.currentIndex + 1) % this.connections.length;
            if (this.currentIndex === 0) {
                throw new Error('All RPC connections failed');
            }
            return this.getConnection();
        }
    }
    async executeWithRetry(operation, maxRetries = 3) {
        let lastError = null;
        for (let i = 0; i < maxRetries; i++) {
            try {
                const connection = await this.getConnection();
                return await operation(connection);
            }
            catch (error) {
                lastError = error;
                logger_1.logger.warn(`Operation failed, attempt ${i + 1}/${maxRetries}`, { error: lastError.message });
                if (i < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
                }
            }
        }
        throw lastError || new Error('Operation failed after all retries');
    }
}
exports.ConnectionManager = ConnectionManager;
exports.connectionManager = new ConnectionManager();
async function getSolBalance(publicKey) {
    return exports.connectionManager.executeWithRetry(async (connection) => {
        const balance = await connection.getBalance(publicKey);
        return balance / web3_js_1.LAMPORTS_PER_SOL;
    });
}
async function getTokenBalances(publicKey) {
    return exports.connectionManager.executeWithRetry(async (connection) => {
        const tokenAccounts = await connection.getParsedTokenAccountsByOwner(publicKey, { programId: spl_token_1.TOKEN_PROGRAM_ID });
        return tokenAccounts.value.map(account => {
            const parsedInfo = account.account.data.parsed.info;
            return {
                mint: parsedInfo.mint,
                amount: parseInt(parsedInfo.tokenAmount.amount),
                decimals: parsedInfo.tokenAmount.decimals,
                uiAmount: parsedInfo.tokenAmount.uiAmount || 0,
            };
        }).filter(balance => balance.uiAmount > 0);
    });
}
async function getUserWallet(walletData) {
    const keypair = loadWalletFromData(walletData);
    const publicKey = keypair.publicKey;
    try {
        const [solBalance, tokenBalances] = await Promise.all([
            getSolBalance(publicKey),
            getTokenBalances(publicKey)
        ]);
        return {
            publicKey,
            keypair,
            balance: {
                sol: solBalance,
                tokens: tokenBalances,
            },
        };
    }
    catch (error) {
        (0, logger_1.logError)(error, { publicKey: publicKey.toString() });
        throw error;
    }
}
async function getTokenAccountAddress(walletPublicKey, tokenMint) {
    return (0, spl_token_1.getAssociatedTokenAddress)(tokenMint, walletPublicKey);
}
async function getTokenBalance(walletPublicKey, tokenMint) {
    return exports.connectionManager.executeWithRetry(async (connection) => {
        try {
            const tokenAccountAddress = await getTokenAccountAddress(walletPublicKey, tokenMint);
            const tokenAccount = await connection.getTokenAccountBalance(tokenAccountAddress);
            return tokenAccount.value.uiAmount || 0;
        }
        catch (error) {
            return 0;
        }
    });
}
function isValidPublicKey(publicKey) {
    try {
        new web3_js_1.PublicKey(publicKey);
        return true;
    }
    catch {
        return false;
    }
}
function isValidPrivateKey(privateKey) {
    try {
        if (privateKey.length !== 128)
            return false;
        const bytes = Buffer.from(privateKey, 'hex');
        if (bytes.length !== 64)
            return false;
        web3_js_1.Keypair.fromSecretKey(bytes);
        return true;
    }
    catch {
        return false;
    }
}
function importWalletFromPrivateKey(privateKeyHex) {
    if (!isValidPrivateKey(privateKeyHex)) {
        throw new Error('Invalid private key format');
    }
    const privateKeyBytes = Buffer.from(privateKeyHex, 'hex');
    return web3_js_1.Keypair.fromSecretKey(privateKeyBytes);
}
function exportPrivateKey(keypair) {
    return Buffer.from(keypair.secretKey).toString('hex');
}
async function estimateTransactionFee(connection) {
    const conn = connection || await exports.connectionManager.getConnection();
    try {
        const feeCalculator = await conn.getRecentBlockhash();
        return feeCalculator.feeCalculator.lamportsPerSignature / web3_js_1.LAMPORTS_PER_SOL;
    }
    catch (error) {
        (0, logger_1.logError)(error, { context: 'estimateTransactionFee' });
        return 0.000005;
    }
}
exports.default = {
    generateWallet,
    loadWalletFromData,
    getSolBalance,
    getTokenBalances,
    getUserWallet,
    getTokenBalance,
    isValidPublicKey,
    isValidPrivateKey,
    importWalletFromPrivateKey,
    exportPrivateKey,
    connectionManager: exports.connectionManager,
};
//# sourceMappingURL=wallet.js.map