import { EventEmitter } from 'events';
export declare class SniperBot extends EventEmitter {
    private poolMonitor;
    private tokenFilter;
    private buyExecutor;
    private sellExecutor;
    private database;
    private activePositions;
    private priceAlerts;
    private isRunning;
    constructor();
    private setupEventHandlers;
    start(): Promise<void>;
    stop(): Promise<void>;
    private handleTokenDetected;
    private executeBuysForAllUsers;
    private createPosition;
    private setupPriceAlerts;
    private setupPriceMonitoring;
    private checkPriceAlerts;
    private getCurrentTokenPrice;
    private getPriceViaQuote;
    private shouldTriggerAlert;
    private triggerPriceAlert;
    private executeAutoSell;
    private loadActivePositions;
    private loadPriceAlerts;
    private getActiveUsers;
    private chunkArray;
    addUserWallet(userId: string, privateKeyHex: string): Promise<void>;
    removeUserWallet(userId: string, walletAddress: string): Promise<void>;
    getStatus(): any;
}
export declare const sniperBot: SniperBot;
export default sniperBot;
//# sourceMappingURL=sniper.d.ts.map