#!/usr/bin/env ts-node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const userManager_1 = require("./utils/userManager");
const wallet_1 = require("./utils/wallet");
const config_1 = __importDefault(require("./config"));
async function setupTestUser() {
    console.log('🎯 Sol Bullet - Setting up test user for automated sniper\n');
    try {
        console.log('1. Creating new user...');
        const uniqueEmail = `sniper-${Date.now()}@solbullet.com`;
        const user = await userManager_1.userManager.createUser(uniqueEmail);
        console.log(`   ✅ User created: ${user.id}`);
        console.log('\n2. Generating wallet for user...');
        const { keypair } = (0, wallet_1.generateWallet)();
        const privateKeyBytes = Array.from(keypair.secretKey);
        console.log(`   ✅ Wallet generated: ${keypair.publicKey.toString()}`);
        console.log(`   🔑 Private Key (raw bytes): [${privateKeyBytes.join(', ')}]`);
        const walletData = await userManager_1.userManager.addWalletToUser(user.id, Buffer.from(keypair.secretKey).toString('hex'));
        console.log(`   ✅ Wallet added to user: ${walletData.id}`);
        console.log('\n3. Configuring automated trading settings...');
        await userManager_1.userManager.updateUserTradingSettings(user.id, {
            buyAmount: config_1.default.trading.defaultBuyAmountSol,
            slippage: config_1.default.trading.defaultSlippagePercent,
            autoSell: true,
            takeProfitPercent: config_1.default.profitLoss.takeProfitPercent,
            stopLossPercent: config_1.default.profitLoss.stopLossPercent,
        });
        console.log(`   ✅ Auto-trading configured:`);
        console.log(`      💰 Buy Amount: ${config_1.default.trading.defaultBuyAmountSol} SOL`);
        console.log(`      📈 Take Profit: ${config_1.default.profitLoss.takeProfitPercent}%`);
        console.log(`      📉 Stop Loss: ${config_1.default.profitLoss.stopLossPercent}%`);
        console.log('\n4. Enabling automated sniper...');
        await userManager_1.userManager.enableSniperForUser(user.id);
        console.log('   ✅ Automated sniper enabled for user');
        console.log('\n🎯 SETUP COMPLETE!');
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        console.log(`👤 User ID: ${user.id}`);
        console.log(`💰 Wallet: ${keypair.publicKey.toString()}`);
        console.log(`🔑 Private Key: [${privateKeyBytes.join(', ')}]`);
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        console.log('\n📋 NEXT STEPS:');
        console.log('1. Fund the wallet with SOL for trading');
        console.log('2. Start the sniper bot: npm run dev');
        console.log('3. The bot will automatically:');
        console.log('   🔍 Monitor for new tokens on Raydium');
        console.log('   ⚡ Buy tokens that pass safety filters');
        console.log('   📈 Sell automatically when profit/loss targets hit');
        console.log('   🔄 All trades executed via Jupiter for best prices');
        console.log('\n🚀 Your automated sniper is ready!');
        const userInfo = {
            userId: user.id,
            email: user.email,
            walletId: walletData.id,
            publicKey: keypair.publicKey.toString(),
            privateKeyBytes: privateKeyBytes,
            privateKeyHex: Buffer.from(keypair.secretKey).toString('hex'),
            settings: {
                buyAmount: config_1.default.trading.defaultBuyAmountSol,
                takeProfitPercent: config_1.default.profitLoss.takeProfitPercent,
                stopLossPercent: config_1.default.profitLoss.stopLossPercent,
            },
        };
        const fs = await Promise.resolve().then(() => __importStar(require('fs')));
        await fs.promises.writeFile('user-setup.json', JSON.stringify(userInfo, null, 2));
        console.log('\n💾 User info saved to user-setup.json');
    }
    catch (error) {
        console.error('❌ Setup failed:', error);
        process.exit(1);
    }
}
setupTestUser().catch((error) => {
    console.error('Setup error:', error);
    process.exit(1);
});
//# sourceMappingURL=setup-user.js.map