'use strict';(function(q){var C=String.fromCharCode,v={}.toString,w=q.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uff<PERSON>,J=w?v.call(w):"",r=q.Uint8Array,K=r?v.call(ArrayBuffer.prototype):"",n=q.Buffer,a=new Uint16Array(32);try{!n&&q.require&&(n=q.require("Buffer"));var D=n.prototype;var L=n?v.call(D):""}catch(c){}var x=n.allocUnsafe,z=!!r&&!n,M=!n||!!r&&r.prototype.isPrototypeOf(D),A=q.TextEncoder,B=q.TextDecoder;if(z||n){var y=function(c){c.TextDecoder=B||E;c.TextEncoder=A||F;c!==q&&(c.decode=B?G(new B,"decode"):H,c.encode=A?G(new A,
"encode"):I);return c},G=function(c,h,d){d=c[h];return function(){return d.apply(c,arguments)}},I=function(c){c=void 0===c?"":""+c;var h=c.length|0,d=z?new r((h<<1)+8|0):x?x((h<<1)+8|0):new n((h<<1)+8|0),b,e=0,u=!1;for(b=0;b<h;b=b+1|0,e=e+1|0){var g=c.charCodeAt(b)|0;if(127>=g)d[e]=g;else{if(2047>=g)d[e]=192|g>>6;else{a:{if(55296<=g)if(56320>g){var f=c.charCodeAt(b=b+1|0)|0;if(56320<=f&&57343>=f){g=(g<<10)+f-56613888|0;if(65535<g){d[e]=240|g>>18;d[e=e+1|0]=128|g>>12&63;d[e=e+1|0]=128|g>>6&63;d[e=
e+1|0]=128|g&63;continue}break a}g=65533}else 57343>=g&&(g=65533);!u&&b<<1<e&&b<<1<(e-7|0)&&(u=!0,f=z?new r(3*h):x?x(3*h):new n(3*h),f.set(d),d=f)}d[e]=224|g>>12;d[e=e+1|0]=128|g>>6&63}d[e=e+1|0]=128|g&63}}return d.subarray(0,e)},F=function(){},H=function(c){var h=c&&c.buffer||c,d=v.call(h);if(d!==K&&d!==L&&d!==J&&"[object ArrayBuffer]"!==d&&void 0!==c)throw TypeError("Failed to execute 'decode' on 'TextDecoder': The provided value is not of type '(ArrayBuffer or ArrayBufferView)'");c=M?new r(h):
h||[];d=h="";for(var b=0,e=c.length|0,u=e-32|0,g,f,k=0,t=0,m,l=0,p=-1;b<e;){for(g=b<=u?32:e-b|0;l<g;b=b+1|0,l=l+1|0){f=c[b]&255;switch(f>>4){case 15:m=c[b=b+1|0]&255;if(2!==m>>6||247<f){b=b-1|0;break}k=(f&7)<<6|m&63;t=5;f=256;case 14:m=c[b=b+1|0]&255,k<<=6,k|=(f&15)<<6|m&63,t=2===m>>6?t+4|0:24,f=f+256&768;case 13:case 12:m=c[b=b+1|0]&255,k<<=6,k|=(f&31)<<6|m&63,t=t+7|0,b<e&&2===m>>6&&k>>t&&1114112>k?(f=k,k=k-65536|0,0<=k&&(p=(k>>10)+55296|0,f=(k&1023)+56320|0,31>l?(a[l]=p,l=l+1|0,p=-1):(m=p,p=f,f=
m))):(f>>=8,b=b-f-1|0,f=65533),k=t=0,g=b<=u?32:e-b|0;default:a[l]=f;continue;case 11:case 10:case 9:case 8:}a[l]=65533}d+=C(a[0],a[1],a[2],a[3],a[4],a[5],a[6],a[7],a[8],a[9],a[10],a[11],a[12],a[13],a[14],a[15],a[16],a[17],a[18],a[19],a[20],a[21],a[22],a[23],a[24],a[25],a[26],a[27],a[28],a[29],a[30],a[31]);32>l&&(d=d.slice(0,l-32|0));if(b<e){if(a[0]=p,l=~p>>>31,p=-1,d.length<h.length)continue}else-1!==p&&(d+=C(p));h+=d;d=""}return h},E=function(){};E.prototype.decode=H;w=F.prototype;w.encode=I;"object"===
typeof exports&&"undefined"!==typeof module?y(module.exports):typeof define==typeof y&&"function"===typeof define&&define.amd?define(function(){return y({})}):y(q)}})("undefined"==typeof global?"undefined"==typeof self?this||{}:self:global);//AnonyCo
//# sourceMappingURL=https://cdn.jsdelivr.net/gh/AnonyCo/FastestSmallestTextEncoderDecoder/NodeJS/EncoderAndDecoderNodeJS.min.js.map
