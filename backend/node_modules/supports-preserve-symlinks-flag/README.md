# node-supports-preserve-symlinks-flag <sup>[![Version Badge][npm-version-svg]][package-url]</sup>

[![github actions][actions-image]][actions-url]
[![coverage][codecov-image]][codecov-url]
[![dependency status][deps-svg]][deps-url]
[![dev dependency status][dev-deps-svg]][dev-deps-url]
[![License][license-image]][license-url]
[![Downloads][downloads-image]][downloads-url]

[![npm badge][npm-badge-png]][package-url]

Determine if the current node version supports the `--preserve-symlinks` flag.

## Example

```js
var supportsPreserveSymlinks = require('node-supports-preserve-symlinks-flag');
var assert = require('assert');

assert.equal(supportsPreserveSymlinks, null); // in a browser
assert.equal(supportsPreserveSymlinks, false); // in node < v6.2
assert.equal(supportsPreserveSymlinks, true); // in node v6.2+
```

## Tests
Simply clone the repo, `npm install`, and run `npm test`

[package-url]: https://npmjs.org/package/node-supports-preserve-symlinks-flag
[npm-version-svg]: https://versionbadg.es/inspect-js/node-supports-preserve-symlinks-flag.svg
[deps-svg]: https://david-dm.org/inspect-js/node-supports-preserve-symlinks-flag.svg
[deps-url]: https://david-dm.org/inspect-js/node-supports-preserve-symlinks-flag
[dev-deps-svg]: https://david-dm.org/inspect-js/node-supports-preserve-symlinks-flag/dev-status.svg
[dev-deps-url]: https://david-dm.org/inspect-js/node-supports-preserve-symlinks-flag#info=devDependencies
[npm-badge-png]: https://nodei.co/npm/node-supports-preserve-symlinks-flag.png?downloads=true&stars=true
[license-image]: https://img.shields.io/npm/l/node-supports-preserve-symlinks-flag.svg
[license-url]: LICENSE
[downloads-image]: https://img.shields.io/npm/dm/node-supports-preserve-symlinks-flag.svg
[downloads-url]: https://npm-stat.com/charts.html?package=node-supports-preserve-symlinks-flag
[codecov-image]: https://codecov.io/gh/inspect-js/node-supports-preserve-symlinks-flag/branch/main/graphs/badge.svg
[codecov-url]: https://app.codecov.io/gh/inspect-js/node-supports-preserve-symlinks-flag/
[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/node-supports-preserve-symlinks-flag
[actions-url]: https://github.com/inspect-js/node-supports-preserve-symlinks-flag/actions
