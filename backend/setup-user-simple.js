const { Keypair } = require('@solana/web3.js');
const crypto = require('crypto');

console.log('🎯 Sol Bullet - Simple User Setup for Automated Sniper\n');

// Generate a new wallet
const keypair = Keypair.generate();
const publicKey = keypair.publicKey.toString();
const privateKeyBytes = Array.from(keypair.secretKey);
const privateKeyHex = Buffer.from(keypair.secretKey).toString('hex');

// Generate user ID
const userId = `user-${Date.now()}`;

console.log('✅ AUTOMATED SNIPER USER CREATED!');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log(`👤 User ID: ${userId}`);
console.log(`💰 Wallet Address: ${publicKey}`);
console.log(`🔑 Private Key (bytes): [${privateKeyBytes.join(', ')}]`);
console.log(`🔑 Private Key (hex): ${privateKeyHex}`);
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

console.log('\n🎯 AUTOMATED SNIPER CONFIGURATION:');
console.log('💰 Buy Amount: 0.1 SOL per token');
console.log('📈 Take Profit: 100% (sell when doubled)');
console.log('📉 Stop Loss: 50% (sell if down 50%)');
console.log('🔍 Min Liquidity: 5 SOL');
console.log('⚡ All trades via Jupiter for best prices');

console.log('\n📋 NEXT STEPS:');
console.log('1. Fund the wallet with SOL for automated trading');
console.log('2. Save the private key securely');
console.log('3. Start the automated sniper bot');
console.log('4. Watch it trade automatically 24/7!');

// Save to file
const userInfo = {
  userId: userId,
  publicKey: publicKey,
  privateKeyBytes: privateKeyBytes,
  privateKeyHex: privateKeyHex,
  settings: {
    buyAmount: 0.1,
    takeProfitPercent: 100,
    stopLossPercent: 50,
    minLiquidity: 5,
  },
  instructions: [
    'Fund the wallet with SOL',
    'Start the sniper bot with: npm run dev',
    'Bot will automatically trade when new tokens are detected',
    'All trades executed via Jupiter for best prices',
  ],
};

const fs = require('fs');
fs.writeFileSync('automated-sniper-user.json', JSON.stringify(userInfo, null, 2));

console.log('\n💾 User info saved to: automated-sniper-user.json');
console.log('\n🚀 Your automated sniper is ready to start trading!');
console.log('\n🎯 TO START AUTOMATED TRADING:');
console.log('   1. Fund wallet: Send SOL to the address above');
console.log('   2. Start bot: npm run dev (or node dist/server.js)');
console.log('   3. Watch logs: Bot will show all automated actions');
console.log('   4. Profit: Bot trades automatically 24/7!');
