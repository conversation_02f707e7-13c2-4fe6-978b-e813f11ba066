2025-08-13 13:25:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 69Z8yLw5CFMjVrSZrtFREfTEWJS13UTuQNweq7GRXPiL: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f4dafc56-2e41-4066-a8d6-cc0fef93cf04\" } \r\n","poolAddress":"69Z8yLw5CFMjVrSZrtFREfTEWJS13UTuQNweq7GRXPiL"}
2025-08-13 13:25:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2iv1vgdmjpDwTZHqZLggyDHDeeHPP2BuGqp5XEnRGWa8: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"acc3e0c5-9202-4b89-97d2-ac2ce306f507\" } \r\n","poolAddress":"2iv1vgdmjpDwTZHqZLggyDHDeeHPP2BuGqp5XEnRGWa8"}
2025-08-13 13:25:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8GGFAnaTziDw19unw6nWeNnYRkukJGDjfBDafUrU6qek: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"162c9bd1-233c-42fb-b69a-5406f30f42a6\" } \r\n","poolAddress":"8GGFAnaTziDw19unw6nWeNnYRkukJGDjfBDafUrU6qek"}
2025-08-13 13:25:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account sVvHqrZ2pmdP7gufgmMp9kdUk7LivkmtqxNiDwC8yNF: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"9f1090b7-2996-4999-90ee-19e5c2509a42\" } \r\n","poolAddress":"sVvHqrZ2pmdP7gufgmMp9kdUk7LivkmtqxNiDwC8yNF"}
2025-08-13 13:25:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account B6tkgSeaj3RGE7vew6cPVGo3Yti1umBmPbGEEB6CDaT7: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1cc8bd25-fa53-439e-80ac-7204d0968f15\" } \r\n","poolAddress":"B6tkgSeaj3RGE7vew6cPVGo3Yti1umBmPbGEEB6CDaT7"}
2025-08-13 13:25:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account asdEJnE7osjgnSyQkSZJ3e5YezbmXuDQPiyeyiBxoUm: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c03b4041-a4ec-4446-be24-3b7dc162ad88\" } \r\n","poolAddress":"asdEJnE7osjgnSyQkSZJ3e5YezbmXuDQPiyeyiBxoUm"}
2025-08-13 13:25:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account ASqRN3v8eMX2Pv3RNbggG7oX2p5vS4D3nJ9amPfUgHxL: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"9471353d-4b1e-4ff9-b08d-4eb45525ae37\" } \r\n","poolAddress":"ASqRN3v8eMX2Pv3RNbggG7oX2p5vS4D3nJ9amPfUgHxL"}
2025-08-13 13:25:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account HDt4asuvNjWn1Y5mV2R3sPXf54hC911ANYihU8HtmK7z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"38ff897c-af73-494a-bdd6-4a473f4e62be\" } \r\n","poolAddress":"HDt4asuvNjWn1Y5mV2R3sPXf54hC911ANYihU8HtmK7z"}
2025-08-13 13:25:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8gmUDfwjLVCgsrLAaXiJWjopsSu2gAs39puu71HBn4E5: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"dacfabac-a324-4c61-9dda-4ca6c765f220\" } \r\n","poolAddress":"8gmUDfwjLVCgsrLAaXiJWjopsSu2gAs39puu71HBn4E5"}
2025-08-13 13:25:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account 97ttdcb483gVneg9oMCfCHDuzvnmQ4JaZGBvkJ8mdFY4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7f79f4ef-e116-4f03-8f09-1a38f325ad40\" } \r\n","poolAddress":"97ttdcb483gVneg9oMCfCHDuzvnmQ4JaZGBvkJ8mdFY4"}
2025-08-13 13:25:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account B2MYDuq9EQVe325Vve2441doZf6Y1B4ZnaRv65fbTuu8: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"62d979ba-7360-4d2f-9439-b6bde64c6d74\" } \r\n","poolAddress":"B2MYDuq9EQVe325Vve2441doZf6Y1B4ZnaRv65fbTuu8"}
2025-08-13 13:25:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account H5TmGsYpr3NmFk2pYhFMgXx7qVBQHjEcZUHCvjCBCSUx: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"cce4bd7b-0341-4507-b189-6ae5018d5890\" } \r\n","poolAddress":"H5TmGsYpr3NmFk2pYhFMgXx7qVBQHjEcZUHCvjCBCSUx"}
2025-08-13 13:25:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account EtZtMKpGofXEJEbWv5R9UWgbLuLK5scDcTn44VNqG4HZ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b7e83eb8-429f-4dc0-8e31-82dac6f3942c\" } \r\n","poolAddress":"EtZtMKpGofXEJEbWv5R9UWgbLuLK5scDcTn44VNqG4HZ"}
2025-08-13 13:25:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account CfKL31XugvcjPtjtJ1Xj2m2MhcfrBynSqPEaXd2pemcU: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f5336ab0-dd3f-40e8-bd1e-01df9a6ed2aa\" } \r\n","poolAddress":"CfKL31XugvcjPtjtJ1Xj2m2MhcfrBynSqPEaXd2pemcU"}
2025-08-13 13:25:13 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:25:13 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:25:13 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:25:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCRty6J2toqf8EAiQSNpAeG21Qd1p8bymJpWWkBkAXkz: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1cf383b7-7c7b-408a-8cd0-e95eb62dfd63\" } \r\n","poolAddress":"HCRty6J2toqf8EAiQSNpAeG21Qd1p8bymJpWWkBkAXkz"}
2025-08-13 13:25:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account E8At72o2tREFGthgRJXgJTwN7HLainPpcsB9dg9CwS5o: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b552afb0-057c-42fb-ab90-c2589944b3ea\" } \r\n","poolAddress":"E8At72o2tREFGthgRJXgJTwN7HLainPpcsB9dg9CwS5o"}
2025-08-13 13:25:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4XCAQuvRXAZVf3AZE13cvyCvrJHnt4uWtLaA7Hd3DTmd: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5b9b94b0-0c00-4812-9073-5bba67d989c2\" } \r\n","poolAddress":"4XCAQuvRXAZVf3AZE13cvyCvrJHnt4uWtLaA7Hd3DTmd"}
2025-08-13 13:25:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account DThR9WwS7qVUFswtabmbX7SRPZejpJgdy6CTB631kpgF: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"077baf61-dd39-41c6-b89e-c6f6b9db69a3\" } \r\n","poolAddress":"DThR9WwS7qVUFswtabmbX7SRPZejpJgdy6CTB631kpgF"}
2025-08-13 13:25:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3CdP7qcdSTQKEhE34gWGd3UcwBYLrFGB5X8G9eszWPL3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7fd9fa26-4605-4b23-bc64-2f587e9e55dc\" } \r\n","poolAddress":"3CdP7qcdSTQKEhE34gWGd3UcwBYLrFGB5X8G9eszWPL3"}
2025-08-13 13:25:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6CFSyWA3F9bcDkttamjmHpois7YKF7ZuPqx9PyQKbDpq: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"03cf73ee-361b-49e4-bd1c-4ab28e38bf04\" } \r\n","poolAddress":"6CFSyWA3F9bcDkttamjmHpois7YKF7ZuPqx9PyQKbDpq"}
2025-08-13 13:25:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account B5uP8Zincgjc6psTzy3poAXTWEX6ZbHz6nJYgMVzVrxt: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4bba35ba-27bd-4134-9952-2b813e708300\" } \r\n","poolAddress":"B5uP8Zincgjc6psTzy3poAXTWEX6ZbHz6nJYgMVzVrxt"}
2025-08-13 13:25:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4jSC2pPsS4oM9BAzj3SzsbWBofKVQz66RfeQZrq3sDWy: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8c04f074-bf3d-4331-a6b5-1be0b55f4ff3\" } \r\n","poolAddress":"4jSC2pPsS4oM9BAzj3SzsbWBofKVQz66RfeQZrq3sDWy"}
2025-08-13 13:25:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4jLmnzy9J8vJNHWCAkm36zLLA9xV3uzjcTVk2YjyGP7f: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"147debbb-3f53-4365-97fd-5e1ffd5fcdb1\" } \r\n","poolAddress":"4jLmnzy9J8vJNHWCAkm36zLLA9xV3uzjcTVk2YjyGP7f"}
2025-08-13 13:25:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8AzFCbbvgUeyAXRyAMt6YDE5nps33zQctPVSrLp3Vu5: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6fff16f8-269b-4bae-a9f5-d3190cd2c928\" } \r\n","poolAddress":"8AzFCbbvgUeyAXRyAMt6YDE5nps33zQctPVSrLp3Vu5"}
2025-08-13 13:25:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account EhiKZ9r8VmNZ3Cd73G6jFXrV93vwfa4zoC8s26H7EMbF: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"02e706cb-7de1-4414-b866-62be90f07311\" } \r\n","poolAddress":"EhiKZ9r8VmNZ3Cd73G6jFXrV93vwfa4zoC8s26H7EMbF"}
2025-08-13 13:25:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9zDRQZGpTitrCvK5LoXuJdygjYCU7t3wLLwbFBS18H58: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fde005ff-9ec6-4827-824a-f63a7020a088\" } \r\n","poolAddress":"9zDRQZGpTitrCvK5LoXuJdygjYCU7t3wLLwbFBS18H58"}
2025-08-13 13:25:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account EtZtMKpGofXEJEbWv5R9UWgbLuLK5scDcTn44VNqG4HZ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ed50e359-50da-419d-8d49-48edf0584e0c\" } \r\n","poolAddress":"EtZtMKpGofXEJEbWv5R9UWgbLuLK5scDcTn44VNqG4HZ"}
2025-08-13 13:25:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account BvvefSzw1xqW7NSYbfSfspRyW9P79f3QgmD1PfqLEMaL: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"26585b93-048d-4a2b-a236-8f1a1c6b4685\" } \r\n","poolAddress":"BvvefSzw1xqW7NSYbfSfspRyW9P79f3QgmD1PfqLEMaL"}
2025-08-13 13:25:13 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:25:14 [ERROR]: All RPC connections failed {"context":"pollForNewPools","stack":"Error: All RPC connections failed\n    at ConnectionManager.getConnection (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:112:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:124:28)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"Error"}
2025-08-13 13:25:14 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9YXBNiKTfsGXvVQvnt6KhpK3HarrT9dSq9e4fCW7D2Y: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2ca9e482-82ef-4a71-8381-ab2ebf923860\" } \r\n","poolAddress":"9YXBNiKTfsGXvVQvnt6KhpK3HarrT9dSq9e4fCW7D2Y"}
2025-08-13 13:25:14 [ERROR]: Parse pool data error: {"error":"failed to get info about account DA4WqYCg4Z1KrsSvSUFwkNvBRG4HjXtAGVH3yGNVXbXW: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"352105f7-d0af-4a8c-9e6d-d2e777fb8520\" } \r\n","poolAddress":"DA4WqYCg4Z1KrsSvSUFwkNvBRG4HjXtAGVH3yGNVXbXW"}
2025-08-13 13:25:14 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9YXBNiKTfsGXvVQvnt6KhpK3HarrT9dSq9e4fCW7D2Y: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"78e0ef97-f68b-4146-82df-5d2257daa22e\" } \r\n","poolAddress":"9YXBNiKTfsGXvVQvnt6KhpK3HarrT9dSq9e4fCW7D2Y"}
2025-08-13 13:25:14 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3d7PRDYq3CvRxFBoXrYeKr3DYYco2AnYupv9D9bAUoyH: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"362c574c-b8b5-4d02-9074-c01509e7390c\" } \r\n","poolAddress":"3d7PRDYq3CvRxFBoXrYeKr3DYYco2AnYupv9D9bAUoyH"}
2025-08-13 13:25:14 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9YXBNiKTfsGXvVQvnt6KhpK3HarrT9dSq9e4fCW7D2Y: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c4dd75e1-7b11-4a87-8f25-fef0ab25e1e4\" } \r\n","poolAddress":"9YXBNiKTfsGXvVQvnt6KhpK3HarrT9dSq9e4fCW7D2Y"}
2025-08-13 13:25:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account GwcNkpNdzzGGvBVQhw1E1WJbsjVfZ6WpYVqT6hQFfY6B: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a30a3b68-8e27-4263-9858-1cbcace54a6a\" } \r\n","poolAddress":"GwcNkpNdzzGGvBVQhw1E1WJbsjVfZ6WpYVqT6hQFfY6B"}
2025-08-13 13:25:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account YPj9CPjjnJidYYokVca5UJL48WGQTKKMAXup2fNF5Eh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f2a2d13e-46cb-4be8-ad47-4c865050fd52\" } \r\n","poolAddress":"YPj9CPjjnJidYYokVca5UJL48WGQTKKMAXup2fNF5Eh"}
2025-08-13 13:25:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2N6SHfcg2U8KPPYujRGMzBjAmW2NZUuWnRWRZVCihBxw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"554cba94-6572-4883-804e-86ce0316be10\" } \r\n","poolAddress":"2N6SHfcg2U8KPPYujRGMzBjAmW2NZUuWnRWRZVCihBxw"}
2025-08-13 13:25:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account AqCWoTYJU6hXES9G6aVbe1B3TZYdUHT1rURd6DLcoSC3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b1b948f9-b335-4e67-898b-56f346dfd4bd\" } \r\n","poolAddress":"AqCWoTYJU6hXES9G6aVbe1B3TZYdUHT1rURd6DLcoSC3"}
2025-08-13 13:25:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account EYErUp5muPYEEkeaUCY22JibeZX7E9UuMcJFZkmNAN7c: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d0e82d5e-891b-454f-a40b-************\" } \r\n","poolAddress":"EYErUp5muPYEEkeaUCY22JibeZX7E9UuMcJFZkmNAN7c"}
2025-08-13 13:25:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9YXBNiKTfsGXvVQvnt6KhpK3HarrT9dSq9e4fCW7D2Y: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7bcd5f09-0286-4537-85c4-e803f585972c\" } \r\n","poolAddress":"9YXBNiKTfsGXvVQvnt6KhpK3HarrT9dSq9e4fCW7D2Y"}
2025-08-13 13:25:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account ECKWo8SwerqftVJ9bd2fCkWVJBYL9fUgotYhTevunxW: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2b33c9c5-cd97-47c6-9594-4112329e1199\" } \r\n","poolAddress":"ECKWo8SwerqftVJ9bd2fCkWVJBYL9fUgotYhTevunxW"}
2025-08-13 13:25:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6R3yY5hpJ2RugzWx6ex4SVsoReK87jEoBu2hWh5XhuE2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cc237c11-e975-46b6-8605-ba02d5bb66f1\" } \r\n","poolAddress":"6R3yY5hpJ2RugzWx6ex4SVsoReK87jEoBu2hWh5XhuE2"}
2025-08-13 13:25:21 [ERROR]: Parse pool data error: {"error":"failed to get info about account GzdVSyAjTfkUtMAXcqTWbPkH1RSQT8VKfGJwdg1f62U8: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c6d35b52-09b3-4c5f-bc20-c3a177639307\" } \r\n","poolAddress":"GzdVSyAjTfkUtMAXcqTWbPkH1RSQT8VKfGJwdg1f62U8"}
2025-08-13 13:25:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account Eo13eh2Tqmvy2gXdi8bRNzgGVuLtRTAUx9G8X5JUs71U: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4f000ad4-8a86-4054-b541-a05c3ec6dbc1\" } \r\n","poolAddress":"Eo13eh2Tqmvy2gXdi8bRNzgGVuLtRTAUx9G8X5JUs71U"}
2025-08-13 13:25:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account HrriFmtKid79BnKaSRW71QK47xXik2nnfuXKqLnYVH28: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ebcbcc13-bc56-409d-ab82-ccb5d013b31a\" } \r\n","poolAddress":"HrriFmtKid79BnKaSRW71QK47xXik2nnfuXKqLnYVH28"}
2025-08-13 13:25:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account Eo13eh2Tqmvy2gXdi8bRNzgGVuLtRTAUx9G8X5JUs71U: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cebb88cf-5c37-4050-abe2-f184aee73cc6\" } \r\n","poolAddress":"Eo13eh2Tqmvy2gXdi8bRNzgGVuLtRTAUx9G8X5JUs71U"}
2025-08-13 13:25:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account BLmjb53P73fcFwdBBDCJygV3zCRcYw8qk1myUB1BAQrs: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"86298c47-9a25-4aa4-9254-20f8311fd308\" } \r\n","poolAddress":"BLmjb53P73fcFwdBBDCJygV3zCRcYw8qk1myUB1BAQrs"}
2025-08-13 13:25:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account DvGzkBHQ6qbsNJCx7VtbEN6qQzjtNW1xYVJ2ijGkbaRy: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"13f4f951-d16e-4786-9389-7736723c5a28\" } \r\n","poolAddress":"DvGzkBHQ6qbsNJCx7VtbEN6qQzjtNW1xYVJ2ijGkbaRy"}
2025-08-13 13:25:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account CF5vPLDsrWXp1bekmwLohDzP7MzKEVubE2Bh4M2w7KsR: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f0630bd8-3dba-43dd-a987-da65af3acfa1\" } \r\n","poolAddress":"CF5vPLDsrWXp1bekmwLohDzP7MzKEVubE2Bh4M2w7KsR"}
2025-08-13 13:25:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6QjNHe3ubr859QKBSuhYwR3Daf25Qxv8afZ9WCTRSve7: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0f5b0eb6-de0b-4284-93ef-651038a0aa6c\" } \r\n","poolAddress":"6QjNHe3ubr859QKBSuhYwR3Daf25Qxv8afZ9WCTRSve7"}
2025-08-13 13:25:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3Etf9yspqbLbhoyqMi9VkX9XzPnyCRcsCpTmv66Kr7XA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"179e2ad1-a047-4536-8084-49c465fab676\" } \r\n","poolAddress":"3Etf9yspqbLbhoyqMi9VkX9XzPnyCRcsCpTmv66Kr7XA"}
2025-08-13 13:25:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3izxtKafzMbkEctj4Mj2u1QGcT2QuKTPpNxR7jerbAdy: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c82457d1-dac6-45e2-968a-f2661b59bc39\" } \r\n","poolAddress":"3izxtKafzMbkEctj4Mj2u1QGcT2QuKTPpNxR7jerbAdy"}
2025-08-13 13:25:32 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6B7HaigdotRyb3h5YQ487zfnTueEmUhXLLPccaaNq1ei: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"94eef6b8-3177-4fed-a81b-e3401db1cbb5\" } \r\n","poolAddress":"6B7HaigdotRyb3h5YQ487zfnTueEmUhXLLPccaaNq1ei"}
2025-08-13 13:25:33 [ERROR]: Parse pool data error: {"error":"failed to get info about account EYErUp5muPYEEkeaUCY22JibeZX7E9UuMcJFZkmNAN7c: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"120becba-1486-4c68-914a-957fc968dc40\" } \r\n","poolAddress":"EYErUp5muPYEEkeaUCY22JibeZX7E9UuMcJFZkmNAN7c"}
2025-08-13 13:25:33 [ERROR]: Parse pool data error: {"error":"failed to get info about account CjXkgjMLBLfE62gQFoJptoNjCTSm1fNFnPvZrpBDc6u1: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9ae93cc0-ea39-4bd5-843f-************\" } \r\n","poolAddress":"CjXkgjMLBLfE62gQFoJptoNjCTSm1fNFnPvZrpBDc6u1"}
2025-08-13 13:25:33 [ERROR]: All RPC connections failed {"context":"pollForNewPools","stack":"Error: All RPC connections failed\n    at ConnectionManager.getConnection (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:112:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:124:28)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"Error"}
2025-08-13 13:25:35 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58qMXs295XW8arxxdPPn6nNWDYRqPVUSv5upv5SBT3Cf: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6625732b-039a-4158-a7cc-26b0fd3ff52a\" } \r\n","poolAddress":"58qMXs295XW8arxxdPPn6nNWDYRqPVUSv5upv5SBT3Cf"}
2025-08-13 13:25:35 [ERROR]: Parse pool data error: {"error":"failed to get info about account ********************************************: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f191907e-cbb5-4085-af9d-54cbf3f32a89\" } \r\n","poolAddress":"********************************************"}
2025-08-13 13:25:35 [ERROR]: All RPC connections failed {"context":"pollForNewPools","stack":"Error: All RPC connections failed\n    at ConnectionManager.getConnection (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:112:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:124:28)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"Error"}
2025-08-13 13:25:35 [ERROR]: All RPC connections failed {"context":"pollForNewPools","stack":"Error: All RPC connections failed\n    at ConnectionManager.getConnection (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:112:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:124:28)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"Error"}
2025-08-13 13:25:37 [ERROR]: All RPC connections failed {"context":"pollForNewPools","stack":"Error: All RPC connections failed\n    at ConnectionManager.getConnection (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:112:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:124:28)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"Error"}
2025-08-13 13:25:37 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5Qtn7FFKmVH4Brx3V1cR8E4mot2V3eSmvFRBpmKjNy35: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0840fe71-3d18-4f43-8b13-4ce6b0c548c7\" } \r\n","poolAddress":"5Qtn7FFKmVH4Brx3V1cR8E4mot2V3eSmvFRBpmKjNy35"}
2025-08-13 13:25:38 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6B7HaigdotRyb3h5YQ487zfnTueEmUhXLLPccaaNq1ei: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6d666a55-7f02-4af5-b6ec-d8137b79cb67\" } \r\n","poolAddress":"6B7HaigdotRyb3h5YQ487zfnTueEmUhXLLPccaaNq1ei"}
2025-08-13 13:25:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account 45FxrSyez1bmh7SXRVsckNDtp19qqdp2xJJyGuMnG7Wa: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5d3ae1ce-4cc5-47fc-9189-238b4287a249\" } \r\n","poolAddress":"45FxrSyez1bmh7SXRVsckNDtp19qqdp2xJJyGuMnG7Wa"}
2025-08-13 13:25:41 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58dnvLJxmJ7RHBxSMA8bLAbQWPdn4BxkDCDWPeJQ6UY8: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cf0a5c9b-64b2-4637-8368-c443b0124836\" } \r\n","poolAddress":"58dnvLJxmJ7RHBxSMA8bLAbQWPdn4BxkDCDWPeJQ6UY8"}
2025-08-13 13:25:41 [ERROR]: Parse pool data error: {"error":"failed to get info about account EP81yUUMsbndD5cS4QzeU69biCVyKA9TAtU7s5PHuDDk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d688c117-f228-4c1f-9bcc-7321f2d0cb85\" } \r\n","poolAddress":"EP81yUUMsbndD5cS4QzeU69biCVyKA9TAtU7s5PHuDDk"}
2025-08-13 13:25:42 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:25:43 [ERROR]: Parse pool data error: {"error":"failed to get info about account AhYwkm4FrKumen8qN3gDBrPE9ezLWgw24BeQhQHLqP2Y: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5d4d337e-afa5-47fb-bbde-4e5621f3aac9\" } \r\n","poolAddress":"AhYwkm4FrKumen8qN3gDBrPE9ezLWgw24BeQhQHLqP2Y"}
2025-08-13 13:25:43 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:25:44 [ERROR]: Parse pool data error: {"error":"failed to get info about account AhYwkm4FrKumen8qN3gDBrPE9ezLWgw24BeQhQHLqP2Y: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a3eeff37-9cca-430f-b772-10b18b6d140a\" } \r\n","poolAddress":"AhYwkm4FrKumen8qN3gDBrPE9ezLWgw24BeQhQHLqP2Y"}
2025-08-13 13:25:44 [ERROR]: Parse pool data error: {"error":"failed to get info about account CjXkgjMLBLfE62gQFoJptoNjCTSm1fNFnPvZrpBDc6u1: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bdbc760d-3272-4e6a-8b77-48c5cd50f70e\" } \r\n","poolAddress":"CjXkgjMLBLfE62gQFoJptoNjCTSm1fNFnPvZrpBDc6u1"}
2025-08-13 13:25:44 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5SE7GMK6xvr1DrbUzQBjjEnBBo3WngF5KaPeQfjrBsMV: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a6a825b2-d329-42ff-9ea8-f14189cff80a\" } \r\n","poolAddress":"5SE7GMK6xvr1DrbUzQBjjEnBBo3WngF5KaPeQfjrBsMV"}
2025-08-13 13:25:44 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4FEiK6MFw5hesnyoh1aZQWbZHv1TwwyKkjcArppFGqyJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d082a151-5f63-4643-ad38-be2fe95d3552\" } \r\n","poolAddress":"4FEiK6MFw5hesnyoh1aZQWbZHv1TwwyKkjcArppFGqyJ"}
2025-08-13 13:25:44 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8q4AtF5SXK6Qp3KzbD5K6WxB8oR2bcT5h4NFCNANmuFj: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"513ddb9e-ebb0-4ede-9b7f-52fc5cff4fb0\" } \r\n","poolAddress":"8q4AtF5SXK6Qp3KzbD5K6WxB8oR2bcT5h4NFCNANmuFj"}
2025-08-13 13:25:45 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4JXtFEzKGYBEH5dL88NxUzP9KG6bUMrzakViadMb9cYK: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6197ddb1-028a-49b0-9109-d234ca025960\" } \r\n","poolAddress":"4JXtFEzKGYBEH5dL88NxUzP9KG6bUMrzakViadMb9cYK"}
2025-08-13 13:25:46 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:25:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account AWe2qgMU3mNW6gkgfppdMsse1dvu2dZaGwCTGa4muXpF: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0423a94a-85ff-4e42-83ca-7a77ee922a25\" } \r\n","poolAddress":"AWe2qgMU3mNW6gkgfppdMsse1dvu2dZaGwCTGa4muXpF"}
2025-08-13 13:25:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account B6wsohtrxtsFxpBriMhUcGqcWspMxJcMf7Fzoei8X7d8: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"eda69b54-c47d-466f-9b9f-2258f7d8e8a1\" } \r\n","poolAddress":"B6wsohtrxtsFxpBriMhUcGqcWspMxJcMf7Fzoei8X7d8"}
2025-08-13 13:25:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account FLJa6qduXnMRqqnFTFZzUf9RBcrU2ZXLsngCmciW7az1: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5b705561-70bc-4223-93f7-79be9e279cb7\" } \r\n","poolAddress":"FLJa6qduXnMRqqnFTFZzUf9RBcrU2ZXLsngCmciW7az1"}
2025-08-13 13:25:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account FrwoGg7MF7eVRL1uFKc9Z72swnXKFQJpYNVkTwFe41eA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bbc9977e-6c7a-43de-85cc-01ba3fea51de\" } \r\n","poolAddress":"FrwoGg7MF7eVRL1uFKc9Z72swnXKFQJpYNVkTwFe41eA"}
2025-08-13 13:25:49 [ERROR]: 410 Gone:  {"jsonrpc":"2.0","error":{"code": 410, "message":"The RPC call or parameters have been disabled"}, "id": "1faec2f1-d31f-4150-9941-46db3b3e8ebb" } 
 {"context":"pollForNewPools","stack":"Error: 410 Gone:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 410, \"message\":\"The RPC call or parameters have been disabled\"}, \"id\": \"1faec2f1-d31f-4150-9941-46db3b3e8ebb\" } \r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:1703:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}
2025-08-13 13:25:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7LvgsbXMKb8BSEoPdZf7RKLY977SJ76yaJGaQa96TzvQ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Connection rate limits exceeded\"}, \"id\": \"68e6c604-6ece-44b2-917f-f53c35296ce3\" } \r\n","poolAddress":"7LvgsbXMKb8BSEoPdZf7RKLY977SJ76yaJGaQa96TzvQ"}
2025-08-13 13:25:50 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:25:51 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:25:51 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:25:52 [ERROR]: Parse pool data error: {"error":"failed to get info about account R9WEuDqa3X54UqGY4SEXeJXEQ2J8ewAdChHKBRv3rse: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Connection rate limits exceeded\"}, \"id\": \"e5249b92-a35a-4520-8015-09ad56985a0d\" } \r\n","poolAddress":"R9WEuDqa3X54UqGY4SEXeJXEQ2J8ewAdChHKBRv3rse"}
2025-08-13 13:25:52 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:25:57 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:43:22 [ERROR]: Request failed with status code 404 {"context":"sendTelegramMessage","payload":{"type":"alert","title":"🎯 Auto-Sniper Started","message":"Monitoring 0 users for new token opportunities","data":{"Active Users":0,"Min Liquidity":"5 SOL","Buy Amount":"0.1 SOL","Take Profit":"100%","Stop Loss":"50%"},"timestamp":"2025-08-13T12:43:22.248Z"},"stack":"AxiosError: Request failed with status code 404\n    at settle (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\axios\\lib\\core\\settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\axios\\lib\\adapters\\http.js:599:11)\n    at IncomingMessage.emit (node:events:519:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async NotificationManager.sendTelegramMessage (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\notifications.ts:47:7)\n    at async Promise.allSettled (index 0)\n    at async NotificationManager.sendNotification (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\notifications.ts:34:7)\n    at async AutoSniperEngine.start (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\engine\\autoSniper.ts:76:7)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\server.ts:1069:5)","name":"AxiosError"}
2025-08-13 13:43:23 [ERROR]: Request failed with status code 405 {"context":"sendDiscordMessage","payload":{"type":"alert","title":"🎯 Auto-Sniper Started","message":"Monitoring 0 users for new token opportunities","data":{"Active Users":0,"Min Liquidity":"5 SOL","Buy Amount":"0.1 SOL","Take Profit":"100%","Stop Loss":"50%"},"timestamp":"2025-08-13T12:43:22.248Z"},"stack":"AxiosError: Request failed with status code 405\n    at settle (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\axios\\lib\\core\\settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\axios\\lib\\adapters\\http.js:599:11)\n    at IncomingMessage.emit (node:events:519:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async NotificationManager.sendDiscordMessage (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\notifications.ts:66:7)\n    at async Promise.allSettled (index 1)\n    at async NotificationManager.sendNotification (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\notifications.ts:34:7)\n    at async AutoSniperEngine.start (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\engine\\autoSniper.ts:76:7)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\server.ts:1069:5)","name":"AxiosError"}
2025-08-13 13:43:23 [ERROR]: Invalid account info or missing data {"context":"parsePoolInfo","stack":"Error: Invalid account info or missing data\n    at RaydiumPoolMonitor.parsePoolInfo (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:265:15)\n    at RaydiumPoolMonitor.processNewPool (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:213:35)\n    at RaydiumPoolMonitor.processAccountUpdate (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:201:18)\n    at RaydiumPoolMonitor.handleWebSocketMessage (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:155:14)\n    at WebSocket.<anonymous> (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:112:14)\n    at WebSocket.emit (node:events:507:28)\n    at WebSocket.emit (node:domain:489:12)\n    at Receiver.receiverOnMessage (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\ws\\lib\\websocket.js:1220:20)\n    at Receiver.emit (node:events:507:28)\n    at Receiver.emit (node:domain:489:12)","name":"Error"}
2025-08-13 13:43:40 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:43:40 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:43:42 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"64beca03-acd9-438f-b954-8de6bb881588\" } \r\n","poolAddress":"8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh"}
2025-08-13 13:43:43 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0a4dc246-1897-4a7f-855b-242e844c809f\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:43:45 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4BBvrWsocM9wY2qXCCufCL8GxKtNeXT73uNfveK4MMq7: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2b261cb6-589f-46dc-9e81-87d03af589b5\" } \r\n","poolAddress":"4BBvrWsocM9wY2qXCCufCL8GxKtNeXT73uNfveK4MMq7"}
2025-08-13 13:43:45 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"04355efe-ff62-4d1e-b4a1-ba55986fa772\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:45 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4efbbc52-cdc2-4309-9a59-0785585e8809\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:43:45 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9e785c53-e6f6-419e-b69a-1f1e8f1e8b3f\" } \r\n","poolAddress":"6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw"}
2025-08-13 13:43:45 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c5125c3e-7425-4d70-8482-d0cb44717fa0\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:45 [ERROR]: Parse pool data error: {"error":"failed to get info about account J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"02058ca3-4386-4ca5-a5e2-51adf40e7b59\" } \r\n","poolAddress":"J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V"}
2025-08-13 13:43:45 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1a39f3de-ad8b-4a9c-8f53-ccdfa911acd9\" } \r\n","poolAddress":"HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j"}
2025-08-13 13:43:45 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1b10aaae-fef4-457d-8405-e1841f712922\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:43:45 [ERROR]: Parse pool data error: {"error":"failed to get info about account DhnhjRBsA2hxwJB7gkw6dzK1u6HySTZjuWfXKTdcjNJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"aea67a0d-5276-4aee-9562-e46c75ba1d57\" } \r\n","poolAddress":"DhnhjRBsA2hxwJB7gkw6dzK1u6HySTZjuWfXKTdcjNJ"}
2025-08-13 13:43:45 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"537a4f8b-4a12-44db-b8f6-713acdb9d34e\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:43:45 [ERROR]: Parse pool data error: {"error":"failed to get info about account FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"18e75fe2-845b-4103-88af-17dffdbacf65\" } \r\n","poolAddress":"FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn"}
2025-08-13 13:43:45 [ERROR]: Parse pool data error: {"error":"failed to get info about account HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e0ce30a0-5e1c-44ab-9f33-ec1cad05843b\" } \r\n","poolAddress":"HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv"}
2025-08-13 13:43:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b0d51427-1aac-4408-99a9-d050a70df053\" } \r\n","poolAddress":"J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V"}
2025-08-13 13:43:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"35a945a2-3e9d-4c6e-b5bc-4c8097fb5303\" } \r\n","poolAddress":"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"}
2025-08-13 13:43:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account 93tjgwff5Ac5ThyMi8C4WejVVQq4tuMeMuYW1LEYZ7bu: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"10c875e7-88a8-40e1-bc3a-fe55c50e5be8\" } \r\n","poolAddress":"93tjgwff5Ac5ThyMi8C4WejVVQq4tuMeMuYW1LEYZ7bu"}
2025-08-13 13:43:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3450b989-ea3e-44c9-aa2b-deb0cdde107f\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:43:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9qiKN1QnuUVtZXhRWo86Prnr3Lsm933MvJ8phrt5ah8Y: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"452bae69-e001-43e1-b5cd-ecab4e0c193d\" } \r\n","poolAddress":"9qiKN1QnuUVtZXhRWo86Prnr3Lsm933MvJ8phrt5ah8Y"}
2025-08-13 13:43:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9y3JdLZDFL3bnLQLuCYkGeEsCaTJxbKub1UyUFf72Luj: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"63d59f66-2778-409c-b05d-3127935783c4\" } \r\n","poolAddress":"9y3JdLZDFL3bnLQLuCYkGeEsCaTJxbKub1UyUFf72Luj"}
2025-08-13 13:43:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account BVFgVkicjVF2ZiHdK7SDoFWHJs6MtvkpwvWahzHQp2F2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"431f0456-2eda-4021-9c46-b68e06a47426\" } \r\n","poolAddress":"BVFgVkicjVF2ZiHdK7SDoFWHJs6MtvkpwvWahzHQp2F2"}
2025-08-13 13:43:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9y3JdLZDFL3bnLQLuCYkGeEsCaTJxbKub1UyUFf72Luj: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"930e32b7-4249-439a-b4ef-aba634486e7d\" } \r\n","poolAddress":"9y3JdLZDFL3bnLQLuCYkGeEsCaTJxbKub1UyUFf72Luj"}
2025-08-13 13:43:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b4c34c51-2755-4a00-80c0-256aa8c7e4f5\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9cc31c54-d24c-48ff-8197-94e5510e4895\" } \r\n","poolAddress":"HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j"}
2025-08-13 13:43:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b70e34c2-2f88-4f34-a41b-b0900afc3bf0\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c91e7e38-64da-4a20-b9d4-ec9162d8fff6\" } \r\n","poolAddress":"6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw"}
2025-08-13 13:43:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7a622cdd-385c-415c-b670-99d7dca64b47\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:43:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account HQWsAXxH3dGy9DQbryJyDrquKt2eDY6MMHWmpUEKfgZq: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a687821e-f997-4646-b5ce-7317d97db462\" } \r\n","poolAddress":"HQWsAXxH3dGy9DQbryJyDrquKt2eDY6MMHWmpUEKfgZq"}
2025-08-13 13:43:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5a56df5d-4c4c-4e55-bc19-ef28ffc4f73b\" } \r\n","poolAddress":"8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh"}
2025-08-13 13:43:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account 25tXTutLkjtcUX3kqoeRvc7AuBYM7fckBWoVqnQnyDGQ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0f05b805-01d6-4683-b64c-a52214e2e721\" } \r\n","poolAddress":"25tXTutLkjtcUX3kqoeRvc7AuBYM7fckBWoVqnQnyDGQ"}
2025-08-13 13:43:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account 91axdGmMoqQQg2MPUXKxT3bdqX4p2RMLXqZSQgXn2JpM: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1b30e242-29cd-4a79-9ab4-ee17d0aa2ce6\" } \r\n","poolAddress":"91axdGmMoqQQg2MPUXKxT3bdqX4p2RMLXqZSQgXn2JpM"}
2025-08-13 13:43:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account 45L3EuUJKyqhkNDfocrd3yAZu8nzMPXiHtwhjCvidgua: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6e1a43f3-a43b-4932-8e88-5c2e43dbf9fa\" } \r\n","poolAddress":"45L3EuUJKyqhkNDfocrd3yAZu8nzMPXiHtwhjCvidgua"}
2025-08-13 13:43:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b8273f6f-0bf5-4c79-837a-b0ba91ccff40\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:43:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"07d4b858-2726-430b-a3a5-e6690a456870\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:43:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-6d44-4900-9bcb-4d5490ee0d28\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:49 [ERROR]: Parse pool data error: {"error":"failed to get info about account Hado5EEe9gU2pissBJkExQfCTLGqjJ2hufYgDKR55FA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8b0a8d52-c829-4bf8-b181-73703423cc4d\" } \r\n","poolAddress":"Hado5EEe9gU2pissBJkExQfCTLGqjJ2hufYgDKR55FA"}
2025-08-13 13:43:49 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8TEu1NTUbMkUnLMw8USnhdWpyGCM1mD6NvKwc65LDMz2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2bb946a6-03dc-4cdc-b165-0d6ffc3f12ac\" } \r\n","poolAddress":"8TEu1NTUbMkUnLMw8USnhdWpyGCM1mD6NvKwc65LDMz2"}
2025-08-13 13:43:49 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2KB3i5uLKhUcjUwq3poxHpuGGqBWYwtTk5eG9E5WnLG6: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"774e508d-617d-4e06-836a-5a3785e2ab5e\" } \r\n","poolAddress":"2KB3i5uLKhUcjUwq3poxHpuGGqBWYwtTk5eG9E5WnLG6"}
2025-08-13 13:43:49 [ERROR]: Parse pool data error: {"error":"failed to get info about account 525DaaRv8VzqycgG7Zankp12tH4J3mBtNzUmReC9pnU6: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"470d4f45-fe96-4198-aa9b-546a2296a490\" } \r\n","poolAddress":"525DaaRv8VzqycgG7Zankp12tH4J3mBtNzUmReC9pnU6"}
2025-08-13 13:43:49 [ERROR]: Parse pool data error: {"error":"failed to get info about account J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"97a7417c-da46-4db0-bc26-c6b0584ad57f\" } \r\n","poolAddress":"J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe"}
2025-08-13 13:43:49 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:43:49 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ab811ff3-a8a5-4f73-959f-e290188f8150\" } \r\n","poolAddress":"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"380938a2-7a93-4152-af24-dbc2841acfe2\" } \r\n","poolAddress":"HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7cfb1067-913a-40f4-9ef3-0fe44bdd6110\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4197f90e-0d1d-4390-aa03-317fa2c309a4\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8zeP7sqNXpPcoUXMhDWb7eNSy7aZQCvEALr1JuijdckX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"62249af3-2cbf-4dce-b704-f34301c9b8ea\" } \r\n","poolAddress":"8zeP7sqNXpPcoUXMhDWb7eNSy7aZQCvEALr1JuijdckX"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"781eaef7-b17c-4829-b612-63259768d191\" } \r\n","poolAddress":"J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d545cb0b-b16c-4d82-9ca8-385c12748df8\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"caeb973c-38ac-47fc-a85f-197445e94681\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5b7a4d11-bd05-460f-8814-beb47590f1eb\" } \r\n","poolAddress":"6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8659b057-c116-496b-9bc4-67a55b94d8c2\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f5a44344-65eb-45d8-8245-87157e13d4ff\" } \r\n","poolAddress":"rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account AgFnRLUScRD2E4nWQxW73hdbSN7eKEUb2jHX7tx9YTYc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ef49fe3a-f13d-4915-9cd4-6c6f61361d26\" } \r\n","poolAddress":"AgFnRLUScRD2E4nWQxW73hdbSN7eKEUb2jHX7tx9YTYc"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"aed7c3ab-3037-4945-9764-4b03fd175f39\" } \r\n","poolAddress":"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account HKprCtGbnh1j8xeQggzWhhVd3kwDUdphqPqDP8vMay8b: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3e21f794-185d-4239-9a8d-232fb7ebcf34\" } \r\n","poolAddress":"HKprCtGbnh1j8xeQggzWhhVd3kwDUdphqPqDP8vMay8b"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2KCDz9EyKfsT3iug2xUwvYqLLhMYrYrGuhDEuMiuWUzg: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b6489246-093c-4b4a-8ea6-a03ac5dc093f\" } \r\n","poolAddress":"2KCDz9EyKfsT3iug2xUwvYqLLhMYrYrGuhDEuMiuWUzg"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4xxM4cdb6MEsCxM52xvYqkNbzvdeWWsPDZrBcTqVGUar: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b1005c10-1b8c-45a0-916b-57a704c124c3\" } \r\n","poolAddress":"4xxM4cdb6MEsCxM52xvYqkNbzvdeWWsPDZrBcTqVGUar"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9dc9dcff-34ec-4380-a88c-483ca469fd76\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8a4403db-955e-48a1-b65f-e797ded03b25\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1d86e67f-faca-4d45-a463-95814cd70c88\" } \r\n","poolAddress":"FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6b844d5b-6ffc-48f3-9961-df3fda44429e\" } \r\n","poolAddress":"HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 83G6VzJzLRCnHBsLATj94VCpRimyyqwuN6ZfL11McADL: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9dc10469-f1f1-46b4-bb4f-f2c3e4325803\" } \r\n","poolAddress":"83G6VzJzLRCnHBsLATj94VCpRimyyqwuN6ZfL11McADL"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"aa3c804b-cb4f-4f9f-8b9c-85fdee40562a\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 29oXiuz7rxtXSsEgPUPnTexvsJgqyZ2mR6W6zf8NhsQE: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"597b66a8-0abf-4437-8747-6a90ee8c4582\" } \r\n","poolAddress":"29oXiuz7rxtXSsEgPUPnTexvsJgqyZ2mR6W6zf8NhsQE"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"32c37db4-5ab6-42bc-b57b-3f7371a7262d\" } \r\n","poolAddress":"J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4d8107e4-9a12-4557-af9b-46118d057249\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-e8a9-4b91-9ae2-05d86bcda812\" } \r\n","poolAddress":"J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f2f9b404-dfb1-4f1f-a820-05bd2b2ee260\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8zq3vBuoy66dur6dhrA4aqnrtGg9yZyRAp51BTBpEXj: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"61d60713-e028-4b8e-a778-6c4a0e9c8bb2\" } \r\n","poolAddress":"8zq3vBuoy66dur6dhrA4aqnrtGg9yZyRAp51BTBpEXj"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3aaf6248-5ad2-4b28-829d-ff45b627db5c\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8f5ff1ca-8c4b-486c-8af5-017c9a5748ea\" } \r\n","poolAddress":"HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5b231167-77bf-45d2-8700-9edf3d096260\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account HH5QkaL9MWWs1T2odjZakAct4buM4xJNqK1jJPPmpGnP: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9f8309ff-a87a-42f9-8e3f-88f47200fc26\" } \r\n","poolAddress":"HH5QkaL9MWWs1T2odjZakAct4buM4xJNqK1jJPPmpGnP"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account BHrHkRzW5MhzVnka8J5YU3iroZZJsB4szsvF8wCavzd2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d05f9800-4db7-4b97-8152-34c330552731\" } \r\n","poolAddress":"BHrHkRzW5MhzVnka8J5YU3iroZZJsB4szsvF8wCavzd2"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"987a09db-ace2-4b66-956d-874d9f328838\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0a7add80-c2d3-4474-8d3d-c2a5162ebc56\" } \r\n","poolAddress":"FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b1e9847d-edef-49eb-b574-445f4922e571\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account DiqbqkdWGnvJM9rS7QkVBzgXsKiPLLzFzv4GvH4EoGPW: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"aabc8ce7-bcc0-4fee-9c91-cdb7772dd9d8\" } \r\n","poolAddress":"DiqbqkdWGnvJM9rS7QkVBzgXsKiPLLzFzv4GvH4EoGPW"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"78ea1099-4141-434b-9973-640657d01e06\" } \r\n","poolAddress":"rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d4d020cc-d1d1-43de-b516-7cafa9bdb961\" } \r\n","poolAddress":"HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account FYAnFcdjkcfAkbtZqixnTqNVLoDjJft82L5FFgPaSWe3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2a14bc30-ebdd-48a4-85dc-2f791919766b\" } \r\n","poolAddress":"FYAnFcdjkcfAkbtZqixnTqNVLoDjJft82L5FFgPaSWe3"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account FHmjZ9z3iZDqcW9E8qRuLZvJwWb2hBhcfb7JQnekV9Bz: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"450d8cac-bf2f-4e85-90d1-a13dab56a5a7\" } \r\n","poolAddress":"FHmjZ9z3iZDqcW9E8qRuLZvJwWb2hBhcfb7JQnekV9Bz"}
2025-08-13 13:43:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ba474222-8aaa-4fd2-a8d1-211f656df4f9\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:43:51 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9E5LzteiLGk8wqUAAd2J9iQC37mFnFdLEf6JX4ckcqax: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4001f061-304a-4c81-b1d6-740caf2cffed\" } \r\n","poolAddress":"9E5LzteiLGk8wqUAAd2J9iQC37mFnFdLEf6JX4ckcqax"}
2025-08-13 13:43:51 [ERROR]: Parse pool data error: {"error":"failed to get info about account 83G6VzJzLRCnHBsLATj94VCpRimyyqwuN6ZfL11McADL: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d71684f1-e65c-496d-a1a4-348c1f8503ee\" } \r\n","poolAddress":"83G6VzJzLRCnHBsLATj94VCpRimyyqwuN6ZfL11McADL"}
2025-08-13 13:43:51 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2N6SHfcg2U8KPPYujRGMzBjAmW2NZUuWnRWRZVCihBxw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fda4faba-6922-456e-9efa-8411c4e1d419\" } \r\n","poolAddress":"2N6SHfcg2U8KPPYujRGMzBjAmW2NZUuWnRWRZVCihBxw"}
2025-08-13 13:43:52 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4677880b-a5c1-4595-ae0e-a07c90dd9a35\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:52 [ERROR]: Parse pool data error: {"error":"failed to get info about account BSzedbEvWRqVksaF558epPWCM16avEpyhm2HgSq9WZyy: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d637bcba-a8cf-4d48-811d-8d4926e75a21\" } \r\n","poolAddress":"BSzedbEvWRqVksaF558epPWCM16avEpyhm2HgSq9WZyy"}
2025-08-13 13:43:52 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e8be1e38-821a-47a4-a174-7ae42be4bf30\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:43:52 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:43:52 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:43:52 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:43:52 [ERROR]: Parse pool data error: {"error":"failed to get info about account rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c8ddffd2-d43d-4c82-bbfb-adc0b1813fd8\" } \r\n","poolAddress":"rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id"}
2025-08-13 13:43:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bdf5ce11-5f06-4dbe-a186-e15cf494a712\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:43:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"54ed95d3-13a8-4dd5-af86-06910438381f\" } \r\n","poolAddress":"8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh"}
2025-08-13 13:43:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account F738UFUJZwaJvRoNJKotySDm83sL4UZ81Jxrd7YPRcX6: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7b31c94d-46d5-42b5-8a31-10d4c362f84a\" } \r\n","poolAddress":"F738UFUJZwaJvRoNJKotySDm83sL4UZ81Jxrd7YPRcX6"}
2025-08-13 13:43:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"02b67c01-51aa-4cb0-a9cb-0154a1b355eb\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:43:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d4b0d647-bfa8-4b87-973c-126dcf968ccf\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:56 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"94b09b19-ce72-43d3-959c-72e8d11cec61\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:43:56 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"45c1de93-1a95-47df-a232-a129a0e6f8d5\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:56 [ERROR]: Parse pool data error: {"error":"failed to get info about account J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b66c3fb9-fd13-4bce-a19c-b21c75c97f77\" } \r\n","poolAddress":"J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe"}
2025-08-13 13:43:56 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5f48e08c-cf2a-4025-b7f4-978bd6693857\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:43:56 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"28b0f5c7-dd7c-4fb3-9e91-3a6eea477d0e\" } \r\n","poolAddress":"6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw"}
2025-08-13 13:43:57 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e9175535-5546-4c9a-945e-f667cb6d3786\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:43:57 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e5569c65-17d0-4161-bf2f-3fe386e7f286\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:43:57 [ERROR]: Parse pool data error: {"error":"failed to get info about account FHmjZ9z3iZDqcW9E8qRuLZvJwWb2hBhcfb7JQnekV9Bz: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d0e15605-35e0-413e-bb27-144973c769f4\" } \r\n","poolAddress":"FHmjZ9z3iZDqcW9E8qRuLZvJwWb2hBhcfb7JQnekV9Bz"}
2025-08-13 13:43:57 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d4aa5742-8a72-485c-8f62-47657bca6ca9\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:57 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:43:58 [ERROR]: Parse pool data error: {"error":"failed to get info about account rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7af609c3-87b3-4e85-9c76-4f54072b3c48\" } \r\n","poolAddress":"rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id"}
2025-08-13 13:43:58 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: TypeError: fetch failed","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:59 [ERROR]: Parse pool data error: {"error":"failed to get info about account J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"365f1268-96fa-4ab8-82f3-8a833e43526b\" } \r\n","poolAddress":"J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe"}
2025-08-13 13:43:59 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bd031cef-6ae2-4517-8e2d-595999aeb9c2\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:59 [ERROR]: Parse pool data error: {"error":"failed to get info about account HZAypsdCgXhGSq4PTkBWzUZCcfRMzu1CpcbmNxo66Awd: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e84a7d62-7052-45a5-9205-f47015ab635a\" } \r\n","poolAddress":"HZAypsdCgXhGSq4PTkBWzUZCcfRMzu1CpcbmNxo66Awd"}
2025-08-13 13:43:59 [ERROR]: Parse pool data error: {"error":"failed to get info about account FMQxUEHet7hhn18GqsRnZDYTcfdbWkLw8hr7tjc5GXwh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"50c2da70-3bf4-4d07-bf25-930f51000831\" } \r\n","poolAddress":"FMQxUEHet7hhn18GqsRnZDYTcfdbWkLw8hr7tjc5GXwh"}
2025-08-13 13:43:59 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7b5fe491-3f94-4932-85d4-94748441527a\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:43:59 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"187d3e08-f524-4c99-9768-336d63b6dee0\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:43:59 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"643f4222-0e82-4752-b162-4902b9f09939\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:43:59 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2KB3i5uLKhUcjUwq3poxHpuGGqBWYwtTk5eG9E5WnLG6: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9df97f6e-a24c-4b5a-8954-2bed4b042009\" } \r\n","poolAddress":"2KB3i5uLKhUcjUwq3poxHpuGGqBWYwtTk5eG9E5WnLG6"}
2025-08-13 13:43:59 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2fu1u1v3tUgFC5Wmb7ADovFrhVPfNPPqiVj6tZhhYqpE: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6e34957c-68b7-46d7-b6f7-dd3aec2fb034\" } \r\n","poolAddress":"2fu1u1v3tUgFC5Wmb7ADovFrhVPfNPPqiVj6tZhhYqpE"}
2025-08-13 13:43:59 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a3d3d086-6a45-4aa8-a197-96c54e6260f5\" } \r\n","poolAddress":"8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh"}
2025-08-13 13:43:59 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5XSQxHj1ao2YcaiA4m3JJQF3jeXyRjKY5ceKfUf58sT8: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7fac2d76-1308-44f4-ba96-7b4b77db2d18\" } \r\n","poolAddress":"5XSQxHj1ao2YcaiA4m3JJQF3jeXyRjKY5ceKfUf58sT8"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account GcHDV1oLCKu6nHXfqcv3tpwg9L8qUPxTZSo3u6R6UPYv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"26f4ec7e-1f07-4e20-af52-cc854e1f8dc1\" } \r\n","poolAddress":"GcHDV1oLCKu6nHXfqcv3tpwg9L8qUPxTZSo3u6R6UPYv"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account ApaUAF25uNRen4P5w4b2mseEECVmfhxNYpdwbwge18MK: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7b6af1ce-905f-4d82-99eb-01b8edef86e8\" } \r\n","poolAddress":"ApaUAF25uNRen4P5w4b2mseEECVmfhxNYpdwbwge18MK"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bd66d42b-34e1-44de-bdc9-a34cbe264331\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"da14c072-31c8-4f6f-89a4-3a3a54d9fb89\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"618de2b1-5d16-44a4-b342-4cb055b8aaf5\" } \r\n","poolAddress":"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account DomLceb5ARci8GJ2NxahnAEvZEFFWYrwSiAHvzZtvz6x: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fb613abb-45b9-4095-a025-1d84d8369c91\" } \r\n","poolAddress":"DomLceb5ARci8GJ2NxahnAEvZEFFWYrwSiAHvzZtvz6x"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a69cd5fd-f6e0-4d1b-8717-1ab9977a7b69\" } \r\n","poolAddress":"8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account BVFgVkicjVF2ZiHdK7SDoFWHJs6MtvkpwvWahzHQp2F2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a7cd6bc6-a6b4-418a-94c7-f70ee6cd316d\" } \r\n","poolAddress":"BVFgVkicjVF2ZiHdK7SDoFWHJs6MtvkpwvWahzHQp2F2"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"45209f51-c62a-4e6f-9917-c46ee33f1fbb\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account FvMZrD1qC66Zw8VPrW15xN1N5owUPqpQgNQ5oH18mR4E: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a240b17f-5865-40f6-9545-6cf4015c37af\" } \r\n","poolAddress":"FvMZrD1qC66Zw8VPrW15xN1N5owUPqpQgNQ5oH18mR4E"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7c99fb6d-5f93-4c8c-89ef-4806364bb839\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account HWWr7z4HhnNENWKrpfqnzs9KJcEZQxYnHZ4iWoN62JWC: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6fc65acd-f29a-42da-94ad-e9e0d2e711d5\" } \r\n","poolAddress":"HWWr7z4HhnNENWKrpfqnzs9KJcEZQxYnHZ4iWoN62JWC"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e7643824-ba7a-4a13-9f1b-840e4588c546\" } \r\n","poolAddress":"6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"60c23b8f-cfc6-4f9b-bdac-3c769ecca4b6\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3bC2e2RxcfvF9oP22LvbaNsVwoS2T98q6ErCRoayQYdq: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"17f2760e-92e1-4641-b23a-b0a5e4b4e3b0\" } \r\n","poolAddress":"3bC2e2RxcfvF9oP22LvbaNsVwoS2T98q6ErCRoayQYdq"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6aE8jwogq83TVWTk2uBUrpEjPyPr2GVE9Cb3vxp1p3SM: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a8cdef31-f340-4934-9e75-6b1251acdbc4\" } \r\n","poolAddress":"6aE8jwogq83TVWTk2uBUrpEjPyPr2GVE9Cb3vxp1p3SM"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9qiKN1QnuUVtZXhRWo86Prnr3Lsm933MvJ8phrt5ah8Y: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"72f9ac0c-1cfb-4bdf-8bcb-f243cc798b07\" } \r\n","poolAddress":"9qiKN1QnuUVtZXhRWo86Prnr3Lsm933MvJ8phrt5ah8Y"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e4f3d822-fc7f-4185-a44d-3a9da1004ca1\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"66993a69-04ed-4497-825a-96107902e0bd\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account AemYRZmJryzAQ9Z4RLfUBLnPRUY5ecooc94EJvemfti4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f3f5c342-f4dd-4a61-a2eb-dc4dbeee691d\" } \r\n","poolAddress":"AemYRZmJryzAQ9Z4RLfUBLnPRUY5ecooc94EJvemfti4"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"eac4048d-9864-4c88-8162-5bb93e93f7f7\" } \r\n","poolAddress":"HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2615960e-d260-482e-99d6-060406e97faf\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4xxM4cdb6MEsCxM52xvYqkNbzvdeWWsPDZrBcTqVGUar: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"982deb82-2dc9-4221-8e91-00d23db06925\" } \r\n","poolAddress":"4xxM4cdb6MEsCxM52xvYqkNbzvdeWWsPDZrBcTqVGUar"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6ea5d888-72ed-49e9-a9b5-43b223fcc25e\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f9a396b1-5472-4f9c-a121-2a71100711fa\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2135e903-8ec6-4e98-98e0-0fb101154a34\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account mfx2W3yXS5mKw8qNcLH4QGZMMLM86TRNcHZKaKL9yWb: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"183e8b52-d949-4002-8d20-b54a13a4f7ff\" } \r\n","poolAddress":"mfx2W3yXS5mKw8qNcLH4QGZMMLM86TRNcHZKaKL9yWb"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account HQqf8wzXtHDV6QU9Gdiqncmuvqdo1UCQAaz1x24ondj: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ee85dd53-ecd4-4b95-bfbb-a8f36cb64830\" } \r\n","poolAddress":"HQqf8wzXtHDV6QU9Gdiqncmuvqdo1UCQAaz1x24ondj"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cb2b3063-8a5f-4073-a691-7ff57e06732c\" } \r\n","poolAddress":"AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e7e32b28-65db-4053-b628-a21dbf60315e\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"74da4d0b-59b6-4e14-bae8-8106a1e31359\" } \r\n","poolAddress":"rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account DhnhjRBsA2hxwJB7gkw6dzK1u6HySTZjuWfXKTdcjNJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0133f9ad-d530-432f-b9c9-9ebee3e7f76e\" } \r\n","poolAddress":"DhnhjRBsA2hxwJB7gkw6dzK1u6HySTZjuWfXKTdcjNJ"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7NenVNjGWUCMVj8UcyPbLwWL9swcUNsEUydWy9yGxsFy: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e6f736e9-e412-4b9d-a9fd-d3e14fc2a557\" } \r\n","poolAddress":"7NenVNjGWUCMVj8UcyPbLwWL9swcUNsEUydWy9yGxsFy"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"74842fac-0944-4738-a000-48289002d1ed\" } \r\n","poolAddress":"8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a44671d9-b8eb-4431-95f9-9b8c81bc0d79\" } \r\n","poolAddress":"6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cdd0bd26-f711-4d2d-adb0-940bff1f3d5d\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d2213c59-db98-4df0-9c94-29b8ea8714f1\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 39YeFEZ5n3TueXLj8KzQghUqKGvrxCCgttELsmkWx8bq: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"45eb99f7-3bf2-4a07-b130-06ce6db2f32a\" } \r\n","poolAddress":"39YeFEZ5n3TueXLj8KzQghUqKGvrxCCgttELsmkWx8bq"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4a0b3f60-7662-4f3c-a86e-be8c135641b5\" } \r\n","poolAddress":"6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f51fca64-c878-4c9a-a8c4-01d88395c83c\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1aa4ff51-ad5d-4c4b-93e7-eab2591a542d\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c1278e33-c814-47c7-bc14-64c1f84073a4\" } \r\n","poolAddress":"FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5b60b9a2-a5fb-46c6-8814-f457f47d46c4\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"07f8a18f-fbc9-4523-bfae-e7910ca54a39\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ac7ceb7a-8a68-4c20-bb40-dd9623fca0d0\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:44:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account EpRp9o4m3PJiuezg6utRxQ3PgUuVXykWKRW76fvo21D5: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f024cfb3-0a2d-4cb8-8916-66fe10b0b068\" } \r\n","poolAddress":"EpRp9o4m3PJiuezg6utRxQ3PgUuVXykWKRW76fvo21D5"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fadf8136-40b1-4eeb-a652-4f4f674a8b68\" } \r\n","poolAddress":"FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6GDrReNVfyjQDCuGMrKdG2JU7Uj8NCvBt2ukaL2mDj1L: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b477a2b7-d5e5-4d88-a27a-4fea1a22563e\" } \r\n","poolAddress":"6GDrReNVfyjQDCuGMrKdG2JU7Uj8NCvBt2ukaL2mDj1L"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account HEwDpMdUJH3ukP36zBvYcuUUDH2LAqiVmETQFVRviB5h: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e90e2902-b43c-4c60-914a-f5d5643a25e5\" } \r\n","poolAddress":"HEwDpMdUJH3ukP36zBvYcuUUDH2LAqiVmETQFVRviB5h"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9147f7c8-7b12-4b1f-a871-c4c365935cdb\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0c1b9437-3220-48ef-9b55-265dca5c925d\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0b426569-fe38-45e6-9e0f-63f006dbf403\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cea87daf-7934-445b-857c-4aea52b91ea9\" } \r\n","poolAddress":"FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d2b301cb-161e-448d-8d43-977abaae6bd5\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"53f988dc-092b-4abd-aa7a-3400d902d268\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e28d56cf-97bc-4947-a758-98fc6a68e76b\" } \r\n","poolAddress":"6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"add22195-4076-4e73-9101-95ed1ab5fb2a\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b285cbac-e863-44d2-a798-aabce1cb68cf\" } \r\n","poolAddress":"FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7cbac0b6-e34b-430e-801b-a98277bf5aa0\" } \r\n","poolAddress":"8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4f4a954a-65ad-43ba-928f-d6d6dc6ca40d\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ec45b855-f427-4cad-818b-48b591e69d3f\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"af13f544-0f6e-4315-a5fb-02b016576abe\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4554daae-61e2-4b6f-b2af-6de27a46a759\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c49adce2-306b-4ff3-b93c-2304fc07eb83\" } \r\n","poolAddress":"J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account EpQwGi2DL7nrdCgZtaDCKtjjqGZxnrbLuktFZbxjDZwH: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"be55cd2c-2d35-4509-b230-0490879f82d3\" } \r\n","poolAddress":"EpQwGi2DL7nrdCgZtaDCKtjjqGZxnrbLuktFZbxjDZwH"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f245750f-97f4-498d-8b28-f71d323cd286\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account CaysL4cjU1BuB9ECvhQ4yNQBVt7eug3GcZjndcJdf5JU: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"044ada41-4550-4bb8-b784-fbe704e5daa3\" } \r\n","poolAddress":"CaysL4cjU1BuB9ECvhQ4yNQBVt7eug3GcZjndcJdf5JU"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account J2p6tgZDkvtHQ3VfbGRjzHJNLrqFgGfvjJsp2K7HX5cH: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4040c7c5-4c50-4d0f-8465-b7e0cbdf5d7e\" } \r\n","poolAddress":"J2p6tgZDkvtHQ3VfbGRjzHJNLrqFgGfvjJsp2K7HX5cH"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8zq3vBuoy66dur6dhrA4aqnrtGg9yZyRAp51BTBpEXj: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"41013a4a-0ea5-49a4-8f58-a6072695443a\" } \r\n","poolAddress":"8zq3vBuoy66dur6dhrA4aqnrtGg9yZyRAp51BTBpEXj"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"67677c96-e976-4e6f-828b-e46be5036e4d\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account G4P3ptRvrM9qzvfy4K5aPnq8KnbCQCU8uGxTYoM8Uumt: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-e503-465d-b962-d27001344f28\" } \r\n","poolAddress":"G4P3ptRvrM9qzvfy4K5aPnq8KnbCQCU8uGxTYoM8Uumt"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 29oXiuz7rxtXSsEgPUPnTexvsJgqyZ2mR6W6zf8NhsQE: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d2856a8d-d676-4eba-871e-ffa3bf24bc01\" } \r\n","poolAddress":"29oXiuz7rxtXSsEgPUPnTexvsJgqyZ2mR6W6zf8NhsQE"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2KCDz9EyKfsT3iug2xUwvYqLLhMYrYrGuhDEuMiuWUzg: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"04fe8274-39b2-4843-92f7-3bbfff8d9555\" } \r\n","poolAddress":"2KCDz9EyKfsT3iug2xUwvYqLLhMYrYrGuhDEuMiuWUzg"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account CpsMssqi3P9VMvNqxrdWVbSBCwyUHbGgNcrw7MorBq3g: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"30ba4af3-6643-4511-abc2-7c50f932171f\" } \r\n","poolAddress":"CpsMssqi3P9VMvNqxrdWVbSBCwyUHbGgNcrw7MorBq3g"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"56b9ff13-5f54-45f5-83c2-67fb339ff37d\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account FTqcn8geGipGj33jQQmT3jNfLfhg2PGtHyRPBVSUobPo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bb2423d6-e3b1-4fc2-8ed0-002709aa0679\" } \r\n","poolAddress":"FTqcn8geGipGj33jQQmT3jNfLfhg2PGtHyRPBVSUobPo"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"97aa9a5f-72bf-431c-9cb7-f73ed21c77c1\" } \r\n","poolAddress":"J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"24627a1f-469d-4812-a8ac-661a2a669417\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f6f0e4cd-c217-4674-a08e-7f55d2994259\" } \r\n","poolAddress":"rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8132c261-d663-4693-8b5a-63f131aacb45\" } \r\n","poolAddress":"HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6fb26a2a-35fe-43f7-ab65-f6e43d98df5e\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9YXBNiKTfsGXvVQvnt6KhpK3HarrT9dSq9e4fCW7D2Y: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bf0c8aa3-3272-438e-a6bf-4cacbe548078\" } \r\n","poolAddress":"9YXBNiKTfsGXvVQvnt6KhpK3HarrT9dSq9e4fCW7D2Y"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account DuHqhU7FqCewQZbKg9G3uUNCCG4saR9Taomo4HYYXQeC: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"08f3830c-fb5a-4105-a6bd-17d419e23d5f\" } \r\n","poolAddress":"DuHqhU7FqCewQZbKg9G3uUNCCG4saR9Taomo4HYYXQeC"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"29ea1b84-0029-48c9-b3d7-54c4af3306e0\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7944329d-9340-44fe-9789-de4a78b79ab4\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0e1c7613-1745-45bc-bda8-47e16b515421\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3sjNoCnkkhWPVXYGDtem8rCciHSGc9jSFZuUAzKbvRVp: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6a4b467d-2163-475c-b7e4-abef6776dc04\" } \r\n","poolAddress":"3sjNoCnkkhWPVXYGDtem8rCciHSGc9jSFZuUAzKbvRVp"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account 39agbWtDRC2yTuoD9S5ef2jnJoYFU1nXWGbkHVTAWwKY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cea32371-d707-4c1e-96c7-dbdddf0e6ee5\" } \r\n","poolAddress":"39agbWtDRC2yTuoD9S5ef2jnJoYFU1nXWGbkHVTAWwKY"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"89da6bb8-2603-4a52-920a-18126d6b216c\" } \r\n","poolAddress":"FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3b87e919-bd26-4a6d-afd2-e51945149a67\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7bcd887c-645c-4964-9e60-ab50208a3918\" } \r\n","poolAddress":"rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8dccd527-59db-4c93-8399-ce5fde499a73\" } \r\n","poolAddress":"6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"98a8f832-d289-455c-9133-ab10f0943efb\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account AgFnRLUScRD2E4nWQxW73hdbSN7eKEUb2jHX7tx9YTYc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3e05820e-afa0-48e7-9b29-7635110cd56f\" } \r\n","poolAddress":"AgFnRLUScRD2E4nWQxW73hdbSN7eKEUb2jHX7tx9YTYc"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account 525DaaRv8VzqycgG7Zankp12tH4J3mBtNzUmReC9pnU6: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"299c148f-84e8-4ddd-b386-0a6afdfa75e6\" } \r\n","poolAddress":"525DaaRv8VzqycgG7Zankp12tH4J3mBtNzUmReC9pnU6"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e6ffc876-4324-4350-89c6-20a82af2ab2a\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ff41d1f0-c46a-42c3-9b01-d59f2723e8a1\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account 91axdGmMoqQQg2MPUXKxT3bdqX4p2RMLXqZSQgXn2JpM: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e2ce2346-d122-4ab8-8103-b7b121944558\" } \r\n","poolAddress":"91axdGmMoqQQg2MPUXKxT3bdqX4p2RMLXqZSQgXn2JpM"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"228cf9fc-4f8e-45ab-8d09-97d92e07276c\" } \r\n","poolAddress":"8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e64d9006-2584-4b9c-ab3a-50d587ef275b\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cc447fb7-9c68-41aa-ad1d-34bb1ba11ea4\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"627b03fe-deaf-4db9-82c7-ea16d37e815d\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e8c5a4c9-be3d-42f7-a1f0-0d4308089723\" } \r\n","poolAddress":"FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn"}
2025-08-13 13:44:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account DJmQv6UYZ4jtPrjM85uaNKpiTkBYYvDdWu3CN4wgku13: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"428aac15-ed24-4209-a0a4-da3ef1a9a6ee\" } \r\n","poolAddress":"DJmQv6UYZ4jtPrjM85uaNKpiTkBYYvDdWu3CN4wgku13"}
2025-08-13 13:44:03 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1f005cf8-1550-476c-9fcb-d2e873baa88e\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:44:03 [ERROR]: Parse pool data error: {"error":"failed to get info about account EP2ib6dYdEeqD8MfE2ezHCxX3kP3K2eLKkirfPm5eyMx: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ef8a2e80-f7d2-41aa-ade4-041c82e8898c\" } \r\n","poolAddress":"EP2ib6dYdEeqD8MfE2ezHCxX3kP3K2eLKkirfPm5eyMx"}
2025-08-13 13:44:04 [ERROR]: Parse pool data error: {"error":"failed to get info about account FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"963ec497-d348-477e-907e-703d2c4efd91\" } \r\n","poolAddress":"FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn"}
2025-08-13 13:44:04 [ERROR]: Parse pool data error: {"error":"failed to get info about account 27FB4Sjy2hqo5ydGzK1dwFWwn6JiFcwkwvsJaL8926aQ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"da0050f8-35c7-4ebc-b633-38ae8843ebc1\" } \r\n","poolAddress":"27FB4Sjy2hqo5ydGzK1dwFWwn6JiFcwkwvsJaL8926aQ"}
2025-08-13 13:44:04 [ERROR]: Parse pool data error: {"error":"failed to get info about account J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2a4e0828-4ec6-49bb-86d8-0a3441332911\" } \r\n","poolAddress":"J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V"}
2025-08-13 13:44:04 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b81419ff-4c0a-4359-b062-91f573c6bc09\" } \r\n","poolAddress":"HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j"}
2025-08-13 13:44:04 [ERROR]: Parse pool data error: {"error":"failed to get info about account KSeAucEoqTBB4ttakHYE1encbxjn2u9zUa9ursDLe1E: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3d2fd342-3551-494b-933b-b33c7198774c\" } \r\n","poolAddress":"KSeAucEoqTBB4ttakHYE1encbxjn2u9zUa9ursDLe1E"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d76a52a9-adb4-4ace-ac05-ec74cf2a1e57\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"92777e00-6695-4d76-b8e6-2ce633462a66\" } \r\n","poolAddress":"J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account FYAnFcdjkcfAkbtZqixnTqNVLoDjJft82L5FFgPaSWe3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"263baa46-fdb7-4481-bfbb-7180e62a9af9\" } \r\n","poolAddress":"FYAnFcdjkcfAkbtZqixnTqNVLoDjJft82L5FFgPaSWe3"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2TEj7Y1chTcYs9zkaJe4vixnqtu78Pw1ycCUBj13zr9X: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b4628813-c661-4d78-975b-a29353068fca\" } \r\n","poolAddress":"2TEj7Y1chTcYs9zkaJe4vixnqtu78Pw1ycCUBj13zr9X"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"485740c2-7166-421f-8e16-016d4cba9b11\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5512c669-365f-43be-a220-f8e5343f088c\" } \r\n","poolAddress":"rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b39600f4-36a8-4482-910f-3c962257eacf\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1f93ca63-90f0-4a4d-94c0-92a33b67057f\" } \r\n","poolAddress":"J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b0bf2fe1-57f7-479a-8244-642fe1530f3f\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b405941a-5cc5-4c79-8775-ae152aa3c83c\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4adb099d-52f4-4879-9fd0-1d10ebc68501\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e349e181-f12b-40c7-b378-90edecf97b5a\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"73fbe312-cd57-4e5a-ad6c-d084a9194bd1\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account HvAUaYpykFbUmzyGUCPbLR2nKA43cXspfxYNyYT2mw7j: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"292ae298-6a2b-461c-949b-b48d6458eae0\" } \r\n","poolAddress":"HvAUaYpykFbUmzyGUCPbLR2nKA43cXspfxYNyYT2mw7j"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"38f1e3cb-b515-4fa2-b2ce-ec09419c9a96\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2TEj7Y1chTcYs9zkaJe4vixnqtu78Pw1ycCUBj13zr9X: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"83d1f7e0-ea84-49e9-81e3-e8c0920cddd4\" } \r\n","poolAddress":"2TEj7Y1chTcYs9zkaJe4vixnqtu78Pw1ycCUBj13zr9X"}
2025-08-13 13:44:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"79b1a559-4003-4790-85ca-356c54a4033e\" } \r\n","poolAddress":"HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv"}
2025-08-13 13:44:07 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0cfd0d88-d6b3-4574-88d8-d0448b19d5ad\" } \r\n","poolAddress":"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"}
2025-08-13 13:44:07 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1c318d1b-17d6-4804-8c08-44aeb1717f45\" } \r\n","poolAddress":"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"}
2025-08-13 13:44:08 [ERROR]: Parse pool data error: {"error":"failed to get info about account EP2ib6dYdEeqD8MfE2ezHCxX3kP3K2eLKkirfPm5eyMx: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b8ce307b-3f12-4b3f-acaf-c323a4ed6497\" } \r\n","poolAddress":"EP2ib6dYdEeqD8MfE2ezHCxX3kP3K2eLKkirfPm5eyMx"}
2025-08-13 13:44:08 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"eaa83c6c-683a-4ebc-a819-dda39212b479\" } \r\n","poolAddress":"HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j"}
2025-08-13 13:44:08 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2fu1u1v3tUgFC5Wmb7ADovFrhVPfNPPqiVj6tZhhYqpE: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"65698d30-94ee-44fe-b10c-6781ea172521\" } \r\n","poolAddress":"2fu1u1v3tUgFC5Wmb7ADovFrhVPfNPPqiVj6tZhhYqpE"}
2025-08-13 13:44:08 [ERROR]: Parse pool data error: {"error":"failed to get info about account J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4b856340-6fe7-4cbf-abac-cedb8e15800f\" } \r\n","poolAddress":"J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V"}
2025-08-13 13:44:08 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"aba8a52b-6b87-4d05-b983-adfbfc168d6c\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"919f175f-a41a-436a-8252-bcb440b99930\" } \r\n","poolAddress":"J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account BVFgVkicjVF2ZiHdK7SDoFWHJs6MtvkpwvWahzHQp2F2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5e77be2a-3239-4a73-8d63-740a5c079fd5\" } \r\n","poolAddress":"BVFgVkicjVF2ZiHdK7SDoFWHJs6MtvkpwvWahzHQp2F2"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7Fz2sWKcWZXFHZLsWU8CEgTe9bUj6yVAmkU9Q6NsYgFB: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a3cfde36-52a9-4010-b2df-3c8f0579c2bd\" } \r\n","poolAddress":"7Fz2sWKcWZXFHZLsWU8CEgTe9bUj6yVAmkU9Q6NsYgFB"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f1d26372-b6c3-453d-bb12-09cd5afb6046\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account 525DaaRv8VzqycgG7Zankp12tH4J3mBtNzUmReC9pnU6: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8c1b5101-e6ed-45ff-916a-729139dc68d3\" } \r\n","poolAddress":"525DaaRv8VzqycgG7Zankp12tH4J3mBtNzUmReC9pnU6"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1464e612-0499-4dcc-9530-c50eff5f6b06\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6090a8f8-b7b3-4405-85b8-200cd687ff4c\" } \r\n","poolAddress":"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account T5mZpKou42YF3ssrQyrxFJZDL4zvBKg8u1HxqBfDyoo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bd443176-1a2a-4a5b-bf24-ca2b41872336\" } \r\n","poolAddress":"T5mZpKou42YF3ssrQyrxFJZDL4zvBKg8u1HxqBfDyoo"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account 99a2qtfLAxv9LVhKesjEawjKy8Pwb4cypC5ccuCB4VUS: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"201a7fe9-66a9-4686-8c9e-7f8064fa480a\" } \r\n","poolAddress":"99a2qtfLAxv9LVhKesjEawjKy8Pwb4cypC5ccuCB4VUS"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"61fe5e9c-7526-4b92-bec1-c6e714b7c29f\" } \r\n","poolAddress":"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"477bcfaf-4d04-4f6f-937f-aee2f7df3a2b\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cb114bc1-4fc3-4550-80a0-afcfd7cd72a7\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2KB3i5uLKhUcjUwq3poxHpuGGqBWYwtTk5eG9E5WnLG6: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8db7719c-c131-433b-8b69-97d273c47924\" } \r\n","poolAddress":"2KB3i5uLKhUcjUwq3poxHpuGGqBWYwtTk5eG9E5WnLG6"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1b1119d9-6581-4969-a01c-78e9d8a9f5f2\" } \r\n","poolAddress":"HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c5944d31-d792-42c5-81b2-af0daf7aa5f8\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"46f96b5d-ac46-467c-a6d3-f18479807585\" } \r\n","poolAddress":"8WQsKRXNjTdSpbDpwRAaZJfPxUEBvAEZ6eeQSt4bjACh"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2yhXxFHiWjWxMiZMsTPRGhiKTWsuYFFx2us2kH8P7Kpq: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6b64d8a1-02cf-482d-8cc2-95f9a4d17b91\" } \r\n","poolAddress":"2yhXxFHiWjWxMiZMsTPRGhiKTWsuYFFx2us2kH8P7Kpq"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account 99a2qtfLAxv9LVhKesjEawjKy8Pwb4cypC5ccuCB4VUS: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"747cc583-9dce-494b-8293-64b754669ea9\" } \r\n","poolAddress":"99a2qtfLAxv9LVhKesjEawjKy8Pwb4cypC5ccuCB4VUS"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9tz6vYKiBDLYx2RnGWC5tESu4pyVE4jD6Tm56352UGte: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b4708a20-0d1f-4396-aa96-8230897d2884\" } \r\n","poolAddress":"9tz6vYKiBDLYx2RnGWC5tESu4pyVE4jD6Tm56352UGte"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"db26e639-0a5b-4b92-935e-dfd64a084deb\" } \r\n","poolAddress":"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4584cda5-cf63-413c-a263-44eac4498869\" } \r\n","poolAddress":"6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d2092cdb-6f23-4a8a-8ff1-8c67e779bab0\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5ae0cb40-f7ad-421e-897c-c6cd0005d833\" } \r\n","poolAddress":"J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ed7c39d0-1dfe-4936-ad53-9ed6ccd66a28\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e00b27d1-7c82-4a45-8b46-272987c9a6c5\" } \r\n","poolAddress":"FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"808f4b4c-3b94-43fe-8bf4-332d386fdba5\" } \r\n","poolAddress":"HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"03bf52cd-b8dd-4c1e-bc89-ec24df177d7a\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account DhnhjRBsA2hxwJB7gkw6dzK1u6HySTZjuWfXKTdcjNJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"08e49323-f2f6-45c2-9ef3-6d781194a599\" } \r\n","poolAddress":"DhnhjRBsA2hxwJB7gkw6dzK1u6HySTZjuWfXKTdcjNJ"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7XawhbbxtsRcQA8KTkHT9f9nc6d69UwqCDh6U5EEbEmX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"889dd091-8979-4e1f-95bd-4b6e82f2bfa7\" } \r\n","poolAddress":"7XawhbbxtsRcQA8KTkHT9f9nc6d69UwqCDh6U5EEbEmX"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9ae24fd0-aa5c-4485-bb8a-6d0e45051ad4\" } \r\n","poolAddress":"FCEnSxyJfRSKsz6tASUENCsfGwKgkH6YuRn1AMmyHhZn"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account GuHvfGf8wZtRkY272dcQWi49U1doNBxC1J1A4o5Yq2ZP: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2cb88631-5e61-42ed-bef2-f9adffa8538c\" } \r\n","poolAddress":"GuHvfGf8wZtRkY272dcQWi49U1doNBxC1J1A4o5Yq2ZP"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account DBAwLuq2AjDCm4ThbeX4NirSrdnBGFA8iJdcVJQp5tW5: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ce5619de-5fef-48da-b047-ebfcb708a65d\" } \r\n","poolAddress":"DBAwLuq2AjDCm4ThbeX4NirSrdnBGFA8iJdcVJQp5tW5"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCRty6J2toqf8EAiQSNpAeG21Qd1p8bymJpWWkBkAXkz: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"99c36c8d-4378-4350-85cf-f89a6912bccf\" } \r\n","poolAddress":"HCRty6J2toqf8EAiQSNpAeG21Qd1p8bymJpWWkBkAXkz"}
2025-08-13 13:44:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3fe0d38c-5a08-48bf-914d-797f7a70ef86\" } \r\n","poolAddress":"HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5SjYRnJWc8gfrzje1vX21WyiCrgNcdJ8NXDdJ8jLGWMg: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3f4591ec-06fd-4a70-843e-91671e698491\" } \r\n","poolAddress":"5SjYRnJWc8gfrzje1vX21WyiCrgNcdJ8NXDdJ8jLGWMg"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"25b067b7-0be7-451c-99b9-a3adfd655717\" } \r\n","poolAddress":"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6nmiCc4QSXqjsNYWgPRBVtij7GdHfDTud71eEGqApqav: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3c77334e-eb0f-4588-9355-7fe92bab0e44\" } \r\n","poolAddress":"6nmiCc4QSXqjsNYWgPRBVtij7GdHfDTud71eEGqApqav"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"db39f861-20a8-4655-ba72-1c7939b36dc6\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9vWVooeqk6udEHyioYr8Lvz6n8VZ45TkgKCtvneEVHCY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"59bd051e-1c97-48ba-b82e-ae21c1553b3c\" } \r\n","poolAddress":"9vWVooeqk6udEHyioYr8Lvz6n8VZ45TkgKCtvneEVHCY"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account HL4KFTfhZZMm7NGefszQxzJ3M6CaDWE7TesyuRuQYmMJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1e03af8f-108f-4ae0-8c9c-092c3393a0e3\" } \r\n","poolAddress":"HL4KFTfhZZMm7NGefszQxzJ3M6CaDWE7TesyuRuQYmMJ"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-5525-4368-925a-184cddc5b871\" } \r\n","poolAddress":"J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account G7mw1d83ismcQJKkzt62Ug4noXCjVhu3eV7U5EMgge6Z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fea84248-dd5d-4b3e-b4df-1ce269ae150c\" } \r\n","poolAddress":"G7mw1d83ismcQJKkzt62Ug4noXCjVhu3eV7U5EMgge6Z"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account 13wWy2kyJjWJsQPftTDgCPeRSQ6eCC6pNCQk5NobW96D: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d2469382-e9cf-4d17-bfcd-c765a9e73d73\" } \r\n","poolAddress":"13wWy2kyJjWJsQPftTDgCPeRSQ6eCC6pNCQk5NobW96D"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account GQaNRzB3wEANnnpUFGftmegjaWQiva2oeppb7K5WdVAv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0fde332d-dd04-435f-8f8f-d9eb6427d89f\" } \r\n","poolAddress":"GQaNRzB3wEANnnpUFGftmegjaWQiva2oeppb7K5WdVAv"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0b44298d-5918-4fe3-89c4-8ed5684c9cdf\" } \r\n","poolAddress":"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3a225d73-2667-4e43-a0da-2dd11e352693\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0e002c5b-f440-4041-b120-f550aee2b919\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ab821c76-f9ea-4a13-bf73-3c9166911f8e\" } \r\n","poolAddress":"J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"202b94dd-805e-4da4-b821-be07f9295879\" } \r\n","poolAddress":"J3REAYUVGQXFapREYxrLfjburb5ZiYd6BooeDgTbGzRe"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account 78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2dc3e0a0-140e-4604-8183-3d4fb14bc10a\" } \r\n","poolAddress":"78sBWyimVhLumzZg1bdMD6ogGig8QpmgYZqCXNyMxx4z"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"aa7b2611-64e9-465f-968b-1a3ede16410c\" } \r\n","poolAddress":"HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"231abd93-60ff-4860-900f-d857f21867ad\" } \r\n","poolAddress":"ERUN4gzkYiab27CamqwXd2HCoh7nRm6aMpwh28gb34pw"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCRty6J2toqf8EAiQSNpAeG21Qd1p8bymJpWWkBkAXkz: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2d61d822-8e89-417a-a9ab-b5b92c5bafd5\" } \r\n","poolAddress":"HCRty6J2toqf8EAiQSNpAeG21Qd1p8bymJpWWkBkAXkz"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"643a55f3-bed0-4470-a975-8c5a35d8d3b0\" } \r\n","poolAddress":"Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f54962b2-57d9-459b-88f0-1926f97b671e\" } \r\n","poolAddress":"AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b4c44f5b-528c-46ce-9f8a-a3ed42957c89\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1b04053b-8466-42e2-898c-6e0b63e73cbf\" } \r\n","poolAddress":"rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account 99a2qtfLAxv9LVhKesjEawjKy8Pwb4cypC5ccuCB4VUS: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a1f216e0-e30c-480b-a100-842dbde624f2\" } \r\n","poolAddress":"99a2qtfLAxv9LVhKesjEawjKy8Pwb4cypC5ccuCB4VUS"}
2025-08-13 13:44:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"28c60e14-bd15-4c92-9fe8-dee7f22934d3\" } \r\n","poolAddress":"J333LZ5UhEjwxb64dcD756viUFXr164dVNxQpXuMPH9V"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7bc5ef76-3c63-43ea-8c91-4a179667ae82\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account FTqcn8geGipGj33jQQmT3jNfLfhg2PGtHyRPBVSUobPo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fd0ea434-d9ef-4db3-ae18-64d68ff780d5\" } \r\n","poolAddress":"FTqcn8geGipGj33jQQmT3jNfLfhg2PGtHyRPBVSUobPo"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"93caac6d-89cf-4e23-a3c4-b123530bed1e\" } \r\n","poolAddress":"HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"94054af7-0af9-4704-b89d-a6160af364d8\" } \r\n","poolAddress":"AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e1455738-f4e0-4264-bc50-5fb0d8ca40fd\" } \r\n","poolAddress":"HCeas2dbSrCrHNuzBVpkuaSz7X3oVu9ekzxMc5ZDsZ4j"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"840a9259-bd15-42a2-b861-7a09660938ef\" } \r\n","poolAddress":"rmXadNc4DuUBHsBrTuY6GXC3pKTyDrAdaMrXMb2f8id"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 13wWy2kyJjWJsQPftTDgCPeRSQ6eCC6pNCQk5NobW96D: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e2ee0a13-4edf-43ea-8c99-ca03e1683afb\" } \r\n","poolAddress":"13wWy2kyJjWJsQPftTDgCPeRSQ6eCC6pNCQk5NobW96D"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5148b7e3-8223-45b0-881f-364e67d82b2f\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"eda4ee8a-b040-4b6c-b34d-416330b6c92e\" } \r\n","poolAddress":"HVNwzt7Pxfu76KHCMQPTLuTCLTm6WnQ1esLv4eizseSv"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8WwcNqdZjCY5Pt7AkhupAFknV2txca9sq6YBkGzLbvdt: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cd0e2883-3962-4960-834f-6f75a56ffacd\" } \r\n","poolAddress":"8WwcNqdZjCY5Pt7AkhupAFknV2txca9sq6YBkGzLbvdt"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9U6jGAy5ySvTqjdzc3t7xti7sZnRgGn1wLFKVQnZQuTu: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f658aa01-acbc-4191-8037-afdd0a1e3b39\" } \r\n","poolAddress":"9U6jGAy5ySvTqjdzc3t7xti7sZnRgGn1wLFKVQnZQuTu"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account EP2ib6dYdEeqD8MfE2ezHCxX3kP3K2eLKkirfPm5eyMx: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c9d230ff-67fe-4ce1-81a4-58985e5a4bf2\" } \r\n","poolAddress":"EP2ib6dYdEeqD8MfE2ezHCxX3kP3K2eLKkirfPm5eyMx"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3adfb3d4-c900-4425-942a-fb5020c61309\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b9b215c6-756b-4b37-bd61-ce246b5b89d0\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2TiBao8WWj72LWLYhRW2kZamfmMPW4cXbYQwJNu1uNpD: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"860d0ee9-7829-4bcc-8793-c8bc9f883353\" } \r\n","poolAddress":"2TiBao8WWj72LWLYhRW2kZamfmMPW4cXbYQwJNu1uNpD"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account HQWsAXxH3dGy9DQbryJyDrquKt2eDY6MMHWmpUEKfgZq: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1eeefdb7-8174-403d-9e60-7e496a520cee\" } \r\n","poolAddress":"HQWsAXxH3dGy9DQbryJyDrquKt2eDY6MMHWmpUEKfgZq"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"96a65433-1293-48ae-bf4c-10b9305d2fb6\" } \r\n","poolAddress":"6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account HL4KFTfhZZMm7NGefszQxzJ3M6CaDWE7TesyuRuQYmMJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4a93d7f9-1bac-4bf1-a8d6-852ccdd9d66a\" } \r\n","poolAddress":"HL4KFTfhZZMm7NGefszQxzJ3M6CaDWE7TesyuRuQYmMJ"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7XawhbbxtsRcQA8KTkHT9f9nc6d69UwqCDh6U5EEbEmX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d2199886-16d7-49a2-9076-046a7cbce05f\" } \r\n","poolAddress":"7XawhbbxtsRcQA8KTkHT9f9nc6d69UwqCDh6U5EEbEmX"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account HL4KFTfhZZMm7NGefszQxzJ3M6CaDWE7TesyuRuQYmMJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c2705a9f-b933-4065-9bfd-a897cbab44d2\" } \r\n","poolAddress":"HL4KFTfhZZMm7NGefszQxzJ3M6CaDWE7TesyuRuQYmMJ"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account HptGD1evUJuaM7fbcUJbyvE1GDpNeYP39dJNuaP6hdj4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f825b749-9fd0-4b70-ae04-354aa6b47419\" } \r\n","poolAddress":"HptGD1evUJuaM7fbcUJbyvE1GDpNeYP39dJNuaP6hdj4"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d5359b03-ecbc-4dc8-ac44-b21fc68e4c7b\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"16000b0c-d1e1-4179-bfc0-8a89262d944a\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 29oXiuz7rxtXSsEgPUPnTexvsJgqyZ2mR6W6zf8NhsQE: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8da05aa7-af71-4ad5-8c30-c56876cea06a\" } \r\n","poolAddress":"29oXiuz7rxtXSsEgPUPnTexvsJgqyZ2mR6W6zf8NhsQE"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"31636b05-9926-4744-9562-e80288f79438\" } \r\n","poolAddress":"AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6tKkSXov17MNwFSTwCHYVy9MqUik6gvez5M4G9S1quUb: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0bb693de-9121-4a50-bb28-63de38c4ef76\" } \r\n","poolAddress":"6tKkSXov17MNwFSTwCHYVy9MqUik6gvez5M4G9S1quUb"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d5bf8440-ff6f-4cb9-a2af-0253c0850f1c\" } \r\n","poolAddress":"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8zeP7sqNXpPcoUXMhDWb7eNSy7aZQCvEALr1JuijdckX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b4206b88-1a6f-4d8d-80cd-54dbace5c748\" } \r\n","poolAddress":"8zeP7sqNXpPcoUXMhDWb7eNSy7aZQCvEALr1JuijdckX"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 27yqYt1yMo7QKqGbcivAT655bYqRCgnspwVqa7Fb55iA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"90bf7cfa-c6e7-4301-9f42-334ca3c5fbbe\" } \r\n","poolAddress":"27yqYt1yMo7QKqGbcivAT655bYqRCgnspwVqa7Fb55iA"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 99a2qtfLAxv9LVhKesjEawjKy8Pwb4cypC5ccuCB4VUS: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8a403874-fd98-4b18-81f7-01dba00d6301\" } \r\n","poolAddress":"99a2qtfLAxv9LVhKesjEawjKy8Pwb4cypC5ccuCB4VUS"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 45L3EuUJKyqhkNDfocrd3yAZu8nzMPXiHtwhjCvidgua: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"39b75d83-b104-45d1-bc92-58aae9cfe7ef\" } \r\n","poolAddress":"45L3EuUJKyqhkNDfocrd3yAZu8nzMPXiHtwhjCvidgua"}
2025-08-13 13:44:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"207f4794-96ec-4e27-895c-4232b07e2262\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account BtbcC3YhgpxfRBPurKMhkeBzBasPKae5XxsY9gpX1xzJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"17ef7309-7f1f-4d4f-9fb4-cb35420b8b72\" } \r\n","poolAddress":"BtbcC3YhgpxfRBPurKMhkeBzBasPKae5XxsY9gpX1xzJ"}
2025-08-13 13:44:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2fu1u1v3tUgFC5Wmb7ADovFrhVPfNPPqiVj6tZhhYqpE: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3e91f2d8-242a-4677-a123-4b5c43ff69d0\" } \r\n","poolAddress":"2fu1u1v3tUgFC5Wmb7ADovFrhVPfNPPqiVj6tZhhYqpE"}
2025-08-13 13:44:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account BnhtEzG3ubrvT4KruvfjXQPasvAVFCFXEPByzJu8i1ss: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b1d79d4a-d47d-4385-8d08-1713bbffcfad\" } \r\n","poolAddress":"BnhtEzG3ubrvT4KruvfjXQPasvAVFCFXEPByzJu8i1ss"}
2025-08-13 13:44:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2abc320d-c86a-4894-9fcd-3e9d1ca62bad\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw: TypeError: fetch failed","poolAddress":"6HfaJiUuTXFZEfmdkQSNbvfe6i95Nh2wUVJ5dWMf7gtw"}
2025-08-13 13:44:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account Av7LGsQmd55ArintYHZYxKfw7useEycN23La5qPbX6pu: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d91c19d0-113a-410c-b82f-f85dd487012d\" } \r\n","poolAddress":"Av7LGsQmd55ArintYHZYxKfw7useEycN23La5qPbX6pu"}
2025-08-13 13:44:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7cjgE6PwoNqfXjayUvUDE7TCS2GmPNnP1jCT29jT3JVJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"685091b9-2f69-4189-8cfe-3bfcc5c0143a\" } \r\n","poolAddress":"7cjgE6PwoNqfXjayUvUDE7TCS2GmPNnP1jCT29jT3JVJ"}
2025-08-13 13:44:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account BmsZE6TkZYskyS1PatPKRyyazGdxWFxdia4BuvLg9AgY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8ff778e9-415f-4fbf-aa0d-ed904a1ab003\" } \r\n","poolAddress":"BmsZE6TkZYskyS1PatPKRyyazGdxWFxdia4BuvLg9AgY"}
2025-08-13 13:44:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account G4EBEAJvfNdmubERWqAHkQh94jKtnEDfs6ZudScSXA5: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7ce7d91d-57bc-4a95-b1f1-54356e78bda1\" } \r\n","poolAddress":"G4EBEAJvfNdmubERWqAHkQh94jKtnEDfs6ZudScSXA5"}
2025-08-13 13:44:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6tKkSXov17MNwFSTwCHYVy9MqUik6gvez5M4G9S1quUb: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fea2898e-33e2-4b9c-b0c5-dec32106d0f4\" } \r\n","poolAddress":"6tKkSXov17MNwFSTwCHYVy9MqUik6gvez5M4G9S1quUb"}
2025-08-13 13:44:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 969wmqydp6mc8jEL2v7tmpv5PwzWBfqzkmiENne2JBfe: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4eaabb63-3f86-4f4d-8502-c5485af1f88b\" } \r\n","poolAddress":"969wmqydp6mc8jEL2v7tmpv5PwzWBfqzkmiENne2JBfe"}
2025-08-13 13:44:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e505c47e-6747-4991-b2ab-d57ebe4b0ce3\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9U6jGAy5ySvTqjdzc3t7xti7sZnRgGn1wLFKVQnZQuTu: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4dea1faa-e572-40f2-93f6-3e14986d0426\" } \r\n","poolAddress":"9U6jGAy5ySvTqjdzc3t7xti7sZnRgGn1wLFKVQnZQuTu"}
2025-08-13 13:44:14 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2FQ28MMbdhEsed8aZfia1tfSCvPxRQy7FjFN7NYU2ADU: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"86b4b44c-bca4-4079-a156-021f5ff04565\" } \r\n","poolAddress":"2FQ28MMbdhEsed8aZfia1tfSCvPxRQy7FjFN7NYU2ADU"}
2025-08-13 13:44:14 [ERROR]: Parse pool data error: {"error":"failed to get info about account G4P3ptRvrM9qzvfy4K5aPnq8KnbCQCU8uGxTYoM8Uumt: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"119243fc-3d0d-49a2-8c37-8959cba6f529\" } \r\n","poolAddress":"G4P3ptRvrM9qzvfy4K5aPnq8KnbCQCU8uGxTYoM8Uumt"}
2025-08-13 13:44:14 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9xyCzsHi1wUWva7t5Z8eAvZDRmUCVhRrbaFfm3VbU4Mf: TypeError: fetch failed","poolAddress":"9xyCzsHi1wUWva7t5Z8eAvZDRmUCVhRrbaFfm3VbU4Mf"}
2025-08-13 13:44:15 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3f3QtzjwEg8GoSMg3mabD71v13EAbqnACPuyPFCqtwYi: TypeError: fetch failed","poolAddress":"3f3QtzjwEg8GoSMg3mabD71v13EAbqnACPuyPFCqtwYi"}
2025-08-13 13:44:15 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2e6f3d40-b0ad-4355-a4c6-f8c497998381\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:15 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7z2rW4tcgT9ofPMJtS9Wn5BW6awKi9M6zrSfEAZXpkhJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1b597d3d-a2df-402c-aede-8e2313a574ca\" } \r\n","poolAddress":"7z2rW4tcgT9ofPMJtS9Wn5BW6awKi9M6zrSfEAZXpkhJ"}
2025-08-13 13:44:15 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9vWVooeqk6udEHyioYr8Lvz6n8VZ45TkgKCtvneEVHCY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"214cb7b8-b16b-48b4-a300-4001a4acb1e9\" } \r\n","poolAddress":"9vWVooeqk6udEHyioYr8Lvz6n8VZ45TkgKCtvneEVHCY"}
2025-08-13 13:44:15 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5tho4By9RsqTF1rbm9Akiepik3kZBT7ffUzGg8bL1mD: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"daaad960-ce65-4877-82aa-9cf7c2edffe7\" } \r\n","poolAddress":"5tho4By9RsqTF1rbm9Akiepik3kZBT7ffUzGg8bL1mD"}
2025-08-13 13:44:15 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"346637e1-5256-4cc3-8e40-0d85f30118c7\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:15 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:15 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8WwcNqdZjCY5Pt7AkhupAFknV2txca9sq6YBkGzLbvdt: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8ae0464a-61f9-4751-b3c6-fe1285e7bbfd\" } \r\n","poolAddress":"8WwcNqdZjCY5Pt7AkhupAFknV2txca9sq6YBkGzLbvdt"}
2025-08-13 13:44:15 [ERROR]: Parse pool data error: {"error":"failed to get info about account 525DaaRv8VzqycgG7Zankp12tH4J3mBtNzUmReC9pnU6: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7589b27d-80fa-44cc-afcd-1641ba6db2d2\" } \r\n","poolAddress":"525DaaRv8VzqycgG7Zankp12tH4J3mBtNzUmReC9pnU6"}
2025-08-13 13:44:15 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9U6jGAy5ySvTqjdzc3t7xti7sZnRgGn1wLFKVQnZQuTu: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"728480a1-420e-43b1-90be-5b297f7c680b\" } \r\n","poolAddress":"9U6jGAy5ySvTqjdzc3t7xti7sZnRgGn1wLFKVQnZQuTu"}
2025-08-13 13:44:15 [ERROR]: Parse pool data error: {"error":"failed to get info about account J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6b47d518-2c7e-4449-bf69-20f81a4eeb6d\" } \r\n","poolAddress":"J1XYBNJntH9Aa2NsduQykyaGCVoad3h6dPZY2RaqrQaT"}
2025-08-13 13:44:15 [ERROR]: Parse pool data error: {"error":"failed to get info about account 29oXiuz7rxtXSsEgPUPnTexvsJgqyZ2mR6W6zf8NhsQE: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"58b8f64a-6267-446d-bdb9-29e38770cde2\" } \r\n","poolAddress":"29oXiuz7rxtXSsEgPUPnTexvsJgqyZ2mR6W6zf8NhsQE"}
2025-08-13 13:44:16 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4xxM4cdb6MEsCxM52xvYqkNbzvdeWWsPDZrBcTqVGUar: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"88af9727-0bc2-45db-ad57-cf7cf06efdd9\" } \r\n","poolAddress":"4xxM4cdb6MEsCxM52xvYqkNbzvdeWWsPDZrBcTqVGUar"}
2025-08-13 13:44:16 [ERROR]: Parse pool data error: {"error":"failed to get info about account G7mw1d83ismcQJKkzt62Ug4noXCjVhu3eV7U5EMgge6Z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"79204da3-cf54-4e73-8e6d-58fd7638c604\" } \r\n","poolAddress":"G7mw1d83ismcQJKkzt62Ug4noXCjVhu3eV7U5EMgge6Z"}
2025-08-13 13:44:16 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4xxM4cdb6MEsCxM52xvYqkNbzvdeWWsPDZrBcTqVGUar: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"af671176-52b3-4106-bf49-00fae3a6fc20\" } \r\n","poolAddress":"4xxM4cdb6MEsCxM52xvYqkNbzvdeWWsPDZrBcTqVGUar"}
2025-08-13 13:44:16 [ERROR]: Parse pool data error: {"error":"failed to get info about account Ek84XpaNtx8E5v8YJANtB3PjwKtwiJa6cszaQ619E9Nw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"539b063e-b00c-4a87-9837-3f9196d5cbbc\" } \r\n","poolAddress":"Ek84XpaNtx8E5v8YJANtB3PjwKtwiJa6cszaQ619E9Nw"}
2025-08-13 13:44:16 [ERROR]: Parse pool data error: {"error":"failed to get info about account AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"efff6f7b-4adc-4da3-b293-f78dc1c1d0b9\" } \r\n","poolAddress":"AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk"}
2025-08-13 13:44:16 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4kppT5tMPqXiQHfK4UvCNn5vCuFfgNfseKgfUj5qp7kF: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f2b97fd2-5ec7-4964-a888-60af535f6879\" } \r\n","poolAddress":"4kppT5tMPqXiQHfK4UvCNn5vCuFfgNfseKgfUj5qp7kF"}
2025-08-13 13:44:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5N96fYr2ZzaiNyZaYGAi31xvT5ExzfPnwh2s95yaXXSv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e14c570c-c4e1-4156-83d6-9b992cd9ecfd\" } \r\n","poolAddress":"5N96fYr2ZzaiNyZaYGAi31xvT5ExzfPnwh2s95yaXXSv"}
2025-08-13 13:44:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account ABcrckEJSQxoiC7V1Gk7xG7UJp5JWrjxc5HYYEyPpB4Q: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a26cd991-ab77-42af-8cb5-615ffbd88e5c\" } \r\n","poolAddress":"ABcrckEJSQxoiC7V1Gk7xG7UJp5JWrjxc5HYYEyPpB4Q"}
2025-08-13 13:44:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"44c92f04-6fb9-4087-88c4-4086799f4e7e\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account GCq81cGTMk27j3RY7mj9LDJa2MQZuueWdTW14WAZf3H8: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4fc5f9d9-812c-49ac-83fa-a0dded3089a4\" } \r\n","poolAddress":"GCq81cGTMk27j3RY7mj9LDJa2MQZuueWdTW14WAZf3H8"}
2025-08-13 13:44:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5297269c-b002-4b8b-a05f-5add0a54c0d3\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7570ab11-4c03-407a-8f80-86858712a77d\" } \r\n","poolAddress":"AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk"}
2025-08-13 13:44:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9U6jGAy5ySvTqjdzc3t7xti7sZnRgGn1wLFKVQnZQuTu: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cf5aea73-949f-45ba-b76c-d0447eaf670e\" } \r\n","poolAddress":"9U6jGAy5ySvTqjdzc3t7xti7sZnRgGn1wLFKVQnZQuTu"}
2025-08-13 13:44:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8zq3vBuoy66dur6dhrA4aqnrtGg9yZyRAp51BTBpEXj: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d3da0e3f-c1ed-40e4-913b-a66ddadfcdb4\" } \r\n","poolAddress":"8zq3vBuoy66dur6dhrA4aqnrtGg9yZyRAp51BTBpEXj"}
2025-08-13 13:44:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5tho4By9RsqTF1rbm9Akiepik3kZBT7ffUzGg8bL1mD: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"234fe444-4a0f-4d3d-ad44-4ad4e0c3863f\" } \r\n","poolAddress":"5tho4By9RsqTF1rbm9Akiepik3kZBT7ffUzGg8bL1mD"}
2025-08-13 13:44:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4xxM4cdb6MEsCxM52xvYqkNbzvdeWWsPDZrBcTqVGUar: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1033a350-b50c-45d4-9478-c0a2473fdd10\" } \r\n","poolAddress":"4xxM4cdb6MEsCxM52xvYqkNbzvdeWWsPDZrBcTqVGUar"}
2025-08-13 13:44:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account Ek84XpaNtx8E5v8YJANtB3PjwKtwiJa6cszaQ619E9Nw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"afda50ec-1431-4fb7-af56-0d4d9e5fb445\" } \r\n","poolAddress":"Ek84XpaNtx8E5v8YJANtB3PjwKtwiJa6cszaQ619E9Nw"}
2025-08-13 13:44:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2FQ28MMbdhEsed8aZfia1tfSCvPxRQy7FjFN7NYU2ADU: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9c806947-8b15-4760-b018-ce9735c076b5\" } \r\n","poolAddress":"2FQ28MMbdhEsed8aZfia1tfSCvPxRQy7FjFN7NYU2ADU"}
2025-08-13 13:44:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bb3wbCewJnpCm94mmXytjNiP2J41MouoC3xWKeNYRTFR: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"756e13b6-3b42-4395-99b7-6d9b28482309\" } \r\n","poolAddress":"Bb3wbCewJnpCm94mmXytjNiP2J41MouoC3xWKeNYRTFR"}
2025-08-13 13:44:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account EzTTryTskAhVzCDDsqq61WfqNz6B2gUXRyuW8hTGM1wR: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"75faff45-e9b5-455c-bb96-1329d4527999\" } \r\n","poolAddress":"EzTTryTskAhVzCDDsqq61WfqNz6B2gUXRyuW8hTGM1wR"}
2025-08-13 13:44:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account AVs9TA4nWDzfPJE9gGVNJMVhcQy3V9PGazuz33BfG2RA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"22f3aee5-10f2-41ec-9942-bf952bee8296\" } \r\n","poolAddress":"AVs9TA4nWDzfPJE9gGVNJMVhcQy3V9PGazuz33BfG2RA"}
2025-08-13 13:44:20 [ERROR]: Parse pool data error: {"error":"failed to get info about account HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0b6ac3ac-1544-4a68-a3a3-080492037ccf\" } \r\n","poolAddress":"HGRvEZF83Hm2x5HoVx9ec2cBiAjTmWFHK6VCUPLH4yDY"}
2025-08-13 13:44:20 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8guix1wqRn8usUfmL54DrHCCLXLyTuDsTKEwhowdD2ms: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2d137a6a-9cc3-4a63-9147-55b9c51d8337\" } \r\n","poolAddress":"8guix1wqRn8usUfmL54DrHCCLXLyTuDsTKEwhowdD2ms"}
2025-08-13 13:44:20 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2TEj7Y1chTcYs9zkaJe4vixnqtu78Pw1ycCUBj13zr9X: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5dcdde98-ab5b-4382-aaae-5d1cf275a4e4\" } \r\n","poolAddress":"2TEj7Y1chTcYs9zkaJe4vixnqtu78Pw1ycCUBj13zr9X"}
2025-08-13 13:44:20 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6UmmUiYoBjSrhakAobJw8BvkmJtDVxaeBtbt7rxWo1mg: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"25acb0df-6466-489b-aff5-3a4150bfdedf\" } \r\n","poolAddress":"6UmmUiYoBjSrhakAobJw8BvkmJtDVxaeBtbt7rxWo1mg"}
2025-08-13 13:44:20 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5tho4By9RsqTF1rbm9Akiepik3kZBT7ffUzGg8bL1mD: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0dae1bf6-d941-4dc3-ad9c-1b8be6716509\" } \r\n","poolAddress":"5tho4By9RsqTF1rbm9Akiepik3kZBT7ffUzGg8bL1mD"}
2025-08-13 13:44:20 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2bnZ1edbvK3CK3LTNZ5jH9anvXYCmzPR4W2HQ6Ngsv5K: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6561548c-2b38-47c2-9236-92a31fcf3ca8\" } \r\n","poolAddress":"2bnZ1edbvK3CK3LTNZ5jH9anvXYCmzPR4W2HQ6Ngsv5K"}
2025-08-13 13:44:20 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6txNZWajZtoSgZUyhkh2MVwJ4CnKstTQyi3uSwxr85ir: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d1ba974d-52c5-47a1-97c9-8d4ee74ef84c\" } \r\n","poolAddress":"6txNZWajZtoSgZUyhkh2MVwJ4CnKstTQyi3uSwxr85ir"}
2025-08-13 13:44:21 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9xyCzsHi1wUWva7t5Z8eAvZDRmUCVhRrbaFfm3VbU4Mf: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f4355737-c16e-4b30-90b5-3462ad1e1cb5\" } \r\n","poolAddress":"9xyCzsHi1wUWva7t5Z8eAvZDRmUCVhRrbaFfm3VbU4Mf"}
2025-08-13 13:44:21 [ERROR]: Parse pool data error: {"error":"failed to get info about account Crsqyrc9LW3KHJE1i6SXZimWbTpBkvfhuk8pvxbYJV16: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6c4d3a94-ece4-4f01-b99d-2e9e4dff4a4b\" } \r\n","poolAddress":"Crsqyrc9LW3KHJE1i6SXZimWbTpBkvfhuk8pvxbYJV16"}
2025-08-13 13:44:21 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9xyCzsHi1wUWva7t5Z8eAvZDRmUCVhRrbaFfm3VbU4Mf: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a5528e9c-c950-4f83-bb90-4cee77c660cf\" } \r\n","poolAddress":"9xyCzsHi1wUWva7t5Z8eAvZDRmUCVhRrbaFfm3VbU4Mf"}
2025-08-13 13:44:21 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2baC5JL75NosEr2oLJZ14gbNjWDuUq3ui33ZPLZTgCEA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3697c489-064c-4388-95d0-c94afe4f571a\" } \r\n","poolAddress":"2baC5JL75NosEr2oLJZ14gbNjWDuUq3ui33ZPLZTgCEA"}
2025-08-13 13:44:21 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9xyCzsHi1wUWva7t5Z8eAvZDRmUCVhRrbaFfm3VbU4Mf: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0ca6c28c-9d11-4cb9-ae6b-3b5077feaa4b\" } \r\n","poolAddress":"9xyCzsHi1wUWva7t5Z8eAvZDRmUCVhRrbaFfm3VbU4Mf"}
2025-08-13 13:44:21 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5tho4By9RsqTF1rbm9Akiepik3kZBT7ffUzGg8bL1mD: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5ad1b54f-32fb-4542-b6b4-5a40171d1937\" } \r\n","poolAddress":"5tho4By9RsqTF1rbm9Akiepik3kZBT7ffUzGg8bL1mD"}
2025-08-13 13:44:22 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:22 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:22 [ERROR]: Parse pool data error: {"error":"failed to get info about account EaDc3TCXyuxdUqsDyA87p5Pf2eVQ3nsng7PTQJECB7VU: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a8b9673a-32d4-4de2-a542-975ef9401de5\" } \r\n","poolAddress":"EaDc3TCXyuxdUqsDyA87p5Pf2eVQ3nsng7PTQJECB7VU"}
2025-08-13 13:44:22 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5eLRsN6qDQTQSBF8KdW4B8mVpeeAzHCCwaDptzMyszxH: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1637e3ce-da42-49e7-b045-5ae9a03ab9ff\" } \r\n","poolAddress":"5eLRsN6qDQTQSBF8KdW4B8mVpeeAzHCCwaDptzMyszxH"}
2025-08-13 13:44:23 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9xyCzsHi1wUWva7t5Z8eAvZDRmUCVhRrbaFfm3VbU4Mf: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"385276b4-574c-4f78-9411-b92115d12b19\" } \r\n","poolAddress":"9xyCzsHi1wUWva7t5Z8eAvZDRmUCVhRrbaFfm3VbU4Mf"}
2025-08-13 13:44:23 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRi5dHtacw7xkcU91pqTvWoZgDMDvP8XinbkzCDRRCd9: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"dd4129bf-4a95-4929-b42a-4336619d3e5e\" } \r\n","poolAddress":"FRi5dHtacw7xkcU91pqTvWoZgDMDvP8XinbkzCDRRCd9"}
2025-08-13 13:44:23 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2baC5JL75NosEr2oLJZ14gbNjWDuUq3ui33ZPLZTgCEA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"b6eb94d4-cf8e-4168-9e4c-bde6dc228678\" } \r\n","poolAddress":"2baC5JL75NosEr2oLJZ14gbNjWDuUq3ui33ZPLZTgCEA"}
2025-08-13 13:44:23 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2baC5JL75NosEr2oLJZ14gbNjWDuUq3ui33ZPLZTgCEA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"5448a4d5-a488-4f2f-89e5-89039182d3ed\" } \r\n","poolAddress":"2baC5JL75NosEr2oLJZ14gbNjWDuUq3ui33ZPLZTgCEA"}
2025-08-13 13:44:23 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2baC5JL75NosEr2oLJZ14gbNjWDuUq3ui33ZPLZTgCEA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"01bf9d75-92a4-46c1-8169-08f141b11c59\" } \r\n","poolAddress":"2baC5JL75NosEr2oLJZ14gbNjWDuUq3ui33ZPLZTgCEA"}
2025-08-13 13:44:23 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7cjgE6PwoNqfXjayUvUDE7TCS2GmPNnP1jCT29jT3JVJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"13a105af-c01a-4764-8622-737219c07db8\" } \r\n","poolAddress":"7cjgE6PwoNqfXjayUvUDE7TCS2GmPNnP1jCT29jT3JVJ"}
2025-08-13 13:44:23 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5tho4By9RsqTF1rbm9Akiepik3kZBT7ffUzGg8bL1mD: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"8459ee44-cda8-458a-b578-74d60a5cb1d3\" } \r\n","poolAddress":"5tho4By9RsqTF1rbm9Akiepik3kZBT7ffUzGg8bL1mD"}
2025-08-13 13:44:23 [ERROR]: Parse pool data error: {"error":"failed to get info about account DeAWLtRGAqCW7imV1jSC9NZkRKKGStFC1U7eDFBRxryR: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"938b047d-cfa5-4b62-b920-b75aab988764\" } \r\n","poolAddress":"DeAWLtRGAqCW7imV1jSC9NZkRKKGStFC1U7eDFBRxryR"}
2025-08-13 13:44:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9Jxk9iyKNyoubTd7ii2XADWHCDd1DrJuux3iQVsXbFcX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"b7ca722f-1ca0-40df-931f-bbb50de5d407\" } \r\n","poolAddress":"9Jxk9iyKNyoubTd7ii2XADWHCDd1DrJuux3iQVsXbFcX"}
2025-08-13 13:44:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7532cc88-a53c-42cd-b84d-bd1ad85d4e57\" } \r\n","poolAddress":"AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk"}
2025-08-13 13:44:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4Ro3pG1XZgSJENfgCccNgQqrHYVqhHjwcL27oHmXMMTG: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"07218ecf-0da9-4bac-8b5d-b956fe169782\" } \r\n","poolAddress":"4Ro3pG1XZgSJENfgCccNgQqrHYVqhHjwcL27oHmXMMTG"}
2025-08-13 13:44:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2baC5JL75NosEr2oLJZ14gbNjWDuUq3ui33ZPLZTgCEA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a21e6144-0e72-4c3d-a872-941e09f6bd30\" } \r\n","poolAddress":"2baC5JL75NosEr2oLJZ14gbNjWDuUq3ui33ZPLZTgCEA"}
2025-08-13 13:44:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account 81AHsVyKA89mmp3aQnQtUnfxGvgM6ZnFCdtnaT9Fs5tc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9e2b0a14-c055-4f38-b5ca-3cd9694e75f4\" } \r\n","poolAddress":"81AHsVyKA89mmp3aQnQtUnfxGvgM6ZnFCdtnaT9Fs5tc"}
2025-08-13 13:44:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3f3QtzjwEg8GoSMg3mabD71v13EAbqnACPuyPFCqtwYi: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"64324a14-2576-48c5-8d71-e2901e78e152\" } \r\n","poolAddress":"3f3QtzjwEg8GoSMg3mabD71v13EAbqnACPuyPFCqtwYi"}
2025-08-13 13:44:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5N96fYr2ZzaiNyZaYGAi31xvT5ExzfPnwh2s95yaXXSv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fd0c9df5-f108-44c6-b152-eb386f1d4166\" } \r\n","poolAddress":"5N96fYr2ZzaiNyZaYGAi31xvT5ExzfPnwh2s95yaXXSv"}
2025-08-13 13:44:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account AraN3NQsK76nkHGjZSV1eVTSN4W32CJhgEZiTq6XvhR3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6f5e6f54-36ff-4363-a56c-5fda1a0a2eec\" } \r\n","poolAddress":"AraN3NQsK76nkHGjZSV1eVTSN4W32CJhgEZiTq6XvhR3"}
2025-08-13 13:44:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2baC5JL75NosEr2oLJZ14gbNjWDuUq3ui33ZPLZTgCEA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"dc09852e-2b26-4506-946b-408e28e2ba5f\" } \r\n","poolAddress":"2baC5JL75NosEr2oLJZ14gbNjWDuUq3ui33ZPLZTgCEA"}
2025-08-13 13:44:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5tho4By9RsqTF1rbm9Akiepik3kZBT7ffUzGg8bL1mD: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"32024e8d-6509-4cdf-89db-97ab06db5242\" } \r\n","poolAddress":"5tho4By9RsqTF1rbm9Akiepik3kZBT7ffUzGg8bL1mD"}
2025-08-13 13:44:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account AVs9TA4nWDzfPJE9gGVNJMVhcQy3V9PGazuz33BfG2RA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fbc601e6-fd9f-4368-ae90-5c0492b9a206\" } \r\n","poolAddress":"AVs9TA4nWDzfPJE9gGVNJMVhcQy3V9PGazuz33BfG2RA"}
2025-08-13 13:44:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account Gu4fiMYj9GCGyaJCi5Ua3w8DHQd3bdsipRn2mbxWMwGy: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ad01e53c-d32d-43af-84bf-c42fb2e4a08d\" } \r\n","poolAddress":"Gu4fiMYj9GCGyaJCi5Ua3w8DHQd3bdsipRn2mbxWMwGy"}
2025-08-13 13:44:25 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7a6PvzFGCCN12wBsZ5D53SrHoEEoLAJ19dGunYWMGXAH: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"738d7d40-9e45-4965-96e0-364fbb7f59ff\" } \r\n","poolAddress":"7a6PvzFGCCN12wBsZ5D53SrHoEEoLAJ19dGunYWMGXAH"}
2025-08-13 13:44:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account FxV4siPjLHixVAx7FvpSNLGFcbH9KzhHwcRnAG9chU6c: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ad5b38f6-edb2-44af-81f2-3c410ab06744\" } \r\n","poolAddress":"FxV4siPjLHixVAx7FvpSNLGFcbH9KzhHwcRnAG9chU6c"}
2025-08-13 13:44:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account F4HDXnULqjSbWcL5VwEUcpjueQop3gDteusSqaXBEyW4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a120557d-379e-46ff-b7a9-902c754bb6bf\" } \r\n","poolAddress":"F4HDXnULqjSbWcL5VwEUcpjueQop3gDteusSqaXBEyW4"}
2025-08-13 13:44:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account FJUtc21eLo7k4E6eb86r5WB8ikD8Gizw3SJDPPoL7wjp: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fc5e7940-4153-4074-8154-c09eef16686e\" } \r\n","poolAddress":"FJUtc21eLo7k4E6eb86r5WB8ikD8Gizw3SJDPPoL7wjp"}
2025-08-13 13:44:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account ApaUAF25uNRen4P5w4b2mseEECVmfhxNYpdwbwge18MK: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8ee54e22-8859-4632-85f6-7746ed89a67c\" } \r\n","poolAddress":"ApaUAF25uNRen4P5w4b2mseEECVmfhxNYpdwbwge18MK"}
2025-08-13 13:44:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"548886ea-8f44-4b2d-8b35-2897efb183d0\" } \r\n","poolAddress":"AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk"}
2025-08-13 13:44:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account E2kV9pdw2bPXkbSGH7k33hQnAXrUcs31o7EWgoPJp2Cn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"27e9cff2-a05f-46b5-a4ca-4d5c837fb4aa\" } \r\n","poolAddress":"E2kV9pdw2bPXkbSGH7k33hQnAXrUcs31o7EWgoPJp2Cn"}
2025-08-13 13:44:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account GqtMj5BVuhcDb5y5ENzg1iebxweo6zjV28d1dSa7t2bK: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"64d1f4c3-d3f4-4c97-9e35-662a2c62e99f\" } \r\n","poolAddress":"GqtMj5BVuhcDb5y5ENzg1iebxweo6zjV28d1dSa7t2bK"}
2025-08-13 13:44:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCRty6J2toqf8EAiQSNpAeG21Qd1p8bymJpWWkBkAXkz: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"54da9ca3-2b4b-473e-bed9-91bd20c8884e\" } \r\n","poolAddress":"HCRty6J2toqf8EAiQSNpAeG21Qd1p8bymJpWWkBkAXkz"}
2025-08-13 13:44:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2RYCJgDVeXG91tVkPCFTdejotTpXGvkv1xjDeT8EmeVh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"674b5109-e2d1-4816-a982-ccaa2bc42260\" } \r\n","poolAddress":"2RYCJgDVeXG91tVkPCFTdejotTpXGvkv1xjDeT8EmeVh"}
2025-08-13 13:44:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"da41d8e9-f3d8-4d04-9cdc-7de96d7581a5\" } \r\n","poolAddress":"AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk"}
2025-08-13 13:44:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"457c2dd3-2304-4bbb-b972-a0838955c943\" } \r\n","poolAddress":"AAoREr9vexyGrinK6Z2GBcq5TKrNkZQXzkDpDDeXVaMk"}
2025-08-13 13:44:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account AB1eu2L1Jr3nfEft85AuD2zGksUbam1Kr8MR3uM2sjwt: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9b6c4716-3311-4455-b518-385966c990b3\" } \r\n","poolAddress":"AB1eu2L1Jr3nfEft85AuD2zGksUbam1Kr8MR3uM2sjwt"}
2025-08-13 13:44:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9vWVooeqk6udEHyioYr8Lvz6n8VZ45TkgKCtvneEVHCY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"90a67559-eb41-4118-add0-4643ce546eab\" } \r\n","poolAddress":"9vWVooeqk6udEHyioYr8Lvz6n8VZ45TkgKCtvneEVHCY"}
2025-08-13 13:44:26 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:26 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:26 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account CwTDbhxMPGG8biMdgDiHeTEywr4HDKawRtiPv7W1UDk3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9046e3b2-15d5-47d7-9f40-d168175cae64\" } \r\n","poolAddress":"CwTDbhxMPGG8biMdgDiHeTEywr4HDKawRtiPv7W1UDk3"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account 578CbhKnpAW5NjbmYku6qSaesZZLy3xwFQ8UkDANzd91: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c4ec756e-e7de-434e-857f-d612dc90eda7\" } \r\n","poolAddress":"578CbhKnpAW5NjbmYku6qSaesZZLy3xwFQ8UkDANzd91"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account CdgoDgZt4EMuYjC2DyfKCBF9EGAdBZFtvNmEd7oyVSTE: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c3888978-d0b0-462d-b593-e571ebcc3e65\" } \r\n","poolAddress":"CdgoDgZt4EMuYjC2DyfKCBF9EGAdBZFtvNmEd7oyVSTE"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account Eb9qkfiSzKd185KdWLkZrMrejKEbA2ah2BK7spNmoPej: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d800d2c7-73ed-427b-bd0a-ca32100b5041\" } \r\n","poolAddress":"Eb9qkfiSzKd185KdWLkZrMrejKEbA2ah2BK7spNmoPej"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account A1wy7BBM3vaNbTFg9sQSXmYJPf17T3Y1UGwLMd35NKXZ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"520b0d22-8c12-4045-a4d0-b04e71fb1077\" } \r\n","poolAddress":"A1wy7BBM3vaNbTFg9sQSXmYJPf17T3Y1UGwLMd35NKXZ"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account AemYRZmJryzAQ9Z4RLfUBLnPRUY5ecooc94EJvemfti4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ecb159df-6282-4c18-b667-176d7e3d1e69\" } \r\n","poolAddress":"AemYRZmJryzAQ9Z4RLfUBLnPRUY5ecooc94EJvemfti4"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5WUxQAPq7eZfoo2Z6qL3svXA9dJRoXqK2fsaFuRNWvqY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5a94f1b3-e286-4af7-a40b-e671e4908de1\" } \r\n","poolAddress":"5WUxQAPq7eZfoo2Z6qL3svXA9dJRoXqK2fsaFuRNWvqY"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account DVa7Qmb5ct9RCpaU7UTpSaf3GVMYz17vNVU67XpdCRut: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"126f7be1-230e-48d7-aad9-81aeecb964d3\" } \r\n","poolAddress":"DVa7Qmb5ct9RCpaU7UTpSaf3GVMYz17vNVU67XpdCRut"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4iuLYGsF3cYfNU3yGc1QVS3pmBG7tTBY1dk6genbJCKM: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8924ad2a-2b93-49b7-a0bf-f5fbfe33668c\" } \r\n","poolAddress":"4iuLYGsF3cYfNU3yGc1QVS3pmBG7tTBY1dk6genbJCKM"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account BEjPEZiTL2ndKEoHa8veyTNceCt5MzpaMZ8TdfpV7nBh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a05a9063-6108-43f5-8b5c-292078e7505f\" } \r\n","poolAddress":"BEjPEZiTL2ndKEoHa8veyTNceCt5MzpaMZ8TdfpV7nBh"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account FJUtc21eLo7k4E6eb86r5WB8ikD8Gizw3SJDPPoL7wjp: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ae17e7f5-e110-4c3e-8326-ac5b62cd8833\" } \r\n","poolAddress":"FJUtc21eLo7k4E6eb86r5WB8ikD8Gizw3SJDPPoL7wjp"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account y2VM9yTBEeLtkHjhMMei447V2BXunRy6K5bw9PEkP7Z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-5a58-4306-958b-69f7a740129f\" } \r\n","poolAddress":"y2VM9yTBEeLtkHjhMMei447V2BXunRy6K5bw9PEkP7Z"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account HL4KFTfhZZMm7NGefszQxzJ3M6CaDWE7TesyuRuQYmMJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9712c60d-9e3b-4bb5-ae0c-0da8718168b8\" } \r\n","poolAddress":"HL4KFTfhZZMm7NGefszQxzJ3M6CaDWE7TesyuRuQYmMJ"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account CUNvtQonaVH5unRQNpcaxRL3mH4pCBXwq6DEUJVGpxKx: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"001aa0bb-d73c-4ae3-a0e0-53b43182dd3c\" } \r\n","poolAddress":"CUNvtQonaVH5unRQNpcaxRL3mH4pCBXwq6DEUJVGpxKx"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account 96iU4qaPxqMny9Tp4p7FmySQFAxViNW5qutWuyCSXcxa: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a0d6ed17-ef67-4cbf-8911-b6e0fd69532f\" } \r\n","poolAddress":"96iU4qaPxqMny9Tp4p7FmySQFAxViNW5qutWuyCSXcxa"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account 63amWndBz75z2j7jyKDbzXvzt36L9qdGw7CZAXbD4KNe: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"23d17766-a7ba-4642-9e8e-ac93ecff1489\" } \r\n","poolAddress":"63amWndBz75z2j7jyKDbzXvzt36L9qdGw7CZAXbD4KNe"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account GjvW8JQSpKG5ogjyD3zozfaeJSShTajS5ZFrexT8L12k: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"642bfacd-2cd7-4926-857a-065c172855ab\" } \r\n","poolAddress":"GjvW8JQSpKG5ogjyD3zozfaeJSShTajS5ZFrexT8L12k"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6g65Drfejv98Ba1LYQAV44z5QmZAX4MzxhyBqsfLz6ZZ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f16d3d59-d9fe-43f2-b02b-e5ab8b009daf\" } \r\n","poolAddress":"6g65Drfejv98Ba1LYQAV44z5QmZAX4MzxhyBqsfLz6ZZ"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account GqtMj5BVuhcDb5y5ENzg1iebxweo6zjV28d1dSa7t2bK: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"947a3b67-cb9f-4446-9dd2-cc9fcd7b158b\" } \r\n","poolAddress":"GqtMj5BVuhcDb5y5ENzg1iebxweo6zjV28d1dSa7t2bK"}
2025-08-13 13:44:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2TEj7Y1chTcYs9zkaJe4vixnqtu78Pw1ycCUBj13zr9X: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a42ffb9e-703f-4a1e-808c-f3d30f7fe40c\" } \r\n","poolAddress":"2TEj7Y1chTcYs9zkaJe4vixnqtu78Pw1ycCUBj13zr9X"}
2025-08-13 13:44:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account BmsZE6TkZYskyS1PatPKRyyazGdxWFxdia4BuvLg9AgY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"43a6cad9-880e-45b7-8cc0-c0b0667be317\" } \r\n","poolAddress":"BmsZE6TkZYskyS1PatPKRyyazGdxWFxdia4BuvLg9AgY"}
2025-08-13 13:44:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account GqtMj5BVuhcDb5y5ENzg1iebxweo6zjV28d1dSa7t2bK: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ac1722ca-3683-498b-bae2-95233d900bbb\" } \r\n","poolAddress":"GqtMj5BVuhcDb5y5ENzg1iebxweo6zjV28d1dSa7t2bK"}
2025-08-13 13:44:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9tz6vYKiBDLYx2RnGWC5tESu4pyVE4jD6Tm56352UGte: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"dc9122db-8b80-4521-9ce1-8488a4ea51a1\" } \r\n","poolAddress":"9tz6vYKiBDLYx2RnGWC5tESu4pyVE4jD6Tm56352UGte"}
2025-08-13 13:44:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8WwcNqdZjCY5Pt7AkhupAFknV2txca9sq6YBkGzLbvdt: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4a7091f1-e4c7-4577-81c0-60aab341909b\" } \r\n","poolAddress":"8WwcNqdZjCY5Pt7AkhupAFknV2txca9sq6YBkGzLbvdt"}
2025-08-13 13:44:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account AQvd55ddq6sVcxzcLXbg85FjdZP9kzNPY55WNWyqZRnc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"efaa36b0-ffbd-4564-b0e7-2e45b3c85009\" } \r\n","poolAddress":"AQvd55ddq6sVcxzcLXbg85FjdZP9kzNPY55WNWyqZRnc"}
2025-08-13 13:44:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account 22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ed5f99c3-398a-4cb0-bfd1-c1ed579c2baa\" } \r\n","poolAddress":"22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd"}
2025-08-13 13:44:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9g33GBYw4BweTk6Bo6QbqHNsS9R8vwFSapsw7NcoZHzy: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"dc0a51c6-f52f-4ff5-850b-6aaf00228cc1\" } \r\n","poolAddress":"9g33GBYw4BweTk6Bo6QbqHNsS9R8vwFSapsw7NcoZHzy"}
2025-08-13 13:44:29 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:29 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account CzHTkDVXF9hLi6zZutAJWRcLxYnfbEqFUceP3pzmBYfr: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"049431fe-0c4a-4614-b588-7f2a6b1911ea\" } \r\n","poolAddress":"CzHTkDVXF9hLi6zZutAJWRcLxYnfbEqFUceP3pzmBYfr"}
2025-08-13 13:44:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account GAT9wnrKT3dagRaoqMuwtGMqM7tVYZHTtxSpaH5eFQKp: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e0285213-c639-4b1c-9a15-283dce96f28f\" } \r\n","poolAddress":"GAT9wnrKT3dagRaoqMuwtGMqM7tVYZHTtxSpaH5eFQKp"}
2025-08-13 13:44:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account HL4KFTfhZZMm7NGefszQxzJ3M6CaDWE7TesyuRuQYmMJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"407b3a92-c95e-4d82-8602-57c9625488af\" } \r\n","poolAddress":"HL4KFTfhZZMm7NGefszQxzJ3M6CaDWE7TesyuRuQYmMJ"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 48eKsoi9ZyQU4DbXM9pvcbgsfTxDWqhEVKuGzHAW3ucc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f2ca941f-582d-4207-9719-cc61b0ef1262\" } \r\n","poolAddress":"48eKsoi9ZyQU4DbXM9pvcbgsfTxDWqhEVKuGzHAW3ucc"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account A1wy7BBM3vaNbTFg9sQSXmYJPf17T3Y1UGwLMd35NKXZ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a30d097a-2f78-4d31-a4a8-d223e9078240\" } \r\n","poolAddress":"A1wy7BBM3vaNbTFg9sQSXmYJPf17T3Y1UGwLMd35NKXZ"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9dowAKn4HEko1SUzdo4T36fk6kZB7b76oe6CusaniECE: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"601db608-628c-4a5e-812b-4a55559fd537\" } \r\n","poolAddress":"9dowAKn4HEko1SUzdo4T36fk6kZB7b76oe6CusaniECE"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4jSC2pPsS4oM9BAzj3SzsbWBofKVQz66RfeQZrq3sDWy: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"33814b74-3c1a-473f-bd5d-201fb6bfa658\" } \r\n","poolAddress":"4jSC2pPsS4oM9BAzj3SzsbWBofKVQz66RfeQZrq3sDWy"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account BMBcZ9GWMCi9HaCE7BagrLxakzffy6fAGdEpihLRfVPw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"deef27f9-0500-4e79-8242-fb1104ea5212\" } \r\n","poolAddress":"BMBcZ9GWMCi9HaCE7BagrLxakzffy6fAGdEpihLRfVPw"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7GqEtF893LmBj7XLmpEAswvE6bpoBP4aYTQK9xMrfNwb: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"072ced33-4943-404e-a567-86e5c88c7a31\" } \r\n","poolAddress":"7GqEtF893LmBj7XLmpEAswvE6bpoBP4aYTQK9xMrfNwb"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9t1H1uDJ558iMPNkEPSN1fqkpC4XSPQ6cqSf6uEsTfTR: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f7a07a07-20f9-4e18-8404-6881ff44b899\" } \r\n","poolAddress":"9t1H1uDJ558iMPNkEPSN1fqkpC4XSPQ6cqSf6uEsTfTR"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3g7PgFfCj5snToTUKzjSfYAgveR9Q2GEfG1mcyezAsqx: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"38cfbffd-2d67-4300-9521-a981348dbaa6\" } \r\n","poolAddress":"3g7PgFfCj5snToTUKzjSfYAgveR9Q2GEfG1mcyezAsqx"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2TEj7Y1chTcYs9zkaJe4vixnqtu78Pw1ycCUBj13zr9X: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e13d9a4c-82ea-4d90-a7ee-1172f539b3c9\" } \r\n","poolAddress":"2TEj7Y1chTcYs9zkaJe4vixnqtu78Pw1ycCUBj13zr9X"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account Dfk133hHxjAA1yPryNkoPERGJ5DMpUtm79YeY1p1Wiyh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ceab007c-3ca4-4d41-8692-2ceb0225c681\" } \r\n","poolAddress":"Dfk133hHxjAA1yPryNkoPERGJ5DMpUtm79YeY1p1Wiyh"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account GjvW8JQSpKG5ogjyD3zozfaeJSShTajS5ZFrexT8L12k: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4aed873e-59c0-41da-aa4b-19bf249fa5fd\" } \r\n","poolAddress":"GjvW8JQSpKG5ogjyD3zozfaeJSShTajS5ZFrexT8L12k"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account J3bY9bwHvvEmn19tQFPpvVZg6wh9Kv2TuXrphYbiDRyL: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4e3df894-07d0-4022-828b-************\" } \r\n","poolAddress":"J3bY9bwHvvEmn19tQFPpvVZg6wh9Kv2TuXrphYbiDRyL"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account HZxMkJ53e3kD17pSQtu21C6gXae9qFdEXBFRujVsj9qb: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d6b3bd6c-2008-429c-8ccb-9305ded28610\" } \r\n","poolAddress":"HZxMkJ53e3kD17pSQtu21C6gXae9qFdEXBFRujVsj9qb"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account G7mw1d83ismcQJKkzt62Ug4noXCjVhu3eV7U5EMgge6Z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d54a5bdd-a38f-4376-81da-9d2e3d865835\" } \r\n","poolAddress":"G7mw1d83ismcQJKkzt62Ug4noXCjVhu3eV7U5EMgge6Z"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account J3bY9bwHvvEmn19tQFPpvVZg6wh9Kv2TuXrphYbiDRyL: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e533033b-ccff-4a1a-8931-acfdd84d1307\" } \r\n","poolAddress":"J3bY9bwHvvEmn19tQFPpvVZg6wh9Kv2TuXrphYbiDRyL"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account CzHTkDVXF9hLi6zZutAJWRcLxYnfbEqFUceP3pzmBYfr: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ca95d60a-751d-468d-8d0d-77475a294969\" } \r\n","poolAddress":"CzHTkDVXF9hLi6zZutAJWRcLxYnfbEqFUceP3pzmBYfr"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account R9WEuDqa3X54UqGY4SEXeJXEQ2J8ewAdChHKBRv3rse: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"eda023d0-a309-4097-a34f-a5d8cb4acf87\" } \r\n","poolAddress":"R9WEuDqa3X54UqGY4SEXeJXEQ2J8ewAdChHKBRv3rse"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account GAT9wnrKT3dagRaoqMuwtGMqM7tVYZHTtxSpaH5eFQKp: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e7589ce7-05cc-4b47-9f76-05eba0f254fc\" } \r\n","poolAddress":"GAT9wnrKT3dagRaoqMuwtGMqM7tVYZHTtxSpaH5eFQKp"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account J3bY9bwHvvEmn19tQFPpvVZg6wh9Kv2TuXrphYbiDRyL: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e3fb0805-b26e-42f4-a0d7-d6cc15476097\" } \r\n","poolAddress":"J3bY9bwHvvEmn19tQFPpvVZg6wh9Kv2TuXrphYbiDRyL"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6fVWstABXJD2vHc48wWEGMGdcjirJ5Q64K2PVdja7bDv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"524fd62f-3783-49de-822b-4dcc5d8e604b\" } \r\n","poolAddress":"6fVWstABXJD2vHc48wWEGMGdcjirJ5Q64K2PVdja7bDv"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account EcP9PszJZ6Rb9JDWn12mHGW35jCFCBLDsKCozKqQ9sW6: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"876e28ca-0ec1-4b8f-90fe-30cf6186b6c3\" } \r\n","poolAddress":"EcP9PszJZ6Rb9JDWn12mHGW35jCFCBLDsKCozKqQ9sW6"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account CzHTkDVXF9hLi6zZutAJWRcLxYnfbEqFUceP3pzmBYfr: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7a9ce512-20d5-4bdf-8cb7-8411e03bbcc7\" } \r\n","poolAddress":"CzHTkDVXF9hLi6zZutAJWRcLxYnfbEqFUceP3pzmBYfr"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5RnWKUsfX7MRSEdNWLFGLVzK65gFMrLpuTVJjePZaokD: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8f3686d2-bbf6-4976-860f-2d2d56715eff\" } \r\n","poolAddress":"5RnWKUsfX7MRSEdNWLFGLVzK65gFMrLpuTVJjePZaokD"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account Fhmjp6SMtmD8GXkq8tw6aZjBk3sDpKtKKSH6RToLAX3m: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9c415d9f-30c8-4390-a3e7-98f3d7ff80dc\" } \r\n","poolAddress":"Fhmjp6SMtmD8GXkq8tw6aZjBk3sDpKtKKSH6RToLAX3m"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5RnWKUsfX7MRSEdNWLFGLVzK65gFMrLpuTVJjePZaokD: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"76b9c65b-8a09-4ece-b788-7e5194460026\" } \r\n","poolAddress":"5RnWKUsfX7MRSEdNWLFGLVzK65gFMrLpuTVJjePZaokD"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account HQ9UFx3pDYCGMLAAPyg75ATfWNx9L6SDJgRQgnEow7hY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c9f5caef-ff26-4921-9606-20b5fd9da0d8\" } \r\n","poolAddress":"HQ9UFx3pDYCGMLAAPyg75ATfWNx9L6SDJgRQgnEow7hY"}
2025-08-13 13:44:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account A2gSHyKxGPdzp66v3HCCQUh8pWKQPBGixnjnQ3T975TK: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"63458a08-636b-4280-84a6-a65c1b936d52\" } \r\n","poolAddress":"A2gSHyKxGPdzp66v3HCCQUh8pWKQPBGixnjnQ3T975TK"}
2025-08-13 13:44:31 [ERROR]: Parse pool data error: {"error":"failed to get info about account DsD69qYsFvMX4cBvHbssGneB2aYwECkL3ehYjQ6NH6aq: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"90d677bb-db32-444d-9fae-dfcb2a91a682\" } \r\n","poolAddress":"DsD69qYsFvMX4cBvHbssGneB2aYwECkL3ehYjQ6NH6aq"}
2025-08-13 13:44:31 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9Tb2ohu5P16BpBarqd3N27WnkF51Ukfs8Z1GzzLDxVZW: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"129fe2bf-ad03-4cfb-81a2-778b81576b11\" } \r\n","poolAddress":"9Tb2ohu5P16BpBarqd3N27WnkF51Ukfs8Z1GzzLDxVZW"}
2025-08-13 13:44:31 [ERROR]: Parse pool data error: {"error":"failed to get info about account Di2FYotKzTV7Kwyyj476KDyk95ispN6jZYRQafwGGFet: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7dbf0a7d-d095-4bdd-b9d7-c85a4981bda4\" } \r\n","poolAddress":"Di2FYotKzTV7Kwyyj476KDyk95ispN6jZYRQafwGGFet"}
2025-08-13 13:44:32 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3f3QtzjwEg8GoSMg3mabD71v13EAbqnACPuyPFCqtwYi: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d153f575-7171-47f1-a0b2-522523b58d53\" } \r\n","poolAddress":"3f3QtzjwEg8GoSMg3mabD71v13EAbqnACPuyPFCqtwYi"}
2025-08-13 13:44:32 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6UmmUiYoBjSrhakAobJw8BvkmJtDVxaeBtbt7rxWo1mg: TypeError: fetch failed","poolAddress":"6UmmUiYoBjSrhakAobJw8BvkmJtDVxaeBtbt7rxWo1mg"}
2025-08-13 13:44:32 [ERROR]: Parse pool data error: {"error":"failed to get info about account 22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd: TypeError: fetch failed","poolAddress":"22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd"}
2025-08-13 13:44:32 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:33 [ERROR]: Parse pool data error: {"error":"failed to get info about account N2hyqVi5FvqqjPTxEYKUNqzjBVczNNxVWDQm1oK9Q91: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cbede0e6-71d8-4d24-921a-fbb60b4ef63f\" } \r\n","poolAddress":"N2hyqVi5FvqqjPTxEYKUNqzjBVczNNxVWDQm1oK9Q91"}
2025-08-13 13:44:33 [ERROR]: Parse pool data error: {"error":"failed to get info about account J3b6dvheS2Y1cbMtVz5TCWXNegSjJDbUKxdUVDPoqmS7: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-7bbf-4034-bc94-ca5c6d474aad\" } \r\n","poolAddress":"J3b6dvheS2Y1cbMtVz5TCWXNegSjJDbUKxdUVDPoqmS7"}
2025-08-13 13:44:33 [ERROR]: Parse pool data error: {"error":"failed to get info about account ABcrckEJSQxoiC7V1Gk7xG7UJp5JWrjxc5HYYEyPpB4Q: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f66a904f-6476-4803-9a3a-8f8d75c922b8\" } \r\n","poolAddress":"ABcrckEJSQxoiC7V1Gk7xG7UJp5JWrjxc5HYYEyPpB4Q"}
2025-08-13 13:44:33 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bx5pAzBqbuYH5yT93XCoShDka6d6vz9AzBddG7zE9syh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0402d11b-5e46-4781-b596-d5dd396bd133\" } \r\n","poolAddress":"Bx5pAzBqbuYH5yT93XCoShDka6d6vz9AzBddG7zE9syh"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account FPMgLords76953os1Y9HTnQc9NsScvspod6i2CZUwyvg: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"79e74a16-8399-4239-9276-0921adb60ae4\" } \r\n","poolAddress":"FPMgLords76953os1Y9HTnQc9NsScvspod6i2CZUwyvg"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account EiL4DDCYrEF4XcKyu6FvxK7qXE7u5MFqPrxbex5FzVz7: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b2f02e4b-0ea4-4333-8df1-11cf64dbb43f\" } \r\n","poolAddress":"EiL4DDCYrEF4XcKyu6FvxK7qXE7u5MFqPrxbex5FzVz7"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8ZeYHZg4iKWyrCdpyNgLKZuEkDC6AkQNT4q8q3zNFX4n: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fe13abe7-ebdf-4203-8652-192fe263ef9c\" } \r\n","poolAddress":"8ZeYHZg4iKWyrCdpyNgLKZuEkDC6AkQNT4q8q3zNFX4n"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5eLRsN6qDQTQSBF8KdW4B8mVpeeAzHCCwaDptzMyszxH: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cee04dbc-700e-4d3d-bce5-0b9594e2928c\" } \r\n","poolAddress":"5eLRsN6qDQTQSBF8KdW4B8mVpeeAzHCCwaDptzMyszxH"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7kct9m2zSDCFuv29yQveAz5FXnJpPX8JRxNX8BFnTUnW: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-9dea-4462-974d-2d345386f1ea\" } \r\n","poolAddress":"7kct9m2zSDCFuv29yQveAz5FXnJpPX8JRxNX8BFnTUnW"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3g7PgFfCj5snToTUKzjSfYAgveR9Q2GEfG1mcyezAsqx: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8cdaecee-4e08-469a-a128-001db864ef47\" } \r\n","poolAddress":"3g7PgFfCj5snToTUKzjSfYAgveR9Q2GEfG1mcyezAsqx"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account 96iU4qaPxqMny9Tp4p7FmySQFAxViNW5qutWuyCSXcxa: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0ec1620a-515d-4446-959f-3590a0151989\" } \r\n","poolAddress":"96iU4qaPxqMny9Tp4p7FmySQFAxViNW5qutWuyCSXcxa"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account 22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5fae038c-b5e0-4cfa-8190-9d5cab9f8632\" } \r\n","poolAddress":"22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account 578CbhKnpAW5NjbmYku6qSaesZZLy3xwFQ8UkDANzd91: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"82ab102f-7596-49d4-b532-0e5a778a3b27\" } \r\n","poolAddress":"578CbhKnpAW5NjbmYku6qSaesZZLy3xwFQ8UkDANzd91"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account HJwicGex1rQLsxoQJCRDSZNCcfzQHG1gu1MLpyMXVchY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b291dbcb-00be-48bf-911d-f572ca896d75\" } \r\n","poolAddress":"HJwicGex1rQLsxoQJCRDSZNCcfzQHG1gu1MLpyMXVchY"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account FdEg8pVv6REHx3hekzUYkjiQF8U2bavhVVeU2KPDE2kq: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"477addd9-21a3-493b-a1c9-1a840e54e103\" } \r\n","poolAddress":"FdEg8pVv6REHx3hekzUYkjiQF8U2bavhVVeU2KPDE2kq"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9Tb2ohu5P16BpBarqd3N27WnkF51Ukfs8Z1GzzLDxVZW: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9ebd6547-4fb8-42a8-b7d2-bc7d4d2beb30\" } \r\n","poolAddress":"9Tb2ohu5P16BpBarqd3N27WnkF51Ukfs8Z1GzzLDxVZW"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account 578CbhKnpAW5NjbmYku6qSaesZZLy3xwFQ8UkDANzd91: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a8a74849-7fbe-4e1f-b434-901c5d387410\" } \r\n","poolAddress":"578CbhKnpAW5NjbmYku6qSaesZZLy3xwFQ8UkDANzd91"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8LinWMnf5LVEVARqko7eUyydwzkrLdBUZgeNtu4fAFwA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8630e856-fdfa-47ca-a8f9-ff5b1b495406\" } \r\n","poolAddress":"8LinWMnf5LVEVARqko7eUyydwzkrLdBUZgeNtu4fAFwA"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account Fhmjp6SMtmD8GXkq8tw6aZjBk3sDpKtKKSH6RToLAX3m: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"515f4296-7117-41eb-b1f9-71e5aaf27427\" } \r\n","poolAddress":"Fhmjp6SMtmD8GXkq8tw6aZjBk3sDpKtKKSH6RToLAX3m"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account 22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0f8bac18-c96e-46f7-b2fc-fa7df9cbff96\" } \r\n","poolAddress":"22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account HzLxx6SoViXoqB2KYDGT1gWEckZ9ooRnv8n3xN6uvfez: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b40452ff-8ff3-45d7-a63e-d52870bef69f\" } \r\n","poolAddress":"HzLxx6SoViXoqB2KYDGT1gWEckZ9ooRnv8n3xN6uvfez"}
2025-08-13 13:44:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account 22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-5449-4cbc-8048-5d9aac7941e3\" } \r\n","poolAddress":"22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd"}
2025-08-13 13:44:34 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:34 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:34 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:36 [ERROR]: Parse pool data error: {"error":"failed to get info about account HD5WhvcRRBw4nkHr2owTatAbPqmJ2MKpZJXeBdgtXwnq: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d83e6378-ebd8-4d13-b09e-2d92483b7a7d\" } \r\n","poolAddress":"HD5WhvcRRBw4nkHr2owTatAbPqmJ2MKpZJXeBdgtXwnq"}
2025-08-13 13:44:36 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9ZJtr5c9nE2Cr2eG64Y7E1gYC2NykMssc71CRopkSpD4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e1c6dd66-ac49-4f9b-8c5e-08dbeed80af2\" } \r\n","poolAddress":"9ZJtr5c9nE2Cr2eG64Y7E1gYC2NykMssc71CRopkSpD4"}
2025-08-13 13:44:36 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9UUtkG2U657EpUmXsQPX3zvW2LTraejNBVc5U61tKNmb: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"50ba6b66-b74b-4ba2-bd91-15302b95c4bc\" } \r\n","poolAddress":"9UUtkG2U657EpUmXsQPX3zvW2LTraejNBVc5U61tKNmb"}
2025-08-13 13:44:36 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7n8sTMq6V5ZWnMLPtY6F2zaeUcwyLS4sF18DCCvs8MWB: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0de9b768-13af-4420-86f0-f281cce7bb5d\" } \r\n","poolAddress":"7n8sTMq6V5ZWnMLPtY6F2zaeUcwyLS4sF18DCCvs8MWB"}
2025-08-13 13:44:36 [ERROR]: Parse pool data error: {"error":"failed to get info about account HBdfVKjLN79veZMUvH9JWk7mUsVYSmZQEkooUb78opew: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7433cf7d-5924-4a05-96e1-a34e898dbf60\" } \r\n","poolAddress":"HBdfVKjLN79veZMUvH9JWk7mUsVYSmZQEkooUb78opew"}
2025-08-13 13:44:36 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5uTwG3y3F5cx4YkodgTjWEHDrX5HDKZ5bZZ72x8eQ6zE: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"65ed97f5-5b8a-4d01-98f0-d3e9ff7c77b3\" } \r\n","poolAddress":"5uTwG3y3F5cx4YkodgTjWEHDrX5HDKZ5bZZ72x8eQ6zE"}
2025-08-13 13:44:37 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:37 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:37 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8XtmZdMYzYKY17aykV2BacCtwuuUMGyZDBqpRAC5dd4W: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8b5c4048-7357-462c-8da1-0359195442e6\" } \r\n","poolAddress":"8XtmZdMYzYKY17aykV2BacCtwuuUMGyZDBqpRAC5dd4W"}
2025-08-13 13:44:37 [ERROR]: Parse pool data error: {"error":"failed to get info about account GjvW8JQSpKG5ogjyD3zozfaeJSShTajS5ZFrexT8L12k: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bdc89d79-434a-4af5-9a49-d6674abab658\" } \r\n","poolAddress":"GjvW8JQSpKG5ogjyD3zozfaeJSShTajS5ZFrexT8L12k"}
2025-08-13 13:44:37 [ERROR]: Parse pool data error: {"error":"failed to get info about account DSYeDak8DVYN8inDsCTV2hA69mVrFdBgmeMHgqeR2wH3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cbe4cc94-634b-4bae-a062-3e6c1239d2ee\" } \r\n","poolAddress":"DSYeDak8DVYN8inDsCTV2hA69mVrFdBgmeMHgqeR2wH3"}
2025-08-13 13:44:38 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9t1H1uDJ558iMPNkEPSN1fqkpC4XSPQ6cqSf6uEsTfTR: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9badddc0-06af-4dea-8dec-6ee7a1fe97b6\" } \r\n","poolAddress":"9t1H1uDJ558iMPNkEPSN1fqkpC4XSPQ6cqSf6uEsTfTR"}
2025-08-13 13:44:38 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4qQM2x2pfhU3ToscAqkQxTfhTm7DmJe8LGWU9kvqeNH4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"add563fc-6726-4cc7-a322-333d837db73a\" } \r\n","poolAddress":"4qQM2x2pfhU3ToscAqkQxTfhTm7DmJe8LGWU9kvqeNH4"}
2025-08-13 13:44:38 [ERROR]: Parse pool data error: {"error":"failed to get info about account HCJLC4yAeeejoBZJSeX9M1YFMTJSqQzEVwh96h4CW2MZ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d5d28d8f-b55e-40f1-8372-0e9e8b453874\" } \r\n","poolAddress":"HCJLC4yAeeejoBZJSeX9M1YFMTJSqQzEVwh96h4CW2MZ"}
2025-08-13 13:44:38 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5def4d67-b713-4a32-9a98-5e1c4283663b\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:38 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9t1H1uDJ558iMPNkEPSN1fqkpC4XSPQ6cqSf6uEsTfTR: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"235ece3f-8f81-4731-9fd5-d20fdb7b40ee\" } \r\n","poolAddress":"9t1H1uDJ558iMPNkEPSN1fqkpC4XSPQ6cqSf6uEsTfTR"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account GAT9wnrKT3dagRaoqMuwtGMqM7tVYZHTtxSpaH5eFQKp: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d5a5bebe-4639-45b3-8371-b02894bb5fb0\" } \r\n","poolAddress":"GAT9wnrKT3dagRaoqMuwtGMqM7tVYZHTtxSpaH5eFQKp"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account 22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f45061b7-1e51-48d2-bba2-d3ec39204c5f\" } \r\n","poolAddress":"22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account FFc9FUVwMZTR2AVET8E8D67sYty51HQga2GcFvn9UfDu: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fad68436-20d3-4ffe-8dde-e699504bbe9b\" } \r\n","poolAddress":"FFc9FUVwMZTR2AVET8E8D67sYty51HQga2GcFvn9UfDu"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6g65Drfejv98Ba1LYQAV44z5QmZAX4MzxhyBqsfLz6ZZ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"98c50716-18bc-48d2-8205-5b6a2ebcda98\" } \r\n","poolAddress":"6g65Drfejv98Ba1LYQAV44z5QmZAX4MzxhyBqsfLz6ZZ"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account DmAsjXoceoL5vTKZbYpTpXPo7MKm16FMfNMm3PJFiUha: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"994410bf-6a7d-4606-a9b0-28c37784f8a3\" } \r\n","poolAddress":"DmAsjXoceoL5vTKZbYpTpXPo7MKm16FMfNMm3PJFiUha"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ebb81c02-e512-4ec7-a0bf-b9b247cf5995\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account DmAsjXoceoL5vTKZbYpTpXPo7MKm16FMfNMm3PJFiUha: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a8fcc726-ff26-4b3f-a85e-5a839323b229\" } \r\n","poolAddress":"DmAsjXoceoL5vTKZbYpTpXPo7MKm16FMfNMm3PJFiUha"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2abNoqYqbH5uqgLnmHiQQYyvcth9pGUvuxGjAxKoGrbm: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"19e468bb-96bb-4ba2-88a0-9c80ae524434\" } \r\n","poolAddress":"2abNoqYqbH5uqgLnmHiQQYyvcth9pGUvuxGjAxKoGrbm"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4yrHms7ekgTBgJg77zJ33TsWrraqHsCXDtuSZqUsuGHb: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9eac5ce7-f3db-4994-860e-a64fbcfd558d\" } \r\n","poolAddress":"4yrHms7ekgTBgJg77zJ33TsWrraqHsCXDtuSZqUsuGHb"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9t1H1uDJ558iMPNkEPSN1fqkpC4XSPQ6cqSf6uEsTfTR: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"82ec8dc7-047f-46db-ae4e-ac32a4e38f40\" } \r\n","poolAddress":"9t1H1uDJ558iMPNkEPSN1fqkpC4XSPQ6cqSf6uEsTfTR"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account CrSxt6CfJoo3GqNG2TNkWgEDRiwzrgh4nZYp2RY1m5u1: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fe327a0d-ede9-4ae3-a70c-510b529f9e91\" } \r\n","poolAddress":"CrSxt6CfJoo3GqNG2TNkWgEDRiwzrgh4nZYp2RY1m5u1"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4AZRPNEfCJ7iw28rJu5aUyeQhYcvdcNm8cswyL51AY9i: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1aca19e4-6ade-416f-bde4-a5f3d3104497\" } \r\n","poolAddress":"4AZRPNEfCJ7iw28rJu5aUyeQhYcvdcNm8cswyL51AY9i"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3b8a2985-a8d0-4457-be4f-8f5664280621\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9t1H1uDJ558iMPNkEPSN1fqkpC4XSPQ6cqSf6uEsTfTR: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"576c9482-2068-4d02-96af-13aa2c8d1e8f\" } \r\n","poolAddress":"9t1H1uDJ558iMPNkEPSN1fqkpC4XSPQ6cqSf6uEsTfTR"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account C9yF5mhR4WzG5DumsPU2xc8CvNFwKRndJ3hGFuZnoYm7: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6a5773ce-2a36-45ff-a802-a1233dda4f1b\" } \r\n","poolAddress":"C9yF5mhR4WzG5DumsPU2xc8CvNFwKRndJ3hGFuZnoYm7"}
2025-08-13 13:44:39 [ERROR]: Parse pool data error: {"error":"failed to get info about account 22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5afc0011-c635-4191-909b-20950c23f9d3\" } \r\n","poolAddress":"22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8idN93ZBpdtMp4672aS4GGMDy7LdVWCCXH7FKFdMw9P4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1d3492ac-dd9b-42e3-9e41-b31d84fe43b9\" } \r\n","poolAddress":"8idN93ZBpdtMp4672aS4GGMDy7LdVWCCXH7FKFdMw9P4"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2b4eeb7a-7d30-4f71-bb51-79761b2a611e\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3hsN6JardLYKL7SSAR69vXAdJw4y9zgz9YSN5s6nGL1b: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ef4a184b-267a-49c1-8a69-7359a4fc04ee\" } \r\n","poolAddress":"3hsN6JardLYKL7SSAR69vXAdJw4y9zgz9YSN5s6nGL1b"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"09049e53-6949-4cf8-8e24-55615201cab0\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account 24Axj19hbaM3qwG4XHNHbHQocHon2Hd8jQq55aJ5Pnm2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"86f04bfc-b4f0-442e-b742-7e04b1b73bda\" } \r\n","poolAddress":"24Axj19hbaM3qwG4XHNHbHQocHon2Hd8jQq55aJ5Pnm2"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4yrHms7ekgTBgJg77zJ33TsWrraqHsCXDtuSZqUsuGHb: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1c972566-c7a1-4909-914b-ffbed1cee757\" } \r\n","poolAddress":"4yrHms7ekgTBgJg77zJ33TsWrraqHsCXDtuSZqUsuGHb"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-b02e-4c25-a798-d6b0a0502b6c\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4qQM2x2pfhU3ToscAqkQxTfhTm7DmJe8LGWU9kvqeNH4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"248ddc02-8c1e-4581-81a6-5721476834a8\" } \r\n","poolAddress":"4qQM2x2pfhU3ToscAqkQxTfhTm7DmJe8LGWU9kvqeNH4"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9fmdkQipJK2teeUv53BMDXi52uRLbrEvV38K8GBNkiM7: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ebf7de99-908c-4a65-abfd-668a98d6a9da\" } \r\n","poolAddress":"9fmdkQipJK2teeUv53BMDXi52uRLbrEvV38K8GBNkiM7"}
2025-08-13 13:44:40 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2497bfe8-e352-4c69-a18b-561f4e0fcee7\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account CrSxt6CfJoo3GqNG2TNkWgEDRiwzrgh4nZYp2RY1m5u1: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a9e4452b-e999-408a-8be1-35d0d3a136a5\" } \r\n","poolAddress":"CrSxt6CfJoo3GqNG2TNkWgEDRiwzrgh4nZYp2RY1m5u1"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a0d08c5a-786a-45d6-a654-a100f23733a7\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3hsN6JardLYKL7SSAR69vXAdJw4y9zgz9YSN5s6nGL1b: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b0822f33-fbab-4caa-b13e-3fecbcf82a0d\" } \r\n","poolAddress":"3hsN6JardLYKL7SSAR69vXAdJw4y9zgz9YSN5s6nGL1b"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"37838d4b-989b-471b-8bd8-447eebef82c4\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9o5zGan2gkoQkDXUnhzWHhGgQ1nVvPN2no4XWCGfHEMz: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-58e8-4555-a246-3aa45ea2ced2\" } \r\n","poolAddress":"9o5zGan2gkoQkDXUnhzWHhGgQ1nVvPN2no4XWCGfHEMz"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"77c29c2b-10f5-4684-a60f-9917ed59adce\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account Cc8AZ2ydkPZaBXJ5btnABsnuKce1m7BoXgGtrsDW3ePY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"78340d9d-7b63-4f0d-8986-dddbc25555a7\" } \r\n","poolAddress":"Cc8AZ2ydkPZaBXJ5btnABsnuKce1m7BoXgGtrsDW3ePY"}
2025-08-13 13:44:41 [ERROR]: Parse pool data error: {"error":"failed to get info about account 22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d3037d34-f8c2-44b9-8644-98826060ebfc\" } \r\n","poolAddress":"22WrmyTj8x2TRVQen3fxxi2r4Rn6JDHWoMTpsSmn8RUd"}
2025-08-13 13:44:41 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2rVLiMUJnoPXMJTzriMW5xPkF1wLEc7aBE9FFHsibVQX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7cdf72ed-6161-4585-8bd4-4a868ad0fc3e\" } \r\n","poolAddress":"2rVLiMUJnoPXMJTzriMW5xPkF1wLEc7aBE9FFHsibVQX"}
2025-08-13 13:44:41 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"af482600-aca3-455a-8dee-adc056bffb5f\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:42 [ERROR]: Parse pool data error: {"error":"failed to get info about account HfCRQVZReS7VrcKdrx73UfHRHjakCfmHdWR5FabXZzR: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9ef4965d-84d6-4a1d-ab18-822c028fe04d\" } \r\n","poolAddress":"HfCRQVZReS7VrcKdrx73UfHRHjakCfmHdWR5FabXZzR"}
2025-08-13 13:44:42 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2720df2f-cd1e-4dc2-af09-5fa64d672c75\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:42 [ERROR]: Parse pool data error: {"error":"failed to get info about account AQvd55ddq6sVcxzcLXbg85FjdZP9kzNPY55WNWyqZRnc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cefcce67-6b7a-4e93-b7a1-d688ac8e1f7d\" } \r\n","poolAddress":"AQvd55ddq6sVcxzcLXbg85FjdZP9kzNPY55WNWyqZRnc"}
2025-08-13 13:44:42 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4961033f-a4d7-42f4-a44b-650ed3dfc0ee\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:42 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:43 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:43 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:43 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"32067b5a-7503-497b-936b-1d9029a75e77\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:43 [ERROR]: Parse pool data error: {"error":"failed to get info about account 93jUcHjoUAyQ1GPVdEHcVm2uCVro11mh8CeMNksfUTJC: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6ce0b30b-17fa-4f3d-911d-42fe17e8c1ea\" } \r\n","poolAddress":"93jUcHjoUAyQ1GPVdEHcVm2uCVro11mh8CeMNksfUTJC"}
2025-08-13 13:44:44 [ERROR]: Parse pool data error: {"error":"failed to get info about account 93jUcHjoUAyQ1GPVdEHcVm2uCVro11mh8CeMNksfUTJC: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f4ff735e-6947-441a-87be-c21bc44c2707\" } \r\n","poolAddress":"93jUcHjoUAyQ1GPVdEHcVm2uCVro11mh8CeMNksfUTJC"}
2025-08-13 13:44:44 [ERROR]: Parse pool data error: {"error":"failed to get info about account BcnbmPBnZpdZ5uE2AosHaaSe29EiP2KHJuo61VDW9HV: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bda68465-b7ed-4d8f-be9b-2629655c011c\" } \r\n","poolAddress":"BcnbmPBnZpdZ5uE2AosHaaSe29EiP2KHJuo61VDW9HV"}
2025-08-13 13:44:44 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"590ab262-f430-4892-bb6b-75165c4b09a1\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:44 [ERROR]: Parse pool data error: {"error":"failed to get info about account A1b61xWnT6jxK6AZnxC5EZjGP3cdkN1Fz6oLSm4sR8qk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"45716c85-8d63-4a56-a423-96fce220fbbe\" } \r\n","poolAddress":"A1b61xWnT6jxK6AZnxC5EZjGP3cdkN1Fz6oLSm4sR8qk"}
2025-08-13 13:44:44 [ERROR]: Parse pool data error: {"error":"failed to get info about account EKq7andGqaCrnFqqSpaSwHae9FJZBpc7A1WsNiCDQZhh: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9141287a-2aa8-445e-88e9-150ec58ab2d6\" } \r\n","poolAddress":"EKq7andGqaCrnFqqSpaSwHae9FJZBpc7A1WsNiCDQZhh"}
2025-08-13 13:44:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2c149fc6-620e-47f6-9a9a-374fea158836\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account DytPhz41hvhYn4t6FDcajVV11kjAEiFnuWSGsqBV2Lb4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a90ce89f-e61b-4bcf-88a4-b91330e15044\" } \r\n","poolAddress":"DytPhz41hvhYn4t6FDcajVV11kjAEiFnuWSGsqBV2Lb4"}
2025-08-13 13:44:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4qQM2x2pfhU3ToscAqkQxTfhTm7DmJe8LGWU9kvqeNH4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"69e62416-5e08-43ee-bd2a-89b6ea277b63\" } \r\n","poolAddress":"4qQM2x2pfhU3ToscAqkQxTfhTm7DmJe8LGWU9kvqeNH4"}
2025-08-13 13:44:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account AQvd55ddq6sVcxzcLXbg85FjdZP9kzNPY55WNWyqZRnc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e1fbd8d7-44ce-402c-a345-f8e7b8b5a952\" } \r\n","poolAddress":"AQvd55ddq6sVcxzcLXbg85FjdZP9kzNPY55WNWyqZRnc"}
2025-08-13 13:44:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8idN93ZBpdtMp4672aS4GGMDy7LdVWCCXH7FKFdMw9P4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-8f74-452d-ad33-b8b695653263\" } \r\n","poolAddress":"8idN93ZBpdtMp4672aS4GGMDy7LdVWCCXH7FKFdMw9P4"}
2025-08-13 13:44:46 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account DkEa1hKCyWaGUzpZGi5iRYNL9CzzhDJdUHP6pHmfmrw5: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e344ccef-2a7a-40d2-828e-819982807d7d\" } \r\n","poolAddress":"DkEa1hKCyWaGUzpZGi5iRYNL9CzzhDJdUHP6pHmfmrw5"}
2025-08-13 13:44:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"42f7fd28-b62a-4728-91c4-6607f23c39d2\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7n45btQhNu5expVQrevYxzNX5V7pikmBJvtJkKCfQxAb: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"15d12b18-775b-4233-897a-33560358ac9b\" } \r\n","poolAddress":"7n45btQhNu5expVQrevYxzNX5V7pikmBJvtJkKCfQxAb"}
2025-08-13 13:44:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7n45btQhNu5expVQrevYxzNX5V7pikmBJvtJkKCfQxAb: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9cb4b34e-b262-4004-acb0-5d8e33443ef0\" } \r\n","poolAddress":"7n45btQhNu5expVQrevYxzNX5V7pikmBJvtJkKCfQxAb"}
2025-08-13 13:44:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account DkEa1hKCyWaGUzpZGi5iRYNL9CzzhDJdUHP6pHmfmrw5: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"07d69332-5297-4d13-8dac-71f3ae37a51f\" } \r\n","poolAddress":"DkEa1hKCyWaGUzpZGi5iRYNL9CzzhDJdUHP6pHmfmrw5"}
2025-08-13 13:44:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"878a79ef-07fd-4a00-9c80-16d05df47c21\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7n45btQhNu5expVQrevYxzNX5V7pikmBJvtJkKCfQxAb: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fb014b27-6141-409f-ba7b-43c65e301f04\" } \r\n","poolAddress":"7n45btQhNu5expVQrevYxzNX5V7pikmBJvtJkKCfQxAb"}
2025-08-13 13:44:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account FkZ5EqsXvRzt2tvneCwkpTzJy1sXZjjf31F1ipQ7HtiV: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5860d3f4-ebb0-424c-8bc5-d80a0a4b8313\" } \r\n","poolAddress":"FkZ5EqsXvRzt2tvneCwkpTzJy1sXZjjf31F1ipQ7HtiV"}
2025-08-13 13:44:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account DDSGy3CxioL4QPuj8kiPzxYg37PH4e7hdbD5E1iPmzu3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9be8ede4-12eb-4a05-9849-790c7a6b570e\" } \r\n","poolAddress":"DDSGy3CxioL4QPuj8kiPzxYg37PH4e7hdbD5E1iPmzu3"}
2025-08-13 13:44:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account FkZ5EqsXvRzt2tvneCwkpTzJy1sXZjjf31F1ipQ7HtiV: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"755a1f85-2b94-4257-9bbf-702147603b25\" } \r\n","poolAddress":"FkZ5EqsXvRzt2tvneCwkpTzJy1sXZjjf31F1ipQ7HtiV"}
2025-08-13 13:44:48 [ERROR]: Parse pool data error: {"error":"failed to get info about account FkE4nxg2R62x35dymgqDidZnnJZ98QKTFLiCSCRBfayc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2d764d97-0b3a-417b-82c2-1861dc5209dd\" } \r\n","poolAddress":"FkE4nxg2R62x35dymgqDidZnnJZ98QKTFLiCSCRBfayc"}
2025-08-13 13:44:48 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2SxoorScsYvR7cvwKaTXMtE3HCquRFDnEJa29bbgKTpA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f985a3fb-d507-4fe5-b801-2f243e42c085\" } \r\n","poolAddress":"2SxoorScsYvR7cvwKaTXMtE3HCquRFDnEJa29bbgKTpA"}
2025-08-13 13:44:48 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"740e8128-bd9f-4d83-94f4-f64a6e6effc8\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:48 [ERROR]: Parse pool data error: {"error":"failed to get info about account BjkBxREU5J1gi9J4o68EMurMdkwkWNf8jhjkGMNUqRmr: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3c8a6e78-26f8-4a2f-a4e3-4d3460241f40\" } \r\n","poolAddress":"BjkBxREU5J1gi9J4o68EMurMdkwkWNf8jhjkGMNUqRmr"}
2025-08-13 13:44:48 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2h282gdoEDejWWtXTMfPxvMDVPqKb7rVf24b2gWS1cg4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e91373b3-2986-47ca-a2e2-1c36596273d5\" } \r\n","poolAddress":"2h282gdoEDejWWtXTMfPxvMDVPqKb7rVf24b2gWS1cg4"}
2025-08-13 13:44:48 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6KbLs3saYWK1fo69B9E27aZtZt72KE4zW3dLAsR2hDt9: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"598bb28e-e2e3-4f3a-851d-8cda71a5867d\" } \r\n","poolAddress":"6KbLs3saYWK1fo69B9E27aZtZt72KE4zW3dLAsR2hDt9"}
2025-08-13 13:44:48 [ERROR]: Parse pool data error: {"error":"failed to get info about account FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"936e10cd-558a-4e53-aeb1-5b2b86093de6\" } \r\n","poolAddress":"FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo"}
2025-08-13 13:44:49 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2WawvFGJo9bMwDJzfRyVdsUzybH9JSHLZhKZNgUu9jrZ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a873a329-5098-41d3-bdf4-a52c6e2f92b2\" } \r\n","poolAddress":"2WawvFGJo9bMwDJzfRyVdsUzybH9JSHLZhKZNgUu9jrZ"}
2025-08-13 13:44:49 [ERROR]: Parse pool data error: {"error":"failed to get info about account uE7Zt7HemuYaVftjiJUnRn5FjeA8u9xbvJY6uGVoVVc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-c526-4bda-9f00-3afae80cc23f\" } \r\n","poolAddress":"uE7Zt7HemuYaVftjiJUnRn5FjeA8u9xbvJY6uGVoVVc"}
2025-08-13 13:44:49 [ERROR]: Parse pool data error: {"error":"failed to get info about account bKr6d1L34sa7jt88PAK7sjpeotgs9cZyRtcQNRDRe2L: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f10238f7-51c2-4922-a27d-982881443eb6\" } \r\n","poolAddress":"bKr6d1L34sa7jt88PAK7sjpeotgs9cZyRtcQNRDRe2L"}
2025-08-13 13:44:49 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:50 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:51 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9qkMnNBuvu77CdqFZ9tTmNFniJcDdzzqfDpHu8AmWKXN: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d66252bc-3fde-432a-8089-445c50fa7249\" } \r\n","poolAddress":"9qkMnNBuvu77CdqFZ9tTmNFniJcDdzzqfDpHu8AmWKXN"}
2025-08-13 13:44:51 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2qagURk5JQwT2kanmtoXfCLPgB2uE4Bp2WSFcBaTizUk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2d2af6e8-5880-45a3-92ae-a27a54ffe7fe\" } \r\n","poolAddress":"2qagURk5JQwT2kanmtoXfCLPgB2uE4Bp2WSFcBaTizUk"}
2025-08-13 13:44:51 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4AZRPNEfCJ7iw28rJu5aUyeQhYcvdcNm8cswyL51AY9i: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cd6f499c-0bda-40ef-a059-17c70fea5462\" } \r\n","poolAddress":"4AZRPNEfCJ7iw28rJu5aUyeQhYcvdcNm8cswyL51AY9i"}
2025-08-13 13:44:52 [ERROR]: Parse pool data error: {"error":"failed to get info about account ApdE1dJxdGeNv9NFRwjtJP3idQXkCvnt1U5XxDmVUQT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c64d7c6f-3ff5-4c6d-8ef2-dd69d4e03344\" } \r\n","poolAddress":"ApdE1dJxdGeNv9NFRwjtJP3idQXkCvnt1U5XxDmVUQT"}
2025-08-13 13:44:53 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:53 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:55 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:44:56 [ERROR]: Parse pool data error: {"error":"failed to get info about account Bj6SamBCNp6uLGsVu8BGjiPBhCVwRHFouHUyYg77CzXx: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"18a6e99f-1423-47d8-9531-15e38ee1cf61\" } \r\n","poolAddress":"Bj6SamBCNp6uLGsVu8BGjiPBhCVwRHFouHUyYg77CzXx"}
2025-08-13 13:44:56 [ERROR]: Parse pool data error: {"error":"failed to get info about account ArzaZSdmvSQ5vw92umJSHFhL6pM1GYpmxwKzeRpxgdRy: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"483444be-e4c1-401d-9766-a94984b2d415\" } \r\n","poolAddress":"ArzaZSdmvSQ5vw92umJSHFhL6pM1GYpmxwKzeRpxgdRy"}
2025-08-13 13:44:57 [ERROR]: Parse pool data error: {"error":"failed to get info about account HMqJNKrVf24mUVgdWC4LjuLxWgMBrGU7HAWLji6hLaJQ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"********-072d-422d-9426-513d64a28fd6\" } \r\n","poolAddress":"HMqJNKrVf24mUVgdWC4LjuLxWgMBrGU7HAWLji6hLaJQ"}
2025-08-13 13:45:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7m7kxcMVhMpnaXRAhwmVHKLymZ8yC2XCZBXXNA6g4Ni2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0f08bdd2-b85a-43d6-ab64-26676b063ca7\" } \r\n","poolAddress":"7m7kxcMVhMpnaXRAhwmVHKLymZ8yC2XCZBXXNA6g4Ni2"}
2025-08-13 13:45:00 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account ErqEuyMPkuZNaLq51JEy7X7WauturMLywRLeFRUD2H94: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b8cd6f89-5ed5-4cca-bfba-27cf276e0131\" } \r\n","poolAddress":"ErqEuyMPkuZNaLq51JEy7X7WauturMLywRLeFRUD2H94"}
2025-08-13 13:45:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8ib8J7ETnctHjjKKCnfiqRzKUTC6iezi9KNtDyV6KyNa: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f04b82f3-b788-431b-a1c3-dd22f8c27fa5\" } \r\n","poolAddress":"8ib8J7ETnctHjjKKCnfiqRzKUTC6iezi9KNtDyV6KyNa"}
2025-08-13 13:45:03 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:03 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:04 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:05 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:08 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:08 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account CpWTQ5h2jetvoWHygpsh99Bh1UWsYzJHNsQ1omynfnmB: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"908350cc-1f31-4e26-9f57-44641d7fa0e6\" } \r\n","poolAddress":"CpWTQ5h2jetvoWHygpsh99Bh1UWsYzJHNsQ1omynfnmB"}
2025-08-13 13:45:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account ********************************************: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"054387b7-4864-4df2-8bf2-0570bd1c731c\" } \r\n","poolAddress":"********************************************"}
2025-08-13 13:45:10 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:15 [ERROR]: Parse pool data error: {"error":"failed to get info about account EXctbZCDmvM3XuhFSZxVid2qLLjjnToxvizTdx1kuuWS: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7bee78e6-614b-48ae-ab6b-7a57d5d30e6c\" } \r\n","poolAddress":"EXctbZCDmvM3XuhFSZxVid2qLLjjnToxvizTdx1kuuWS"}
2025-08-13 13:45:15 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:15 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:15 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8EqeJ7M9bAnvN96YciZj2o8x3cr8YwMRfCxXJpSmWibc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"d0d5a9b7-f28d-4db5-ab3c-2e8e5150460a\" } \r\n","poolAddress":"8EqeJ7M9bAnvN96YciZj2o8x3cr8YwMRfCxXJpSmWibc"}
2025-08-13 13:45:16 [ERROR]: Parse pool data error: {"error":"failed to get info about account 58fzJMbX5PatnfJPqWWsqkVFPRKptkbb5r2vCw4Qq3z9: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"78e41479-3995-4c7a-b466-351f2900cdf2\" } \r\n","poolAddress":"58fzJMbX5PatnfJPqWWsqkVFPRKptkbb5r2vCw4Qq3z9"}
2025-08-13 13:45:16 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8CN7kNZ4xYCyVR3NHUpy3McEunD5vV8coptcuNGZQoZ4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bd3a839c-4ac6-4579-ae7d-b5752e0d8ee2\" } \r\n","poolAddress":"8CN7kNZ4xYCyVR3NHUpy3McEunD5vV8coptcuNGZQoZ4"}
2025-08-13 13:45:16 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5FKh4iDorXcHPif1ehYr2isWdN2w5ujhMhL4LrVeNrd: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"23e67ced-a43f-401e-8af5-25cf675de568\" } \r\n","poolAddress":"5FKh4iDorXcHPif1ehYr2isWdN2w5ujhMhL4LrVeNrd"}
2025-08-13 13:45:16 [ERROR]: Parse pool data error: {"error":"failed to get info about account HdEr5QQvphykyPnHRMufEACs8vcUyeJa2hymax85AjMv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9b3fe98c-362d-4880-85b4-d06cf9cdf630\" } \r\n","poolAddress":"HdEr5QQvphykyPnHRMufEACs8vcUyeJa2hymax85AjMv"}
2025-08-13 13:45:16 [ERROR]: Parse pool data error: {"error":"failed to get info about account Y8YyWu9gyCYSomE99JkDvsfR4eHNEeQpWtR8quGpBwX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"60309d3d-f03e-47c0-b361-6bf323bc3894\" } \r\n","poolAddress":"Y8YyWu9gyCYSomE99JkDvsfR4eHNEeQpWtR8quGpBwX"}
2025-08-13 13:45:16 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5hDVek6pZbg6m6K1tkNcdetPNFp28NP2NSmGrpPKVBcb: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a46941a6-a427-430b-a0b5-7e6cc8f10ac8\" } \r\n","poolAddress":"5hDVek6pZbg6m6K1tkNcdetPNFp28NP2NSmGrpPKVBcb"}
2025-08-13 13:45:17 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account 44ML4erfVyj6LLnLfD1dN7F5Z5cXhpssXRAVdJdifUTn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1d61b181-17cd-4093-b704-aadce56b96b4\" } \r\n","poolAddress":"44ML4erfVyj6LLnLfD1dN7F5Z5cXhpssXRAVdJdifUTn"}
2025-08-13 13:45:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account F1FMsNYuCNRHTDVjSkNbiZLp4qv6r6oQyMRJM9ZYdkm3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4acdbb86-94d3-49de-813b-8e2552e91212\" } \r\n","poolAddress":"F1FMsNYuCNRHTDVjSkNbiZLp4qv6r6oQyMRJM9ZYdkm3"}
2025-08-13 13:45:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account F73euqPynBwrgcZn3fNSEneSnYasDQohPM5aZazW9hp2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"83d25044-cf2d-4409-b501-a49b942927eb\" } \r\n","poolAddress":"F73euqPynBwrgcZn3fNSEneSnYasDQohPM5aZazW9hp2"}
2025-08-13 13:45:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5zf4J2N2RGtUai7unfWEp45RqzeDW2QncRvtXqaaXUSc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"632f76d3-0fef-4de1-8d7f-cf43093ff401\" } \r\n","poolAddress":"5zf4J2N2RGtUai7unfWEp45RqzeDW2QncRvtXqaaXUSc"}
2025-08-13 13:45:19 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2Z9SGDsHWvdKddAkfQS5QJ7ecaj18cwcHWcsDy9CrwuN: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a157263f-e627-47c5-967c-e2d43ba18428\" } \r\n","poolAddress":"2Z9SGDsHWvdKddAkfQS5QJ7ecaj18cwcHWcsDy9CrwuN"}
2025-08-13 13:45:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2hwbkSnGtDUpJPymXeLu1Tuh2rHV7W9xQTZG5KuYyJge: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"80ee6604-42db-4012-abfc-140374e95e6a\" } \r\n","poolAddress":"2hwbkSnGtDUpJPymXeLu1Tuh2rHV7W9xQTZG5KuYyJge"}
2025-08-13 13:45:25 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2GtAgPyss2tCr44FAdgwSFdjLy2rUwSb8ePgX2N5wzbc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"21ac21cf-ee0c-4689-9359-0dbb5636424a\" } \r\n","poolAddress":"2GtAgPyss2tCr44FAdgwSFdjLy2rUwSb8ePgX2N5wzbc"}
2025-08-13 13:45:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account HTLdQz4PAQbza7Topf8FnCdRDeBPy2TAHZf4HYtvikSk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"********-be12-419c-b0fd-85aab2a678a0\" } \r\n","poolAddress":"HTLdQz4PAQbza7Topf8FnCdRDeBPy2TAHZf4HYtvikSk"}
2025-08-13 13:45:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account Hga48QXtpCgLSTsfysDirPJzq8aoBPjvePUgmXhFGDro: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"e727432b-d308-4f52-a4b1-2a4f30ee4184\" } \r\n","poolAddress":"Hga48QXtpCgLSTsfysDirPJzq8aoBPjvePUgmXhFGDro"}
2025-08-13 13:45:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account CzWqL4M1CzQiw45djF1xdrWHQmuJbJoZPY38ezKGb6q8: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1dae945d-a7e1-4934-aff2-33d1b390b3f6\" } \r\n","poolAddress":"CzWqL4M1CzQiw45djF1xdrWHQmuJbJoZPY38ezKGb6q8"}
2025-08-13 13:45:26 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:26 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7VGxCx8QTpWu9gMWsFVmXc8Z8tBAsvW5mu2dUUfut4hc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"02d9128c-67b0-43ff-bdd6-2a8b8d723e34\" } \r\n","poolAddress":"7VGxCx8QTpWu9gMWsFVmXc8Z8tBAsvW5mu2dUUfut4hc"}
2025-08-13 13:45:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4w2iPn26d9UJAbQpoaAw34iFPta53fq6KvHr3cGfYsxa: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"78f6ec9b-2854-47c0-b884-be7290eaf6d8\" } \r\n","poolAddress":"4w2iPn26d9UJAbQpoaAw34iFPta53fq6KvHr3cGfYsxa"}
2025-08-13 13:45:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9rL8hBDrqGStgo5opiyyLZFStEvzi95izzUDRtBgqXaX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"45d6a6d0-81ca-448d-aa70-ebd7ec628ce8\" } \r\n","poolAddress":"9rL8hBDrqGStgo5opiyyLZFStEvzi95izzUDRtBgqXaX"}
2025-08-13 13:45:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account EDeE65ozvB2G6xKFSvrtUqbqB9CCPiJm7Ejx9mqWDy4i: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c4a7fcc8-3113-4d36-a100-24bf3530aee9\" } \r\n","poolAddress":"EDeE65ozvB2G6xKFSvrtUqbqB9CCPiJm7Ejx9mqWDy4i"}
2025-08-13 13:45:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account FmLmM2C3svyMmRwzTmnEjGnA5cD4YBLuPoQPixqZJG15: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bb23597e-aca9-4c97-aabd-97b9fc2401a4\" } \r\n","poolAddress":"FmLmM2C3svyMmRwzTmnEjGnA5cD4YBLuPoQPixqZJG15"}
2025-08-13 13:45:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account DMn1nqyK3EpEGYVcTHEnysP4WKoyQQcBtEZ3amGyeutP: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d25f3fb4-a158-439f-a45f-aee69d2ac623\" } \r\n","poolAddress":"DMn1nqyK3EpEGYVcTHEnysP4WKoyQQcBtEZ3amGyeutP"}
2025-08-13 13:45:27 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:28 [ERROR]: Parse pool data error: {"error":"failed to get info about account DSUvc5qf5LJHHV5e2tD184ixotSnCnwj7i4jJa4Xsrmt: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"918f70da-5f1e-4246-9c96-b9cec05892a0\" } \r\n","poolAddress":"DSUvc5qf5LJHHV5e2tD184ixotSnCnwj7i4jJa4Xsrmt"}
2025-08-13 13:45:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9qJpZcMnGD6NqrKC2usxQ3wie5gRwXxaexWUHx6w8vym: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"dfaf3df2-bc76-4288-8382-a01584076761\" } \r\n","poolAddress":"9qJpZcMnGD6NqrKC2usxQ3wie5gRwXxaexWUHx6w8vym"}
2025-08-13 13:45:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4GhqgtbCADa2UHjvAfhLvVKpE1g1uDpoBbEEZ1UnNPwu: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"cb7e5a2b-b718-49f6-aab8-f9463c3b2766\" } \r\n","poolAddress":"4GhqgtbCADa2UHjvAfhLvVKpE1g1uDpoBbEEZ1UnNPwu"}
2025-08-13 13:45:30 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:30 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4RJ3K3JRPYyFpmEYboFoMbpQfravq8TVfmgcCEsmVvvs: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7b8f23a6-0ad0-419d-b6aa-a647465a63cf\" } \r\n","poolAddress":"4RJ3K3JRPYyFpmEYboFoMbpQfravq8TVfmgcCEsmVvvs"}
2025-08-13 13:45:32 [ERROR]: Parse pool data error: {"error":"failed to get info about account G4XVreGygU5LCyzgNzt9u6yeb9xGCqeYKDeNC4TuWFYy: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0d0a07a5-753c-4aca-af93-7f2f348e77a9\" } \r\n","poolAddress":"G4XVreGygU5LCyzgNzt9u6yeb9xGCqeYKDeNC4TuWFYy"}
2025-08-13 13:45:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account CgByjroR5PpY46QszJba8DqipgG9bKM9GMRRyeLibmXv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"91ce4f55-647e-41bb-9d9f-db5292ebb8c4\" } \r\n","poolAddress":"CgByjroR5PpY46QszJba8DqipgG9bKM9GMRRyeLibmXv"}
2025-08-13 13:45:34 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:35 [ERROR]: Parse pool data error: {"error":"failed to get info about account GqpCwsBbmcCPwwPaJevdsfK9M5zeAoDBd96yxHUpdgrW: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c007d359-82a2-4bd4-8008-cde5993e3738\" } \r\n","poolAddress":"GqpCwsBbmcCPwwPaJevdsfK9M5zeAoDBd96yxHUpdgrW"}
2025-08-13 13:45:35 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9UMuN94bbuH53F4PTVWDYZoQjsJ3zgEx2j2vtT5Rbo1x: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"365770d3-4897-4481-a683-26b09f8a610e\" } \r\n","poolAddress":"9UMuN94bbuH53F4PTVWDYZoQjsJ3zgEx2j2vtT5Rbo1x"}
2025-08-13 13:45:35 [ERROR]: Parse pool data error: {"error":"failed to get info about account Fo7VNHaddVnMX4Axjo7CC1wWb9Ko2Pk2dFDzL3dYbXKp: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"99ab3457-0d95-4d1a-b814-7d1befeb922d\" } \r\n","poolAddress":"Fo7VNHaddVnMX4Axjo7CC1wWb9Ko2Pk2dFDzL3dYbXKp"}
2025-08-13 13:45:35 [ERROR]: Parse pool data error: {"error":"failed to get info about account Fo7VNHaddVnMX4Axjo7CC1wWb9Ko2Pk2dFDzL3dYbXKp: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"20b2792a-bcaa-4b29-a40a-5500158f7799\" } \r\n","poolAddress":"Fo7VNHaddVnMX4Axjo7CC1wWb9Ko2Pk2dFDzL3dYbXKp"}
2025-08-13 13:45:35 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3SjmTooiF5nF5ohgQjrySCSm4oYYaPbZWgGTqUFfiVPn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a4d47fc4-68f2-407f-9ae8-10c4371104a9\" } \r\n","poolAddress":"3SjmTooiF5nF5ohgQjrySCSm4oYYaPbZWgGTqUFfiVPn"}
2025-08-13 13:45:36 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:36 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:38 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7YnNZDcV53itSrkyXZoH2PoQcWDsrbQUs1ubgZfJCrCH: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4502abf7-c4ba-4a8d-bdb4-0a8cfb29324b\" } \r\n","poolAddress":"7YnNZDcV53itSrkyXZoH2PoQcWDsrbQUs1ubgZfJCrCH"}
2025-08-13 13:45:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2aPsSVxFw6dGRqWWUKfwujN6WVoyxuhjJaPzYaJvGDDR: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"37d21b21-921a-4ca2-a4d7-f1c82ee6bd3c\" } \r\n","poolAddress":"2aPsSVxFw6dGRqWWUKfwujN6WVoyxuhjJaPzYaJvGDDR"}
2025-08-13 13:45:41 [ERROR]: Parse pool data error: {"error":"failed to get info about account HzoZ2sSeshKTg6QdWQC9FY8jxBdpJQw34WbVFds1RWhc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"64c16c2e-8cf7-4f63-bc39-2fe6628fd1a6\" } \r\n","poolAddress":"HzoZ2sSeshKTg6QdWQC9FY8jxBdpJQw34WbVFds1RWhc"}
2025-08-13 13:45:41 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4AaoLPMjwhMQ5i1vGPXhXZ3Tm7FXaAYspnWCRLJdySPk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-39f9-4e71-bb8a-cad978b302fe\" } \r\n","poolAddress":"4AaoLPMjwhMQ5i1vGPXhXZ3Tm7FXaAYspnWCRLJdySPk"}
2025-08-13 13:45:42 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:43 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9kLGUEFwEuFzn9txDfGJ3FimGp9LjMtNPp4GvMLfkZSY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5a15cc82-2557-415f-9a9b-1316bf65fd0a\" } \r\n","poolAddress":"9kLGUEFwEuFzn9txDfGJ3FimGp9LjMtNPp4GvMLfkZSY"}
2025-08-13 13:45:45 [ERROR]: Parse pool data error: {"error":"failed to get info about account BVSiP9RR4T8ZqY4ZLkgsxNwKZoEVbB92RsWN5tCdtRox: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"587bd63b-d2da-49cc-bb8f-415a6327e802\" } \r\n","poolAddress":"BVSiP9RR4T8ZqY4ZLkgsxNwKZoEVbB92RsWN5tCdtRox"}
2025-08-13 13:45:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account 879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"06499d55-5409-47d3-a462-8f3ad436eef9\" } \r\n","poolAddress":"879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce"}
2025-08-13 13:45:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account DvcZpw5XekU8PunRMXNKoE5mNgUKoeYExQUXbGmMczjE: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d07b63bc-f0b6-42cf-a53a-b3db92058e2e\" } \r\n","poolAddress":"DvcZpw5XekU8PunRMXNKoE5mNgUKoeYExQUXbGmMczjE"}
2025-08-13 13:45:48 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:48 [ERROR]: Parse pool data error: {"error":"failed to get info about account 879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b488fa31-0999-4201-b90d-006ec5163233\" } \r\n","poolAddress":"879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce"}
2025-08-13 13:45:48 [ERROR]: Parse pool data error: {"error":"failed to get info about account 73wLBbQ3FnVx9GEEyTDaKEuVbau5KWP47aaYsPbsZuEc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4cd6cb6a-37d6-40d1-bcca-aa34f6c207a9\" } \r\n","poolAddress":"73wLBbQ3FnVx9GEEyTDaKEuVbau5KWP47aaYsPbsZuEc"}
2025-08-13 13:45:48 [ERROR]: Parse pool data error: {"error":"failed to get info about account BH2SWzKqSbo1EhrF6rMbzjzZTDCyqkh8wunjst95iD6N: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"88ca35c3-9aec-4843-989e-99657022c1cc\" } \r\n","poolAddress":"BH2SWzKqSbo1EhrF6rMbzjzZTDCyqkh8wunjst95iD6N"}
2025-08-13 13:45:49 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-2a43-4d26-a485-a021175f8ef0\" } \r\n","poolAddress":"879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce"}
2025-08-13 13:45:52 [ERROR]: Parse pool data error: {"error":"failed to get info about account 879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"043cfd58-3a97-4930-8d34-584d65d51dce\" } \r\n","poolAddress":"879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce"}
2025-08-13 13:45:52 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:52 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:54 [ERROR]: Parse pool data error: {"error":"failed to get info about account pHpLmu4kkDctD449gm3RQ6etoy6yTMyeZgPBMZ5fP13: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9cc9cff5-44d9-40a5-957f-e95e80586deb\" } \r\n","poolAddress":"pHpLmu4kkDctD449gm3RQ6etoy6yTMyeZgPBMZ5fP13"}
2025-08-13 13:45:55 [ERROR]: Parse pool data error: {"error":"failed to get info about account H2W2CeByodEEZG1BF4RVa7JWQosBGFmiibAxcAhNhtKA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c5cde218-e133-4ec7-bfb8-2a0e237fef4f\" } \r\n","poolAddress":"H2W2CeByodEEZG1BF4RVa7JWQosBGFmiibAxcAhNhtKA"}
2025-08-13 13:45:55 [ERROR]: Parse pool data error: {"error":"failed to get info about account B4PHSkL6CeZbxVqm1aUPQuVpc5ERFcaZj7u9dhtphmVX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fb01533f-b8f4-4bf9-ae9c-c242b1c7e941\" } \r\n","poolAddress":"B4PHSkL6CeZbxVqm1aUPQuVpc5ERFcaZj7u9dhtphmVX"}
2025-08-13 13:45:55 [ERROR]: Parse pool data error: {"error":"failed to get info about account 879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"09b530cf-2092-4d2c-a3a3-a79f99243b92\" } \r\n","poolAddress":"879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce"}
2025-08-13 13:45:55 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2dRNngAm729NzLbb1pzgHtfHvPqR4XHFmFyYK78EfEeX: TypeError: fetch failed","poolAddress":"2dRNngAm729NzLbb1pzgHtfHvPqR4XHFmFyYK78EfEeX"}
2025-08-13 13:45:56 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:45:59 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e61f26a7-1c24-413d-8d52-81b2a9e1e184\" } \r\n","poolAddress":"879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce"}
2025-08-13 13:46:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7DQ2tdyQVNHE68wbSLjuCgTh3JiBUZ1MT9UuuiqU1BPA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8b650b9f-3a40-45a5-bf23-e88e405a3ab9\" } \r\n","poolAddress":"7DQ2tdyQVNHE68wbSLjuCgTh3JiBUZ1MT9UuuiqU1BPA"}
2025-08-13 13:46:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account GnJRR5tTEJJ9Um5kkf7abMtUKxM24J95kmyoRFz8PCJz: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"74f3c988-f5a8-46b4-b0b1-f07413515518\" } \r\n","poolAddress":"GnJRR5tTEJJ9Um5kkf7abMtUKxM24J95kmyoRFz8PCJz"}
2025-08-13 13:46:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account DSUvc5qf5LJHHV5e2tD184ixotSnCnwj7i4jJa4Xsrmt: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e0c2242d-081d-4fdf-8b01-5194f220ddf4\" } \r\n","poolAddress":"DSUvc5qf5LJHHV5e2tD184ixotSnCnwj7i4jJa4Xsrmt"}
2025-08-13 13:46:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6d3YhKJSf1kxFiv5rNW8EZkL6vg2E8XgnMwNz3LQos8x: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ba14492a-f51f-48d2-8d00-a3dc77338430\" } \r\n","poolAddress":"6d3YhKJSf1kxFiv5rNW8EZkL6vg2E8XgnMwNz3LQos8x"}
2025-08-13 13:46:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0f7bf9b4-6145-4bd2-891e-e41b2f31b8eb\" } \r\n","poolAddress":"879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce"}
2025-08-13 13:46:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account DtTkLBvYUaYBZ7PC4vCwWfu56Zkgbf7ycEXxLhAP7Xx8: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"754d939f-2176-4c6e-a72f-64514ec6fd49\" } \r\n","poolAddress":"DtTkLBvYUaYBZ7PC4vCwWfu56Zkgbf7ycEXxLhAP7Xx8"}
2025-08-13 13:46:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"78eb9159-a2c0-4c55-b2d2-dcecbcbef4ef\" } \r\n","poolAddress":"879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce"}
2025-08-13 13:46:01 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2fBZk41iJ4S2sNHBkMf2uGUzY5YvKZTYihjqS2scwM9Y: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c8051608-36b2-46ab-94f9-ae3db3813eba\" } \r\n","poolAddress":"2fBZk41iJ4S2sNHBkMf2uGUzY5YvKZTYihjqS2scwM9Y"}
2025-08-13 13:46:02 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:02 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:03 [ERROR]: Parse pool data error: {"error":"failed to get info about account GP1TLVRBVfn5RuAZfzqRFA9dTy8EfpE76rbzZ5u2Y1n2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a8e84d6c-807b-4c2c-962f-07cde9ad9a93\" } \r\n","poolAddress":"GP1TLVRBVfn5RuAZfzqRFA9dTy8EfpE76rbzZ5u2Y1n2"}
2025-08-13 13:46:04 [ERROR]: Parse pool data error: {"error":"failed to get info about account 879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"eb4b9b8a-f2f3-4a96-a3d4-c705d9d8dbef\" } \r\n","poolAddress":"879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce"}
2025-08-13 13:46:04 [ERROR]: Parse pool data error: {"error":"failed to get info about account HbSgCfKD1WyS19gUBbn13oAWFvXtZ8CnfGbr35VVTvB5: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c25625fe-4e8a-41ba-b8fa-d7a8f97cef47\" } \r\n","poolAddress":"HbSgCfKD1WyS19gUBbn13oAWFvXtZ8CnfGbr35VVTvB5"}
2025-08-13 13:46:04 [ERROR]: Parse pool data error: {"error":"failed to get info about account 879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"75deae97-f91b-41da-a776-29894e3ff71f\" } \r\n","poolAddress":"879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce"}
2025-08-13 13:46:04 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7fbTnN92i1NGAcMqw7qtWfvZQ1M1NDfcv9Pc4bsyZ1qz: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ca653f2d-050e-4cb3-96c4-60cac290e5b7\" } \r\n","poolAddress":"7fbTnN92i1NGAcMqw7qtWfvZQ1M1NDfcv9Pc4bsyZ1qz"}
2025-08-13 13:46:04 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5WGYajM1xtLy3QrLHGSX4YPwsso3jrjEsbU1VivUErzk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7875072d-de1c-43ab-bc22-0081eb428b4f\" } \r\n","poolAddress":"5WGYajM1xtLy3QrLHGSX4YPwsso3jrjEsbU1VivUErzk"}
2025-08-13 13:46:04 [ERROR]: Parse pool data error: {"error":"failed to get info about account 879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"330f3fd8-703d-437f-9698-7883a48a9f82\" } \r\n","poolAddress":"879F697iuDJGMevRkRcnW21fcXiAeLJK1ffsw2ATebce"}
2025-08-13 13:46:04 [ERROR]: Parse pool data error: {"error":"failed to get info about account DQ4HHZ77LeRccybLmpq7xbkvqsz4PN7h8hQMHsw7ZWxX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5810aace-585c-4198-8446-f7e6da922fb3\" } \r\n","poolAddress":"DQ4HHZ77LeRccybLmpq7xbkvqsz4PN7h8hQMHsw7ZWxX"}
2025-08-13 13:46:04 [ERROR]: Parse pool data error: {"error":"failed to get info about account B4PHSkL6CeZbxVqm1aUPQuVpc5ERFcaZj7u9dhtphmVX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f2af98bb-a428-4ea4-a29b-28ce03cf8839\" } \r\n","poolAddress":"B4PHSkL6CeZbxVqm1aUPQuVpc5ERFcaZj7u9dhtphmVX"}
2025-08-13 13:46:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5WGYajM1xtLy3QrLHGSX4YPwsso3jrjEsbU1VivUErzk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"72191ac7-3150-4acc-b84f-61530175cb4e\" } \r\n","poolAddress":"5WGYajM1xtLy3QrLHGSX4YPwsso3jrjEsbU1VivUErzk"}
2025-08-13 13:46:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3hsdbMFsiCh3YCsXoFjgx4TVpxECsUE9nRMgvaoyveQT: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0dc9f972-e7b4-4b9c-8c97-db6e39477f90\" } \r\n","poolAddress":"3hsdbMFsiCh3YCsXoFjgx4TVpxECsUE9nRMgvaoyveQT"}
2025-08-13 13:46:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4wu1o8x5zNnHqj1tAqSHmfcwGaRrBw2htayJXePUK5Xo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9f59e45c-fb8c-43cc-b3ea-942bb7ff5351\" } \r\n","poolAddress":"4wu1o8x5zNnHqj1tAqSHmfcwGaRrBw2htayJXePUK5Xo"}
2025-08-13 13:46:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account B4PHSkL6CeZbxVqm1aUPQuVpc5ERFcaZj7u9dhtphmVX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1f3cd8c0-970c-4763-b127-5bfc1cfb2c08\" } \r\n","poolAddress":"B4PHSkL6CeZbxVqm1aUPQuVpc5ERFcaZj7u9dhtphmVX"}
2025-08-13 13:46:05 [ERROR]: Parse pool data error: {"error":"failed to get info about account 966sLyXCxj1HedfdFQsv2LiNHUaUhMNadBcD4kDcJ1MP: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bc06c79a-77f0-442a-8779-af8fd04a1b5f\" } \r\n","poolAddress":"966sLyXCxj1HedfdFQsv2LiNHUaUhMNadBcD4kDcJ1MP"}
2025-08-13 13:46:08 [ERROR]: Parse pool data error: {"error":"failed to get info about account B4PHSkL6CeZbxVqm1aUPQuVpc5ERFcaZj7u9dhtphmVX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"455f468d-02fc-4ccd-82a9-500d89d2b669\" } \r\n","poolAddress":"B4PHSkL6CeZbxVqm1aUPQuVpc5ERFcaZj7u9dhtphmVX"}
2025-08-13 13:46:08 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7tkEbPtfcFuUgrekDCaZrz7ZiegAXfvDEamWoaK9jC9a: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f177bbc8-a3b1-480d-adf3-737c2836fed2\" } \r\n","poolAddress":"7tkEbPtfcFuUgrekDCaZrz7ZiegAXfvDEamWoaK9jC9a"}
2025-08-13 13:46:08 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8W9yYBBkyW2yYB6mGpT4TdTF4kroRuRsLnFfUyU5JTt3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"2e6b5b92-6cff-4086-acf4-c1123ce7e448\" } \r\n","poolAddress":"8W9yYBBkyW2yYB6mGpT4TdTF4kroRuRsLnFfUyU5JTt3"}
2025-08-13 13:46:08 [ERROR]: Parse pool data error: {"error":"failed to get info about account EA1raWKkmk44cKQsSZwimTxUJPL4fshAsqhRHUcERpEM: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"8890087f-2d8b-4db2-ac9c-bde6c1616df5\" } \r\n","poolAddress":"EA1raWKkmk44cKQsSZwimTxUJPL4fshAsqhRHUcERpEM"}
2025-08-13 13:46:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account AwejPg8uTXPdrWgQMD6yrWTCVyLhaUEAzyJSDbdCkGEE: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fa0f7b78-e7cb-4590-86d7-cfd48be3a902\" } \r\n","poolAddress":"AwejPg8uTXPdrWgQMD6yrWTCVyLhaUEAzyJSDbdCkGEE"}
2025-08-13 13:46:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account CGh7VdBdzDVYScEWM1vvhpAXwGDs9QuRhViVox6dsky5: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"291d06db-d5ff-4b43-bcc4-d29f68cd4930\" } \r\n","poolAddress":"CGh7VdBdzDVYScEWM1vvhpAXwGDs9QuRhViVox6dsky5"}
2025-08-13 13:46:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2QtkjW4vLYFx7ffTDaXd7ZvZ8Dx8ewhAxs8jfQ4y9wt5: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"28b8b7ff-5e61-4609-b5aa-92487b76ccc8\" } \r\n","poolAddress":"2QtkjW4vLYFx7ffTDaXd7ZvZ8Dx8ewhAxs8jfQ4y9wt5"}
2025-08-13 13:46:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account C4ZuB3dbCEu7jQoSU3ikC5DKKqbXdttUVEBNcjCEsL7g: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ce78b543-61d4-41e5-98dd-16f1ce2e29fd\" } \r\n","poolAddress":"C4ZuB3dbCEu7jQoSU3ikC5DKKqbXdttUVEBNcjCEsL7g"}
2025-08-13 13:46:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account B6R72jxHaBU1kaJ9chKRyV82pWnUFjZiJZpMZ61hC17M: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2ceea85a-e25a-4d64-89a8-0c7098e43dba\" } \r\n","poolAddress":"B6R72jxHaBU1kaJ9chKRyV82pWnUFjZiJZpMZ61hC17M"}
2025-08-13 13:46:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3aqXTJiMPFevQKhkdcCGfqDcYJ1yA4CbkswpZFzZq7XN: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fba5bc8b-e3d9-40a0-b8ba-83e958b36db3\" } \r\n","poolAddress":"3aqXTJiMPFevQKhkdcCGfqDcYJ1yA4CbkswpZFzZq7XN"}
2025-08-13 13:46:11 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5WGYajM1xtLy3QrLHGSX4YPwsso3jrjEsbU1VivUErzk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3e857d1d-52e5-4d80-ad30-7d8090a79071\" } \r\n","poolAddress":"5WGYajM1xtLy3QrLHGSX4YPwsso3jrjEsbU1VivUErzk"}
2025-08-13 13:46:11 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:11 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:12 [ERROR]: Parse pool data error: {"error":"failed to get info about account FUVbZZ2VH9eZuCHS8ygT4wcrCuHe3WFmQv3tBWi7dd3k: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d0062e21-b60d-47ce-8eaf-5f35941d8c66\" } \r\n","poolAddress":"FUVbZZ2VH9eZuCHS8ygT4wcrCuHe3WFmQv3tBWi7dd3k"}
2025-08-13 13:46:12 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:12 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:13 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:13 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:13 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:14 [ERROR]: Parse pool data error: {"error":"failed to get info about account CWQVga1qUbpZXjrWQRj6U6tmL3HhrFiAT11VYnB8d3CF: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b36d1c00-e063-4187-a1e6-669af18c1de2\" } \r\n","poolAddress":"CWQVga1qUbpZXjrWQRj6U6tmL3HhrFiAT11VYnB8d3CF"}
2025-08-13 13:46:14 [ERROR]: Parse pool data error: {"error":"failed to get info about account ********************************************: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"147fbc22-5f43-483d-bea9-1ff536d4898b\" } \r\n","poolAddress":"********************************************"}
2025-08-13 13:46:14 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5WGYajM1xtLy3QrLHGSX4YPwsso3jrjEsbU1VivUErzk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5b9b2c05-cd56-450f-b8d6-d37ddedef2b6\" } \r\n","poolAddress":"5WGYajM1xtLy3QrLHGSX4YPwsso3jrjEsbU1VivUErzk"}
2025-08-13 13:46:14 [ERROR]: Parse pool data error: {"error":"failed to get info about account DEFjgZfxS59iJQ42gNvVA3Gi7A1MGHNhcW4Ht5eRgXh2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f9a37c32-6106-40a2-8b0d-0d8e911b1716\" } \r\n","poolAddress":"DEFjgZfxS59iJQ42gNvVA3Gi7A1MGHNhcW4Ht5eRgXh2"}
2025-08-13 13:46:14 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2XhcY9rDMWFBtHYNbXYhsEhi3pNqtd9CUSxeDKzAe1x9: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1ffab8f9-5404-432d-b51c-fabe75de071d\" } \r\n","poolAddress":"2XhcY9rDMWFBtHYNbXYhsEhi3pNqtd9CUSxeDKzAe1x9"}
2025-08-13 13:46:14 [ERROR]: Parse pool data error: {"error":"failed to get info about account AQcBbrwGmgzwimvfwNdBTGTgn8mq2u74NerGta5mGB2o: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b4c05753-a40e-4332-8550-e28e2c47c662\" } \r\n","poolAddress":"AQcBbrwGmgzwimvfwNdBTGTgn8mq2u74NerGta5mGB2o"}
2025-08-13 13:46:16 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:19 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account Cne2WysEXzSLWbdABTG3vYkRNyJyMJ1zLhn26QPrBRZg: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3d5fd4e5-1c09-40d6-9c2c-52b90a8802de\" } \r\n","poolAddress":"Cne2WysEXzSLWbdABTG3vYkRNyJyMJ1zLhn26QPrBRZg"}
2025-08-13 13:46:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5mypUqrnKj7n4A3JUEoJfsa68NBGg5sLXFCczPagRP21: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0be24c51-652e-48db-9f95-b2b15104a6cd\" } \r\n","poolAddress":"5mypUqrnKj7n4A3JUEoJfsa68NBGg5sLXFCczPagRP21"}
2025-08-13 13:46:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5WGYajM1xtLy3QrLHGSX4YPwsso3jrjEsbU1VivUErzk: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ec6469d7-1d93-4aa1-afc7-b10c4a4feadb\" } \r\n","poolAddress":"5WGYajM1xtLy3QrLHGSX4YPwsso3jrjEsbU1VivUErzk"}
2025-08-13 13:46:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5mypUqrnKj7n4A3JUEoJfsa68NBGg5sLXFCczPagRP21: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7b1dee3a-955e-4187-86b7-e2c210bffdea\" } \r\n","poolAddress":"5mypUqrnKj7n4A3JUEoJfsa68NBGg5sLXFCczPagRP21"}
2025-08-13 13:46:19 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:19 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:19 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:19 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5KZumkRJXzDA4h2woJBZBKQZJKUjW9HA95AkY6mYGtSt: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5c41f17f-0aaf-4e66-ab6c-452edbb60012\" } \r\n","poolAddress":"5KZumkRJXzDA4h2woJBZBKQZJKUjW9HA95AkY6mYGtSt"}
2025-08-13 13:46:20 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2QtkjW4vLYFx7ffTDaXd7ZvZ8Dx8ewhAxs8jfQ4y9wt5: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bdcb6c61-876f-4922-a0ae-8c9509607e45\" } \r\n","poolAddress":"2QtkjW4vLYFx7ffTDaXd7ZvZ8Dx8ewhAxs8jfQ4y9wt5"}
2025-08-13 13:46:20 [ERROR]: Parse pool data error: {"error":"failed to get info about account B9k4vGKPBwtYwcsaxuQfhyifKXPWzZr8c2sffmGab1bR: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5e43317a-ba23-49f1-9a5e-759aaa758eba\" } \r\n","poolAddress":"B9k4vGKPBwtYwcsaxuQfhyifKXPWzZr8c2sffmGab1bR"}
2025-08-13 13:46:21 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7fEuRxSq6rYC1MWh24zodRRVJbgHzok3e2BwuxktbonC: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5fca4e21-b2e0-4f60-9de7-7468fcc87ff6\" } \r\n","poolAddress":"7fEuRxSq6rYC1MWh24zodRRVJbgHzok3e2BwuxktbonC"}
2025-08-13 13:46:22 [ERROR]: Parse pool data error: {"error":"failed to get info about account EYErUp5muPYEEkeaUCY22JibeZX7E9UuMcJFZkmNAN7c: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"929fe3d6-145c-4c4e-a732-34edc0161474\" } \r\n","poolAddress":"EYErUp5muPYEEkeaUCY22JibeZX7E9UuMcJFZkmNAN7c"}
2025-08-13 13:46:23 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5rhWcZ1a24yBDRzUn4XS1rJDArCrxZT1YTuirqJT99Jj: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c320f8b0-fc1e-4ae3-a1b4-e8adcb42a2b1\" } \r\n","poolAddress":"5rhWcZ1a24yBDRzUn4XS1rJDArCrxZT1YTuirqJT99Jj"}
2025-08-13 13:46:23 [ERROR]: Parse pool data error: {"error":"failed to get info about account EVy89kSpyFaXgJUkX5Cya84EMCBTLKGqSdAbMUuqTUVC: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-1803-4526-bbb8-4c08b3dd9c47\" } \r\n","poolAddress":"EVy89kSpyFaXgJUkX5Cya84EMCBTLKGqSdAbMUuqTUVC"}
2025-08-13 13:46:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8TyM4TdgXB7bpknWpUQxBGjT6JkUEnVUVGPNuSxLwZLZ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8d2bd67e-e1b3-454f-a768-97639aee20a5\" } \r\n","poolAddress":"8TyM4TdgXB7bpknWpUQxBGjT6JkUEnVUVGPNuSxLwZLZ"}
2025-08-13 13:46:24 [ERROR]: Parse pool data error: {"error":"failed to get info about account CWQVga1qUbpZXjrWQRj6U6tmL3HhrFiAT11VYnB8d3CF: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0a47dc39-663d-4a41-b628-3db6d525f246\" } \r\n","poolAddress":"CWQVga1qUbpZXjrWQRj6U6tmL3HhrFiAT11VYnB8d3CF"}
2025-08-13 13:46:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account DiUdNPBq7yU4AEN5z1foibMdmkjSTuXz3aPZg3SAiUDB: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"83e97172-d213-4789-a8f0-61d3061cfc11\" } \r\n","poolAddress":"DiUdNPBq7yU4AEN5z1foibMdmkjSTuXz3aPZg3SAiUDB"}
2025-08-13 13:46:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2pofQcvn4wysAzpSqPPxQ4cSPBtKQLSEHF9q5x77Avm7: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7a1daa41-f76c-4f26-86c6-7692dbf8c6f8\" } \r\n","poolAddress":"2pofQcvn4wysAzpSqPPxQ4cSPBtKQLSEHF9q5x77Avm7"}
2025-08-13 13:46:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8nWPBjtQU9JrCmmFTWTe6Ttn6p4fFZGRiUtMQ5dgd4ze: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9eaf046e-731d-4d1b-b63d-70c22e0e9288\" } \r\n","poolAddress":"8nWPBjtQU9JrCmmFTWTe6Ttn6p4fFZGRiUtMQ5dgd4ze"}
2025-08-13 13:46:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account 424kbbJyt6VkSn7GeKT9Vh5yetuTR1sbeyoya2nmBJpw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8c15184d-9653-436a-b4e6-8d4da965182f\" } \r\n","poolAddress":"424kbbJyt6VkSn7GeKT9Vh5yetuTR1sbeyoya2nmBJpw"}
2025-08-13 13:46:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account 79J8N2ZusXXim1PuVKyoTYZKBD2B6HphfGoNiJWgctnA: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"784d569f-c9c6-4d4b-bec1-3c59938a32e6\" } \r\n","poolAddress":"79J8N2ZusXXim1PuVKyoTYZKBD2B6HphfGoNiJWgctnA"}
2025-08-13 13:46:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4s9TNMbbs1AZcNNc8TRkfbvKspgvVWgujNv8a8tHwfYi: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e0b80b57-ea37-4bed-8319-edbd8ff50e35\" } \r\n","poolAddress":"4s9TNMbbs1AZcNNc8TRkfbvKspgvVWgujNv8a8tHwfYi"}
2025-08-13 13:46:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6CqbccfcQtZ6MMhyDERpAcorU5qJbykLvBBTLfQU2XdY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2cc5d6ad-8a4a-4610-9cc0-5d85439a02d1\" } \r\n","poolAddress":"6CqbccfcQtZ6MMhyDERpAcorU5qJbykLvBBTLfQU2XdY"}
2025-08-13 13:46:25 [ERROR]: Parse pool data error: {"error":"failed to get info about account 424kbbJyt6VkSn7GeKT9Vh5yetuTR1sbeyoya2nmBJpw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"800f99a9-9a38-4c1f-b6c5-6ac2f2e39b31\" } \r\n","poolAddress":"424kbbJyt6VkSn7GeKT9Vh5yetuTR1sbeyoya2nmBJpw"}
2025-08-13 13:46:25 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:25 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account CW9DFoTWEUiwxyxVGnQFYhbrYEfGkvaqXEgxKZG7d7X1: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7f654cca-6238-42a4-8be5-595f0a5583b9\" } \r\n","poolAddress":"CW9DFoTWEUiwxyxVGnQFYhbrYEfGkvaqXEgxKZG7d7X1"}
2025-08-13 13:46:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account ********************************************: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"568c0c0d-4912-4f56-a7a4-9390b5794df6\" } \r\n","poolAddress":"********************************************"}
2025-08-13 13:46:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account 93o4y72oH55JbKvuU3QSibznocGYKdiexEE3zJ1WAhAe: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-6edc-43d1-8b25-68c8a957b0b0\" } \r\n","poolAddress":"93o4y72oH55JbKvuU3QSibznocGYKdiexEE3zJ1WAhAe"}
2025-08-13 13:46:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account BnkDVQKwr7DWx9zmPcqtgCpTfKm7wzp2ydzrE218nrtV: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f03f2b50-2d7a-413d-82a5-686fd9941695\" } \r\n","poolAddress":"BnkDVQKwr7DWx9zmPcqtgCpTfKm7wzp2ydzrE218nrtV"}
2025-08-13 13:46:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account B9c1h8YJTH7qx1fzsRkXDRJAtjHBEyppj4aWkQEAeUfz: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5571c8f2-7e51-4ea7-bd23-b3f327fa5081\" } \r\n","poolAddress":"B9c1h8YJTH7qx1fzsRkXDRJAtjHBEyppj4aWkQEAeUfz"}
2025-08-13 13:46:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account HQ1GmezAAnbPk5c6g5Xgz4nMZ4ALTphTT1tz5iJrkdXV: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7535738c-41a3-4163-a2a3-71196ef29616\" } \r\n","poolAddress":"HQ1GmezAAnbPk5c6g5Xgz4nMZ4ALTphTT1tz5iJrkdXV"}
2025-08-13 13:46:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2ZSXjTj5FzMWk53yncX5pHBxacukwutxCL6BDj36T846: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"1b5be48f-f0ea-40c0-a91b-48b8cb43f52c\" } \r\n","poolAddress":"2ZSXjTj5FzMWk53yncX5pHBxacukwutxCL6BDj36T846"}
2025-08-13 13:46:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account BVN4g7bApqbzS4SP34EcHi7h6zqn1V5tsJZaN1wFiMgM: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"89f11b91-30a6-4cb9-8a2e-1b89dd99404a\" } \r\n","poolAddress":"BVN4g7bApqbzS4SP34EcHi7h6zqn1V5tsJZaN1wFiMgM"}
2025-08-13 13:46:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6btaEBWeGTucPsuXrSBmbdmqdR6S7MaBV4RkzbipHEYu: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8566a2e8-7c56-4246-ae2c-c49bfe63986c\" } \r\n","poolAddress":"6btaEBWeGTucPsuXrSBmbdmqdR6S7MaBV4RkzbipHEYu"}
2025-08-13 13:46:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account G6XZu9m4yAmoW1uGFKcAHUW5ufpURBk4kCeWr9dP1KxJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"152c0455-6c29-4a37-8371-6574fb3ebdc0\" } \r\n","poolAddress":"G6XZu9m4yAmoW1uGFKcAHUW5ufpURBk4kCeWr9dP1KxJ"}
2025-08-13 13:46:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account 424kbbJyt6VkSn7GeKT9Vh5yetuTR1sbeyoya2nmBJpw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"5263a219-ff85-4905-aee1-0ebe0a942263\" } \r\n","poolAddress":"424kbbJyt6VkSn7GeKT9Vh5yetuTR1sbeyoya2nmBJpw"}
2025-08-13 13:46:26 [ERROR]: Parse pool data error: {"error":"failed to get info about account B9c1h8YJTH7qx1fzsRkXDRJAtjHBEyppj4aWkQEAeUfz: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4467000b-5bb9-441e-99b4-3b31cdef75ca\" } \r\n","poolAddress":"B9c1h8YJTH7qx1fzsRkXDRJAtjHBEyppj4aWkQEAeUfz"}
2025-08-13 13:46:26 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:26 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:27 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5kkGeDtRqXV69xwC3xwXvdbxgD9VPi1JYCom4gtaH4kn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2975dde9-fe73-4f86-8f82-d3f03330ac8d\" } \r\n","poolAddress":"5kkGeDtRqXV69xwC3xwXvdbxgD9VPi1JYCom4gtaH4kn"}
2025-08-13 13:46:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4soxi47UtPBzUuSw27i9Vq7oGKwRZ8XLrj2fRUUNnSJg: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"863d5a07-42bc-4178-b287-ae7a3f7efdb3\" } \r\n","poolAddress":"4soxi47UtPBzUuSw27i9Vq7oGKwRZ8XLrj2fRUUNnSJg"}
2025-08-13 13:46:30 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:31 [ERROR]: Parse pool data error: {"error":"failed to get info about account ********************************************: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bc5af8f8-177f-4da4-b14f-a196c33287d7\" } \r\n","poolAddress":"********************************************"}
2025-08-13 13:46:31 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5kkGeDtRqXV69xwC3xwXvdbxgD9VPi1JYCom4gtaH4kn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"05c559fe-a76d-455e-8907-624c9f726466\" } \r\n","poolAddress":"5kkGeDtRqXV69xwC3xwXvdbxgD9VPi1JYCom4gtaH4kn"}
2025-08-13 13:46:32 [ERROR]: Parse pool data error: {"error":"failed to get info about account G6XZu9m4yAmoW1uGFKcAHUW5ufpURBk4kCeWr9dP1KxJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"4b0b3be2-0d21-4bb6-9de1-71d630ff8d55\" } \r\n","poolAddress":"G6XZu9m4yAmoW1uGFKcAHUW5ufpURBk4kCeWr9dP1KxJ"}
2025-08-13 13:46:32 [ERROR]: Parse pool data error: {"error":"failed to get info about account DiUdNPBq7yU4AEN5z1foibMdmkjSTuXz3aPZg3SAiUDB: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0e643b30-0573-4e33-bd97-3f1bcc594e5b\" } \r\n","poolAddress":"DiUdNPBq7yU4AEN5z1foibMdmkjSTuXz3aPZg3SAiUDB"}
2025-08-13 13:46:32 [ERROR]: Parse pool data error: {"error":"failed to get info about account CW9DFoTWEUiwxyxVGnQFYhbrYEfGkvaqXEgxKZG7d7X1: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0ae4a135-8e9a-49d9-89e2-158a32245648\" } \r\n","poolAddress":"CW9DFoTWEUiwxyxVGnQFYhbrYEfGkvaqXEgxKZG7d7X1"}
2025-08-13 13:46:32 [ERROR]: Parse pool data error: {"error":"failed to get info about account G6XZu9m4yAmoW1uGFKcAHUW5ufpURBk4kCeWr9dP1KxJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8f3901cf-28e4-414a-8457-8f87745ac63a\" } \r\n","poolAddress":"G6XZu9m4yAmoW1uGFKcAHUW5ufpURBk4kCeWr9dP1KxJ"}
2025-08-13 13:46:32 [ERROR]: Parse pool data error: {"error":"failed to get info about account EvitUp6oGAqrmyUtsXsUom8kEWLZD1pPRJ2rG7AYyghW: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e57b5b78-f0a4-460c-8b13-69d5dd7ce832\" } \r\n","poolAddress":"EvitUp6oGAqrmyUtsXsUom8kEWLZD1pPRJ2rG7AYyghW"}
2025-08-13 13:46:32 [ERROR]: Parse pool data error: {"error":"failed to get info about account EYErUp5muPYEEkeaUCY22JibeZX7E9UuMcJFZkmNAN7c: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2e568d56-3fe2-4c2e-ad56-25905ebd3007\" } \r\n","poolAddress":"EYErUp5muPYEEkeaUCY22JibeZX7E9UuMcJFZkmNAN7c"}
2025-08-13 13:46:33 [ERROR]: Parse pool data error: {"error":"failed to get info about account EYErUp5muPYEEkeaUCY22JibeZX7E9UuMcJFZkmNAN7c: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"98b7fe50-82ad-43aa-a52e-5ae5ca28fdde\" } \r\n","poolAddress":"EYErUp5muPYEEkeaUCY22JibeZX7E9UuMcJFZkmNAN7c"}
2025-08-13 13:46:33 [ERROR]: Parse pool data error: {"error":"failed to get info about account 59VtDHQrcDKswbaStjTsjNiqLngjX72UHFqChnpAb93p: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c4944d44-4ce2-4c13-aa07-5cea08f8f11c\" } \r\n","poolAddress":"59VtDHQrcDKswbaStjTsjNiqLngjX72UHFqChnpAb93p"}
2025-08-13 13:46:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2pofQcvn4wysAzpSqPPxQ4cSPBtKQLSEHF9q5x77Avm7: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"34f51c1c-2b94-4a91-9c43-24a66fd2a224\" } \r\n","poolAddress":"2pofQcvn4wysAzpSqPPxQ4cSPBtKQLSEHF9q5x77Avm7"}
2025-08-13 13:46:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account EYErUp5muPYEEkeaUCY22JibeZX7E9UuMcJFZkmNAN7c: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f2616d21-a016-4ef5-83da-97992698d40f\" } \r\n","poolAddress":"EYErUp5muPYEEkeaUCY22JibeZX7E9UuMcJFZkmNAN7c"}
2025-08-13 13:46:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account BnkDVQKwr7DWx9zmPcqtgCpTfKm7wzp2ydzrE218nrtV: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"09c3995c-b4a2-4ee9-9556-9b88c21021db\" } \r\n","poolAddress":"BnkDVQKwr7DWx9zmPcqtgCpTfKm7wzp2ydzrE218nrtV"}
2025-08-13 13:46:35 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:35 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2BGZSt9cgNrEJiPwyfY1AVXoxRBTuR7J5AYihbgRA7CP: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c125f373-8629-4c69-83d6-2e713c48d0e8\" } \r\n","poolAddress":"2BGZSt9cgNrEJiPwyfY1AVXoxRBTuR7J5AYihbgRA7CP"}
2025-08-13 13:46:35 [ERROR]: Parse pool data error: {"error":"failed to get info about account vo77toQS6oomYK4deaiFefKZEUnREXkEeGby3QRy6Bt: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-db99-4586-bddf-0dc55c01909a\" } \r\n","poolAddress":"vo77toQS6oomYK4deaiFefKZEUnREXkEeGby3QRy6Bt"}
2025-08-13 13:46:35 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8SQNDszijLRLnXmfQBPzxbBhSUjRJur7CEfqcQZQz1hQ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"040c0042-a1b5-4f5a-8180-79e43d6fd6d4\" } \r\n","poolAddress":"8SQNDszijLRLnXmfQBPzxbBhSUjRJur7CEfqcQZQz1hQ"}
2025-08-13 13:46:36 [ERROR]: Parse pool data error: {"error":"failed to get info about account GH96covWLWpgcDHYs5aj824EX1c3Cfk7kreRsG6WpVEF: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7a721784-2f79-4de6-9565-************\" } \r\n","poolAddress":"GH96covWLWpgcDHYs5aj824EX1c3Cfk7kreRsG6WpVEF"}
2025-08-13 13:46:36 [ERROR]: Parse pool data error: {"error":"failed to get info about account A8iNixG97AjK5kCXTdi5ooDg2DrKeEBpnW2Ghchpe4j1: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"21b1d7b6-a496-4f9f-a80c-3203ed9cfc74\" } \r\n","poolAddress":"A8iNixG97AjK5kCXTdi5ooDg2DrKeEBpnW2Ghchpe4j1"}
2025-08-13 13:46:36 [ERROR]: Parse pool data error: {"error":"failed to get info about account AtWMAA6T9t8cq8XCccCFPGDNNQYXhScuNuY6WVRi7FKe: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7baae0b2-4cd4-4f3c-8d97-e486cd9d86e3\" } \r\n","poolAddress":"AtWMAA6T9t8cq8XCccCFPGDNNQYXhScuNuY6WVRi7FKe"}
2025-08-13 13:46:36 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9Y35GD9E4oirRAQpg8J3Abj7di4bXrAhW9W6kfGZ4JJ5: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6941875e-b477-4a01-8475-74e449c0392c\" } \r\n","poolAddress":"9Y35GD9E4oirRAQpg8J3Abj7di4bXrAhW9W6kfGZ4JJ5"}
2025-08-13 13:46:37 [ERROR]: Parse pool data error: {"error":"failed to get info about account EBi2v5DMsZ5xGrjA6147KZseT9FEAJxrf5UE5PX3koUD: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"229e0f09-0c67-4abf-b2f2-fe2ecaf664f1\" } \r\n","poolAddress":"EBi2v5DMsZ5xGrjA6147KZseT9FEAJxrf5UE5PX3koUD"}
2025-08-13 13:46:37 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6M43tquLN4Y1BBocSoZhwYaWTRoPEp3qjF6Mn2QHhPDv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"106c1c52-971a-4a1c-955b-d9e389af756f\" } \r\n","poolAddress":"6M43tquLN4Y1BBocSoZhwYaWTRoPEp3qjF6Mn2QHhPDv"}
2025-08-13 13:46:37 [ERROR]: Parse pool data error: {"error":"failed to get info about account F6HgcRZAsWGegc9mk6oUeG47G43PnL3MdjRfVAvZMiGN: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"eb4f38b3-d378-4191-862a-c223a09447cb\" } \r\n","poolAddress":"F6HgcRZAsWGegc9mk6oUeG47G43PnL3MdjRfVAvZMiGN"}
2025-08-13 13:46:38 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4BKRQ2iL3Rv8mSpDsFM5FNkZ9SGq4iaqrYtgNWjGE3s4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"09e6e4c1-9199-4596-8d9f-18048228f61a\" } \r\n","poolAddress":"4BKRQ2iL3Rv8mSpDsFM5FNkZ9SGq4iaqrYtgNWjGE3s4"}
2025-08-13 13:46:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account B9c1h8YJTH7qx1fzsRkXDRJAtjHBEyppj4aWkQEAeUfz: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a37a768c-a332-4fdb-b871-6d6d123c18b2\" } \r\n","poolAddress":"B9c1h8YJTH7qx1fzsRkXDRJAtjHBEyppj4aWkQEAeUfz"}
2025-08-13 13:46:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5rsp7vXTeAF6Ry6cvj7YZwYtH9pQ1GSgsLrZvCMsvitN: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b00957c4-b85a-46fd-87af-f029e31f2a95\" } \r\n","poolAddress":"5rsp7vXTeAF6Ry6cvj7YZwYtH9pQ1GSgsLrZvCMsvitN"}
2025-08-13 13:46:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account GTkRQ1PLoP8T9XPMZw4BteQJ9cakktd8DQC8WZkfF8cP: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"49f763b0-78a2-4f8c-a437-56288559538c\" } \r\n","poolAddress":"GTkRQ1PLoP8T9XPMZw4BteQJ9cakktd8DQC8WZkfF8cP"}
2025-08-13 13:46:41 [ERROR]: Parse pool data error: {"error":"failed to get info about account B5ZguAWAGC3GXVtJZVfoMtzvEvDnDKBPCevsUKMy4DTZ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0196970f-06ff-481f-8e2c-e5eb29137229\" } \r\n","poolAddress":"B5ZguAWAGC3GXVtJZVfoMtzvEvDnDKBPCevsUKMy4DTZ"}
2025-08-13 13:46:41 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6DPQjMYHi5kHprCyyAguuvnutMeeNtXeEiddW8mD1o4W: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f974fc52-d229-4753-84f8-82b302f1af41\" } \r\n","poolAddress":"6DPQjMYHi5kHprCyyAguuvnutMeeNtXeEiddW8mD1o4W"}
2025-08-13 13:46:41 [ERROR]: Parse pool data error: {"error":"failed to get info about account B5ZguAWAGC3GXVtJZVfoMtzvEvDnDKBPCevsUKMy4DTZ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9f19f92b-7937-4640-93af-d43cd427cd1f\" } \r\n","poolAddress":"B5ZguAWAGC3GXVtJZVfoMtzvEvDnDKBPCevsUKMy4DTZ"}
2025-08-13 13:46:41 [ERROR]: Parse pool data error: {"error":"failed to get info about account BHdhmprkGkBqRjo4cWLGanBZTfdtPRk8dXFaLsqZdXij: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ad58d4f4-cace-488b-85fe-ca83bedf312d\" } \r\n","poolAddress":"BHdhmprkGkBqRjo4cWLGanBZTfdtPRk8dXFaLsqZdXij"}
2025-08-13 13:46:41 [ERROR]: Parse pool data error: {"error":"failed to get info about account yHzjrLAsZt5xAbEWQoHFMSG1BAUAKPoquY1nRsPGpSf: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fc6fe822-2065-48ea-a156-96a1f28910d0\" } \r\n","poolAddress":"yHzjrLAsZt5xAbEWQoHFMSG1BAUAKPoquY1nRsPGpSf"}
2025-08-13 13:46:41 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:42 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:42 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7SkV9hMXcwvNFfBWUjn2oYd1VqdnASnw5jYxqGGq4i71: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"153e6b88-d292-48cd-86b9-e2230e2d4bea\" } \r\n","poolAddress":"7SkV9hMXcwvNFfBWUjn2oYd1VqdnASnw5jYxqGGq4i71"}
2025-08-13 13:46:46 [ERROR]: Parse pool data error: {"error":"failed to get info about account U71hPHrnG3mNJzTHyK1rmuNzySs97pnoGbwX3t5xeyx: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6740a7d9-07a5-4561-9686-fc3fc888eed6\" } \r\n","poolAddress":"U71hPHrnG3mNJzTHyK1rmuNzySs97pnoGbwX3t5xeyx"}
2025-08-13 13:46:46 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account HcBRBsb7CNAice5Tq7RbrHY1GYe59AJzkUd5RycVvrQ3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"********-c410-4a30-8c68-891b5b57c911\" } \r\n","poolAddress":"HcBRBsb7CNAice5Tq7RbrHY1GYe59AJzkUd5RycVvrQ3"}
2025-08-13 13:46:47 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6DgFEDUcbhKGA9o38tVqomATEsS9HctXxUghF3QmRLDm: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c25f2be5-c8a0-427c-b24d-5ba9f150ead7\" } \r\n","poolAddress":"6DgFEDUcbhKGA9o38tVqomATEsS9HctXxUghF3QmRLDm"}
2025-08-13 13:46:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account BDuGgVL2yLc41BHXMZevH3zJjZ69sVCX6Lhwfy4B71Mo: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e828a2f0-49e2-4874-a554-f962fc1e0c44\" } \r\n","poolAddress":"BDuGgVL2yLc41BHXMZevH3zJjZ69sVCX6Lhwfy4B71Mo"}
2025-08-13 13:46:47 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9zfuhgi7VXWAbCKaXzaAnPPBeyp56EH717SRrL3u2a1s: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ef9553a2-be93-4f05-97a8-9b3f778799c9\" } \r\n","poolAddress":"9zfuhgi7VXWAbCKaXzaAnPPBeyp56EH717SRrL3u2a1s"}
2025-08-13 13:46:48 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4D1LDiENf5RkTJivGBvFbbUW4KPCNpyPRPnNJk1aT8z2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9eedb0e6-1b4c-4ffa-9f68-554369bf2563\" } \r\n","poolAddress":"4D1LDiENf5RkTJivGBvFbbUW4KPCNpyPRPnNJk1aT8z2"}
2025-08-13 13:46:48 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:49 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:49 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7KqUHWB67F1qpqRn5sHLtpdZaXkb8pWdKcqWb6zwKoQY: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a9c5ef32-a9a1-4e8f-aeed-fece225c3812\" } \r\n","poolAddress":"7KqUHWB67F1qpqRn5sHLtpdZaXkb8pWdKcqWb6zwKoQY"}
2025-08-13 13:46:50 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account F9XE9W1YYtd31kgKQbke6K7SKQzd9HJEFUCfqF4poSJC: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"026aad28-1417-4553-aa7e-58926103176d\" } \r\n","poolAddress":"F9XE9W1YYtd31kgKQbke6K7SKQzd9HJEFUCfqF4poSJC"}
2025-08-13 13:46:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account CQQDXt2M6Cx1J8N3cYsSmPiD7fcLdU5RpVtRbs9WaCXZ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6365e50d-e0fc-4c24-8afc-e1f707497b8e\" } \r\n","poolAddress":"CQQDXt2M6Cx1J8N3cYsSmPiD7fcLdU5RpVtRbs9WaCXZ"}
2025-08-13 13:46:50 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8kWG6UGuqCkFmNszPKaatZTZqYzKF1KvHRFQCz1bwYYJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"675782d9-ca32-4f7a-9171-6bd9fb4b9762\" } \r\n","poolAddress":"8kWG6UGuqCkFmNszPKaatZTZqYzKF1KvHRFQCz1bwYYJ"}
2025-08-13 13:46:51 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:52 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5JbknNv534djG9TeCUMpLevedino2c1WrYB9pqGkyqLe: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bc5a14dd-9a59-4f68-b7c7-3afa2e7ce226\" } \r\n","poolAddress":"5JbknNv534djG9TeCUMpLevedino2c1WrYB9pqGkyqLe"}
2025-08-13 13:46:52 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account BRhBNpVvrrTmRXtDUzhdPBRJxU5MDrmjfNFm3wAJW1QD: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"db2573bd-4957-4b5f-9b3c-a4d20edfc245\" } \r\n","poolAddress":"BRhBNpVvrrTmRXtDUzhdPBRJxU5MDrmjfNFm3wAJW1QD"}
2025-08-13 13:46:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account AtWMAA6T9t8cq8XCccCFPGDNNQYXhScuNuY6WVRi7FKe: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"39f7fabf-c9d3-4c5f-aa3c-e87315137584\" } \r\n","poolAddress":"AtWMAA6T9t8cq8XCccCFPGDNNQYXhScuNuY6WVRi7FKe"}
2025-08-13 13:46:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account 8kWG6UGuqCkFmNszPKaatZTZqYzKF1KvHRFQCz1bwYYJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bbc6ac03-e26a-4f1d-beb7-bb21df50d395\" } \r\n","poolAddress":"8kWG6UGuqCkFmNszPKaatZTZqYzKF1KvHRFQCz1bwYYJ"}
2025-08-13 13:46:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account D1kxaKCjTLogUXjM3mye4wbEQgwuvJdhiDmM3JDUxdNn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"28ec8acf-d69f-4e81-8fe3-33fa7400e53c\" } \r\n","poolAddress":"D1kxaKCjTLogUXjM3mye4wbEQgwuvJdhiDmM3JDUxdNn"}
2025-08-13 13:46:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account HTpfjCDF85RuupwQ5vjEivs8GCWtNvZRWjfUAjde3tjv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3610e4a6-08d1-4db2-bc02-a2b69783c027\" } \r\n","poolAddress":"HTpfjCDF85RuupwQ5vjEivs8GCWtNvZRWjfUAjde3tjv"}
2025-08-13 13:46:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account ErMacDgjD1Nk6hWk35P921sWoHkx4JKLkVp7qxReZSW1: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6d11534e-181e-49c9-8116-e4324a8a7af0\" } \r\n","poolAddress":"ErMacDgjD1Nk6hWk35P921sWoHkx4JKLkVp7qxReZSW1"}
2025-08-13 13:46:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3QwxFXhietfr6Nt8ZfLUrpXfre5moacfiSqzHCFAGshE: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f1426e15-a968-4906-a313-0f3b60d518e4\" } \r\n","poolAddress":"3QwxFXhietfr6Nt8ZfLUrpXfre5moacfiSqzHCFAGshE"}
2025-08-13 13:46:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account 95AT5r4i85gfqeew2yR6BYFG8RLrY1d9ztPs7qrSKDVc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d4ec65a9-483a-4413-8b04-b8ee0348dbda\" } \r\n","poolAddress":"95AT5r4i85gfqeew2yR6BYFG8RLrY1d9ztPs7qrSKDVc"}
2025-08-13 13:46:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account 55qVFUA6BpjBTbcijw77aC7K6XKoGMkhSdEx9NhUSj1q: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ba283a5a-49cc-4822-aa94-807c938dd198\" } \r\n","poolAddress":"55qVFUA6BpjBTbcijw77aC7K6XKoGMkhSdEx9NhUSj1q"}
2025-08-13 13:46:53 [ERROR]: Parse pool data error: {"error":"failed to get info about account 34tFULRrRwh4bMcBLPtJaNqqe5pVgGZACi5sR8Xz95KC: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d016c1aa-8faa-4d89-97cb-a8eebc5bd665\" } \r\n","poolAddress":"34tFULRrRwh4bMcBLPtJaNqqe5pVgGZACi5sR8Xz95KC"}
2025-08-13 13:46:54 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9ViX1VductEoC2wERTSp2TuDxXPwAf69aeET8ENPJpsN: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"7542eb59-9d35-4072-8232-4f9c83066659\" } \r\n","poolAddress":"9ViX1VductEoC2wERTSp2TuDxXPwAf69aeET8ENPJpsN"}
2025-08-13 13:46:54 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:54 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:54 [ERROR]: Parse pool data error: {"error":"failed to get info about account CQQDXt2M6Cx1J8N3cYsSmPiD7fcLdU5RpVtRbs9WaCXZ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a71c6e6d-fc33-4f43-88ab-b2cf90ccdf3f\" } \r\n","poolAddress":"CQQDXt2M6Cx1J8N3cYsSmPiD7fcLdU5RpVtRbs9WaCXZ"}
2025-08-13 13:46:54 [ERROR]: Parse pool data error: {"error":"failed to get info about account Drvvg3cnk7pUUaMk3gtQ7tLcHNmScdVYRjuBcroznKtd: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"48a65cd7-34c0-4fe4-9b95-5b258589d443\" } \r\n","poolAddress":"Drvvg3cnk7pUUaMk3gtQ7tLcHNmScdVYRjuBcroznKtd"}
2025-08-13 13:46:55 [ERROR]: Parse pool data error: {"error":"failed to get info about account GH8Ers4yzKR3UKDvgVu8cqJfGzU4cU62mTeg9bcJ7ug6: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3498a110-f599-4e49-81ed-084624697ed3\" } \r\n","poolAddress":"GH8Ers4yzKR3UKDvgVu8cqJfGzU4cU62mTeg9bcJ7ug6"}
2025-08-13 13:46:56 [ERROR]: Parse pool data error: {"error":"failed to get info about account E61pEDMEwf8iUHFhmGn3Wcj5P32DPjKDgo1UNjjaNrg1: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2d8e998d-3be0-42d3-a4b9-eb349d511f7d\" } \r\n","poolAddress":"E61pEDMEwf8iUHFhmGn3Wcj5P32DPjKDgo1UNjjaNrg1"}
2025-08-13 13:46:56 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:57 [ERROR]: Parse pool data error: {"error":"failed to get info about account FyqYBBJ8vhr5AtDZiyJue4Khx9Be6Xijx5nm6aL6wZZV: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9196d06f-b341-4918-a913-d8c3b55229e3\" } \r\n","poolAddress":"FyqYBBJ8vhr5AtDZiyJue4Khx9Be6Xijx5nm6aL6wZZV"}
2025-08-13 13:46:57 [ERROR]: Parse pool data error: {"error":"failed to get info about account HFmGMJREKBiMvkJZd2pTA8KxLZYgk4bFyJjixTot3p7a: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"aec61d45-67e2-4915-ae06-ee81160be329\" } \r\n","poolAddress":"HFmGMJREKBiMvkJZd2pTA8KxLZYgk4bFyJjixTot3p7a"}
2025-08-13 13:46:58 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4jsJXjsrwRYNFUQY7esf6xbAv1LvCHaZkR5R5MWkeCWw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a51f8891-8c5b-4702-8cac-47aa187e0389\" } \r\n","poolAddress":"4jsJXjsrwRYNFUQY7esf6xbAv1LvCHaZkR5R5MWkeCWw"}
2025-08-13 13:46:58 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:46:59 [ERROR]: Parse pool data error: {"error":"failed to get info about account UPrqJBTFKabPJqkKJMAjwSqiRBbTwuKDcPkzz19era2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"e60f09ff-8984-43c3-b181-661f22d91cfa\" } \r\n","poolAddress":"UPrqJBTFKabPJqkKJMAjwSqiRBbTwuKDcPkzz19era2"}
2025-08-13 13:46:59 [ERROR]: Parse pool data error: {"error":"failed to get info about account D6ozS87rMDQuXgsxK7swfyQZTmQw5rVfaHTHCnDjCwQg: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"6211f3cf-8d64-45cf-9fcb-085b2fa9f131\" } \r\n","poolAddress":"D6ozS87rMDQuXgsxK7swfyQZTmQw5rVfaHTHCnDjCwQg"}
2025-08-13 13:47:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account UPrqJBTFKabPJqkKJMAjwSqiRBbTwuKDcPkzz19era2: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b0756940-8bc8-4371-98e3-************\" } \r\n","poolAddress":"UPrqJBTFKabPJqkKJMAjwSqiRBbTwuKDcPkzz19era2"}
2025-08-13 13:47:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account HcPgh6B2yHNvT6JsEmkrHYT8pVHu9Xiaoxm4Mmn2ibWw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d22f10ee-b02e-4e08-aa5a-7ab6f297e820\" } \r\n","poolAddress":"HcPgh6B2yHNvT6JsEmkrHYT8pVHu9Xiaoxm4Mmn2ibWw"}
2025-08-13 13:47:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account fGFqfsdxtBkYtwaTqs3bYGMugVpLBMNs48ZNv1Kg69Q: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"100fd4f5-7959-4102-ac50-27d4ab4c935c\" } \r\n","poolAddress":"fGFqfsdxtBkYtwaTqs3bYGMugVpLBMNs48ZNv1Kg69Q"}
2025-08-13 13:47:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account HcPgh6B2yHNvT6JsEmkrHYT8pVHu9Xiaoxm4Mmn2ibWw: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"ee2dbf12-55d3-47cb-9a7a-fe4d431fbb16\" } \r\n","poolAddress":"HcPgh6B2yHNvT6JsEmkrHYT8pVHu9Xiaoxm4Mmn2ibWw"}
2025-08-13 13:47:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account CR8FJB9jqGvtNYnYUuxyyS41WXue2HZRfhNavkaV8CS4: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bb9df02c-ecf4-4ac7-ab1a-1870c9d2db4b\" } \r\n","poolAddress":"CR8FJB9jqGvtNYnYUuxyyS41WXue2HZRfhNavkaV8CS4"}
2025-08-13 13:47:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account DsptZksRHePtDbqtQUYYLsC4uPepS72APwRcijDVyA6J: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"c998e538-1c89-435c-b11f-fd8e4277db07\" } \r\n","poolAddress":"DsptZksRHePtDbqtQUYYLsC4uPepS72APwRcijDVyA6J"}
2025-08-13 13:47:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account EhiKZ9r8VmNZ3Cd73G6jFXrV93vwfa4zoC8s26H7EMbF: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"9be7e59e-cdfd-4d81-8301-4b844a5513da\" } \r\n","poolAddress":"EhiKZ9r8VmNZ3Cd73G6jFXrV93vwfa4zoC8s26H7EMbF"}
2025-08-13 13:47:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account HocTbXPoVzJJr46UY96Qd8RQEkgPga5SUU5TVWjxfjq6: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fa160191-b45e-429b-9562-a29d019ae9a1\" } \r\n","poolAddress":"HocTbXPoVzJJr46UY96Qd8RQEkgPga5SUU5TVWjxfjq6"}
2025-08-13 13:47:00 [ERROR]: Parse pool data error: {"error":"failed to get info about account DLaoh9okkk4gdtXj2mkH3WJUE7VbhMBJRuKmciD1PSZX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"2ae45e92-2b3e-4054-89e6-3a73d6e47003\" } \r\n","poolAddress":"DLaoh9okkk4gdtXj2mkH3WJUE7VbhMBJRuKmciD1PSZX"}
2025-08-13 13:47:01 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:47:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5Pce2B55ms8b2uFyptCnVNkPrtzpyKLe5HtMc8UymWxB: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"0a00ea5b-d355-4b10-b361-8d378bd54431\" } \r\n","poolAddress":"5Pce2B55ms8b2uFyptCnVNkPrtzpyKLe5HtMc8UymWxB"}
2025-08-13 13:47:01 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4khS8gMkdyYt3Zm28P8qbWbt42MR5jv6tKENjqfoTrvn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"485c2333-e2d2-432c-9f9e-4dd40f95104b\" } \r\n","poolAddress":"4khS8gMkdyYt3Zm28P8qbWbt42MR5jv6tKENjqfoTrvn"}
2025-08-13 13:47:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account HcBRBsb7CNAice5Tq7RbrHY1GYe59AJzkUd5RycVvrQ3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8ec1e8fb-ad25-4880-b8e8-8d14ee17e3e6\" } \r\n","poolAddress":"HcBRBsb7CNAice5Tq7RbrHY1GYe59AJzkUd5RycVvrQ3"}
2025-08-13 13:47:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account EogZ8pgx9k58w8KUHyhZWu7TU7DU4qujTJvG1ssT7g53: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"f8b4ac77-6f96-4fb6-9409-bed2529d13ec\" } \r\n","poolAddress":"EogZ8pgx9k58w8KUHyhZWu7TU7DU4qujTJvG1ssT7g53"}
2025-08-13 13:47:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6r8hNqoByx2svqQ7PDb9GLdxhiE9JaHeKs5LKMEFAAqM: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"dde051a6-026c-46cd-b8f2-d8df429c6b4a\" } \r\n","poolAddress":"6r8hNqoByx2svqQ7PDb9GLdxhiE9JaHeKs5LKMEFAAqM"}
2025-08-13 13:47:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5geXi63bMNe9WD1nmXsLR3wT6bL4jFryyo6wsZBNGK1z: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"80cb7647-3e77-485b-959b-7b3a39879e7f\" } \r\n","poolAddress":"5geXi63bMNe9WD1nmXsLR3wT6bL4jFryyo6wsZBNGK1z"}
2025-08-13 13:47:02 [ERROR]: Parse pool data error: {"error":"failed to get info about account 3ioPvyDKuz4G48vbizvUqtMYKgxdLRcebgMxFWrbnHfi: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fb52c146-68b6-4cc8-b1e1-43e7eb17a829\" } \r\n","poolAddress":"3ioPvyDKuz4G48vbizvUqtMYKgxdLRcebgMxFWrbnHfi"}
2025-08-13 13:47:02 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:47:03 [ERROR]: Parse pool data error: {"error":"failed to get info about account J8pLEWXkTndVACnM5tgDAKUQDcwURsN2spFwM5dFeBYB: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"de667619-44cb-4828-9858-c28b06822ec1\" } \r\n","poolAddress":"J8pLEWXkTndVACnM5tgDAKUQDcwURsN2spFwM5dFeBYB"}
2025-08-13 13:47:03 [ERROR]: Parse pool data error: {"error":"failed to get info about account ********************************************: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3911cc62-843e-49bf-85be-f6918aa72ccd\" } \r\n","poolAddress":"********************************************"}
2025-08-13 13:47:04 [ERROR]: Parse pool data error: {"error":"failed to get info about account 38jHxPBGEsyPgrgfbZnjanoSrzhgPhYUf5b1ThgzsgoP: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"20b9dc96-01e7-49a9-aaa5-c22ced192b29\" } \r\n","poolAddress":"38jHxPBGEsyPgrgfbZnjanoSrzhgPhYUf5b1ThgzsgoP"}
2025-08-13 13:47:05 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:47:06 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:47:06 [ERROR]: Parse pool data error: {"error":"failed to get info about account Hy1AQMfphVyveUbtN1y9XnoRr1c641iT2qJxDnCVk8Nn: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d364a8c6-a617-4aaa-a28a-8aa606ad194e\" } \r\n","poolAddress":"Hy1AQMfphVyveUbtN1y9XnoRr1c641iT2qJxDnCVk8Nn"}
2025-08-13 13:47:07 [ERROR]: Parse pool data error: {"error":"failed to get info about account H4H6NpquSJG43i5pCmeqWkHr62yKGNKXUW2X6wSgWJJb: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"83c6b12b-6015-4f70-b847-57a4ac64a4c2\" } \r\n","poolAddress":"H4H6NpquSJG43i5pCmeqWkHr62yKGNKXUW2X6wSgWJJb"}
2025-08-13 13:47:07 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9KSa1TnVcdSM1Zckrtw8RSzk2FDt2Eu1iPDjMbXENPpu: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"18b1c2bb-37dc-4180-8d5c-389b681780fb\" } \r\n","poolAddress":"9KSa1TnVcdSM1Zckrtw8RSzk2FDt2Eu1iPDjMbXENPpu"}
2025-08-13 13:47:09 [ERROR]: Parse pool data error: {"error":"failed to get info about account Du57tUPaDWmAnAyNkBSpHj6qRuaMKjMFBicuuLqGCTJE: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Connection rate limits exceeded\"}, \"id\": \"961a280b-d8f8-475b-af42-05c246af0af7\" } \r\n","poolAddress":"Du57tUPaDWmAnAyNkBSpHj6qRuaMKjMFBicuuLqGCTJE"}
2025-08-13 13:47:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account DsptZksRHePtDbqtQUYYLsC4uPepS72APwRcijDVyA6J: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Connection rate limits exceeded\"}, \"id\": \"16f0f3c5-cbaa-4dc2-b331-3262a938c071\" } \r\n","poolAddress":"DsptZksRHePtDbqtQUYYLsC4uPepS72APwRcijDVyA6J"}
2025-08-13 13:47:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account DFWDh1nD5SoGKfSh7ug6gpUJcbeaR85atCKB6fQgffZ3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Connection rate limits exceeded\"}, \"id\": \"8cfda01f-f8a6-4790-999d-ad3b2ec07463\" } \r\n","poolAddress":"DFWDh1nD5SoGKfSh7ug6gpUJcbeaR85atCKB6fQgffZ3"}
2025-08-13 13:47:10 [ERROR]: Parse pool data error: {"error":"failed to get info about account DjQpzLq1yXsZ6SZt5MQP4yWgxRmL7RKMiu5VYoC8Usbv: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Connection rate limits exceeded\"}, \"id\": \"f22f287f-8d5f-4940-aa35-87022ca402b6\" } \r\n","poolAddress":"DjQpzLq1yXsZ6SZt5MQP4yWgxRmL7RKMiu5VYoC8Usbv"}
2025-08-13 13:47:10 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:47:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9uWW4C36HiCTGr6pZW9VFhr9vdXktZ8NA8jVnzQU35pJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"020167e8-9883-4ff9-8c18-0998e233d32d\" } \r\n","poolAddress":"9uWW4C36HiCTGr6pZW9VFhr9vdXktZ8NA8jVnzQU35pJ"}
2025-08-13 13:47:13 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:47:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 5so14wFmDP16hcW7ZVWrNsaHv1ErM2v5RBHCrYpzYNcs: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"234c4298-a7ff-4935-8970-66692a3feb3e\" } \r\n","poolAddress":"5so14wFmDP16hcW7ZVWrNsaHv1ErM2v5RBHCrYpzYNcs"}
2025-08-13 13:47:13 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6QcJ2eioSRrQRmwoDmhRB5farf2hby2GbcLmmkF8gvki: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"380127a0-8edd-4a08-ba84-42afa54dc920\" } \r\n","poolAddress":"6QcJ2eioSRrQRmwoDmhRB5farf2hby2GbcLmmkF8gvki"}
2025-08-13 13:47:17 [ERROR]: Parse pool data error: {"error":"failed to get info about account BZF5zgRbbbsuiW73FC9TMtPDw1d1iCKGkF5kDEoBugeL: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d693fa02-7402-4f62-ae3f-c4fc5ed008c7\" } \r\n","poolAddress":"BZF5zgRbbbsuiW73FC9TMtPDw1d1iCKGkF5kDEoBugeL"}
2025-08-13 13:47:17 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:47:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account HBS7a3br8GMMWuqVa7VB3SMFa7xVi1tSFdoF5w4ZZ3kS: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"b98141e2-1126-4278-8e79-aa00afc52217\" } \r\n","poolAddress":"HBS7a3br8GMMWuqVa7VB3SMFa7xVi1tSFdoF5w4ZZ3kS"}
2025-08-13 13:47:18 [ERROR]: Parse pool data error: {"error":"failed to get info about account HBS7a3br8GMMWuqVa7VB3SMFa7xVi1tSFdoF5w4ZZ3kS: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"69e6abb5-d26a-4a67-b0ac-3471fa14892a\" } \r\n","poolAddress":"HBS7a3br8GMMWuqVa7VB3SMFa7xVi1tSFdoF5w4ZZ3kS"}
2025-08-13 13:47:18 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:47:19 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:47:20 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:47:22 [ERROR]: Parse pool data error: {"error":"failed to get info about account A4XeGGhWRcyYRnZqHwDanEow9nrodSJDRHEwcpCbuk4e: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"bc4a2d48-d094-41e3-b519-827c15c7c7e1\" } \r\n","poolAddress":"A4XeGGhWRcyYRnZqHwDanEow9nrodSJDRHEwcpCbuk4e"}
2025-08-13 13:47:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account F4xnwLvEajvq24PQjyKuSyH9Ph5uBsixUPPRhDk88AHz: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"2b3bf321-1b55-4be7-86d2-05a0722cd72a\" } \r\n","poolAddress":"F4xnwLvEajvq24PQjyKuSyH9Ph5uBsixUPPRhDk88AHz"}
2025-08-13 13:47:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6AVGYuPWShvwtYJER5ASB3cLQ2WjrN2mHHTpfeuhVptg: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"793b29f7-4db2-4df5-bd3a-640be09a3da6\" } \r\n","poolAddress":"6AVGYuPWShvwtYJER5ASB3cLQ2WjrN2mHHTpfeuhVptg"}
2025-08-13 13:47:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account 7Mu9zK6qV3wGp5deSkhCeWqaDnL3kdD4gKL87ui6GtmX: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d0ef365d-634a-44e7-b031-9065750b9ca4\" } \r\n","poolAddress":"7Mu9zK6qV3wGp5deSkhCeWqaDnL3kdD4gKL87ui6GtmX"}
2025-08-13 13:47:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account EcVFAhRSeTcCkSNcLvW91sWirekfSDkXVZUuptAWKbuU: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a6231036-6bf4-4a70-bf60-65fb97cd7171\" } \r\n","poolAddress":"EcVFAhRSeTcCkSNcLvW91sWirekfSDkXVZUuptAWKbuU"}
2025-08-13 13:47:29 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9NffvSX9owdgrGjG2r85hQi8aEEPMYpQCvp1nbvbSt3y: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fcb09591-025e-4818-8e0b-e2d7e10afae1\" } \r\n","poolAddress":"9NffvSX9owdgrGjG2r85hQi8aEEPMYpQCvp1nbvbSt3y"}
2025-08-13 13:47:30 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9Pk43Sr7J8GYQ1h89riWSSBVH4Cp6ZxoWez9xTiAm92T: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"989f4e8f-a90d-4b0d-bab8-b06eea7cb697\" } \r\n","poolAddress":"9Pk43Sr7J8GYQ1h89riWSSBVH4Cp6ZxoWez9xTiAm92T"}
2025-08-13 13:47:30 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:47:31 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:47:31 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4Gd7CtdHLdaLV13TPry3z55XDDzQBgCZEVKekGDxxa8F: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"19f0ce1a-034b-4b23-8fa6-40b3a78d28bc\" } \r\n","poolAddress":"4Gd7CtdHLdaLV13TPry3z55XDDzQBgCZEVKekGDxxa8F"}
2025-08-13 13:47:31 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9MeHN6NLCdUdzsW7CAkGgCEyVxCACzMYBXuP8YpfgTbu: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"78c9242d-0f65-46db-b8d0-2c8f5c3e8b7b\" } \r\n","poolAddress":"9MeHN6NLCdUdzsW7CAkGgCEyVxCACzMYBXuP8YpfgTbu"}
2025-08-13 13:47:31 [ERROR]: Parse pool data error: {"error":"failed to get info about account 9uWW4C36HiCTGr6pZW9VFhr9vdXktZ8NA8jVnzQU35pJ: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"a0374e1f-1cc3-4042-be10-a30760c66187\" } \r\n","poolAddress":"9uWW4C36HiCTGr6pZW9VFhr9vdXktZ8NA8jVnzQU35pJ"}
2025-08-13 13:47:31 [ERROR]: Parse pool data error: {"error":"failed to get info about account F7j2A8bosNpXzkk1VXmUGXrbQNoT27TQukGLCa8rEFVa: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"d367db54-2426-4e31-9497-879e2b6161f0\" } \r\n","poolAddress":"F7j2A8bosNpXzkk1VXmUGXrbQNoT27TQukGLCa8rEFVa"}
2025-08-13 13:47:32 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:47:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account zRZxviEJF5kBkwukY38qsipr5UZxsRmwEXqTCVj5oN5: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"283644d6-0314-416e-84e0-037bd8d222e3\" } \r\n","poolAddress":"zRZxviEJF5kBkwukY38qsipr5UZxsRmwEXqTCVj5oN5"}
2025-08-13 13:47:34 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2CotF9J6Q6FMXq9igZmer6iHd6w8pWBD6dtnzMWzNbr3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fd7e00d2-739b-47ca-9858-751b73026cc5\" } \r\n","poolAddress":"2CotF9J6Q6FMXq9igZmer6iHd6w8pWBD6dtnzMWzNbr3"}
2025-08-13 13:47:34 [ERROR]: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results {"context":"pollForNewPools","stack":"SolanaJSONRPCError: failed to get accounts owned by program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8: Request deprioritized due to number of accounts requested. Slow down requests or add filters to narrow down results\n    at Connection.getProgramAccounts (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\node_modules\\@solana\\web3.js\\src\\connection.ts:3774:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager.executeWithRetry (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\utils\\wallet.ts:125:16)\n    at async RaydiumPoolMonitor.pollForNewPools (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:180:24)\n    at async Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\solbullet\\sol-bullet\\backend\\src\\listeners\\raydium.ts:170:9)","name":"SolanaJSONRPCError"}
2025-08-13 13:47:38 [ERROR]: Parse pool data error: {"error":"failed to get info about account 2CotF9J6Q6FMXq9igZmer6iHd6w8pWBD6dtnzMWzNbr3: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"8d8b41d2-3578-486c-9d60-cad6e49798c9\" } \r\n","poolAddress":"2CotF9J6Q6FMXq9igZmer6iHd6w8pWBD6dtnzMWzNbr3"}
2025-08-13 13:47:38 [ERROR]: Parse pool data error: {"error":"failed to get info about account EF16cKCMkrtwwdkRKDy7oumVV1n5A6sohGszVmipXcdj: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"646e21e9-0b93-4e68-be69-58f6197c4254\" } \r\n","poolAddress":"EF16cKCMkrtwwdkRKDy7oumVV1n5A6sohGszVmipXcdj"}
2025-08-13 13:47:38 [ERROR]: Parse pool data error: {"error":"failed to get info about account 12NCw1mtsUoVSLPyS6Y3SMEtyEjWMeHfkHLesbhk9hvc: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"3e7f05a4-a508-42aa-bb33-2c8a154aeb84\" } \r\n","poolAddress":"12NCw1mtsUoVSLPyS6Y3SMEtyEjWMeHfkHLesbhk9hvc"}
2025-08-13 13:47:38 [ERROR]: Parse pool data error: {"error":"failed to get info about account 4Sx1NLrQiK4b9FdLKe2DhQ9FHvRzJhzKN3LoD6BrEPnf: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"fa29673f-5c9e-4173-9f5e-3dd16b236e7e\" } \r\n","poolAddress":"4Sx1NLrQiK4b9FdLKe2DhQ9FHvRzJhzKN3LoD6BrEPnf"}
2025-08-13 13:47:40 [ERROR]: Parse pool data error: {"error":"failed to get info about account 6U37bjMU3QWuJQEiDibcF6XSJeQM6p4vV8HZ57Pdf6pF: Error: 429 Too Many Requests:  {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests from your IP\"}, \"id\": \"586acce9-f572-45b2-9206-7ae93e393031\" } \r\n","poolAddress":"6U37bjMU3QWuJQEiDibcF6XSJeQM6p4vV8HZ57Pdf6pF"}
