{"userId": "user-1755034845653", "publicKey": "GQ5GxvYV4WqWZJ8d9mNdmLrZmWHHx492N3X8sE91ZmUe", "privateKeyBytes": [16, 211, 229, 177, 53, 130, 74, 148, 130, 218, 64, 16, 108, 211, 210, 145, 164, 180, 217, 70, 42, 44, 249, 65, 94, 221, 226, 143, 40, 239, 25, 9, 228, 200, 127, 29, 39, 82, 217, 211, 124, 31, 201, 66, 95, 255, 199, 138, 109, 154, 111, 207, 143, 12, 79, 41, 113, 185, 57, 51, 49, 3, 11, 179], "privateKeyHex": "10d3e5b135824a9482da40106cd3d291a4b4d9462a2cf9415edde28f28ef1909e4c87f1d2752d9d37c1fc9425fffc78a6d9a6fcf8f0c4f2971b9393331030bb3", "settings": {"buyAmount": 0.1, "takeProfitPercent": 100, "stopLossPercent": 50, "minLiquidity": 5}, "instructions": ["Fund the wallet with SOL", "Start the sniper bot with: npm run dev", "Bot will automatically trade when new tokens are detected", "All trades executed via Jupiter for best prices"]}