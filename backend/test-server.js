// Simple test server to verify basic functionality
const express = require('express');
const app = express();

console.log('🎯 Starting Sol Bullet Test Server...');

app.use(express.json());

app.get('/health', (req, res) => {
  console.log('Health check requested');
  res.json({
    success: true,
    message: '🎯 Sol Bullet Test Server is running',
    timestamp: new Date(),
    version: '1.0.0',
  });
});

app.get('/status', (req, res) => {
  console.log('Status check requested');
  res.json({
    success: true,
    message: '🎯 Test Server Status',
    data: {
      isRunning: true,
      testMode: true,
      timestamp: new Date(),
    },
  });
});

const PORT = 3001;

app.listen(PORT, () => {
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('🎯 SOL BULLET TEST SERVER READY!');
  console.log(`🚀 Server running on port ${PORT}`);
  console.log('📊 Health check: http://localhost:3001/health');
  console.log('📈 Status check: http://localhost:3001/status');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('\n🔍 Ready to test basic functionality...');
});
