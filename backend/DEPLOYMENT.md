# 🚀 Sol Bullet Backend Deployment Guide

## 📋 Prerequisites

### Required Services
1. **Helius RPC**: Sign up at [helius.xyz](https://helius.xyz) for fastest Solana RPC
2. **Supabase**: Create project at [supabase.com](https://supabase.com) for database
3. **Server**: VPS or cloud instance (AWS, DigitalOcean, etc.)

### Optional Services
4. **Telegram Bot**: Create bot via [@BotFather](https://t.me/botfather) for notifications
5. **Discord Webhook**: Create webhook in Discord server for alerts

## 🛠️ Setup Instructions

### 1. <PERSON>lone and Install
```bash
git clone <your-repo>
cd sol-bullet/backend
npm install
```

### 2. Database Setup (Supabase)

#### Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Create new project
3. Go to SQL Editor
4. Run the contents of `database/schema.sql`

#### Get Credentials
1. Go to Project Settings → API
2. Copy Project URL and Service Role Key
3. Add to `.env` file

### 3. Environment Configuration

```bash
cp .env.example .env
```

Edit `.env` with your values:

```env
# Required - Helius RPC (fastest)
SOLANA_RPC_ENDPOINT=https://mainnet.helius-rpc.com/?api-key=YOUR_HELIUS_KEY
HELIUS_API_KEY=your_helius_api_key

# Required - Supabase Database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your_service_role_key

# Required - Security
JWT_SECRET=your_super_secret_jwt_key_minimum_32_chars
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Trading Configuration
MIN_LIQUIDITY_SOL=5
DEFAULT_BUY_AMOUNT_SOL=0.1
TAKE_PROFIT_PERCENT=100
STOP_LOSS_PERCENT=50

# Optional - Notifications
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your_webhook
```

### 4. Build and Test

```bash
# Build TypeScript
npm run build

# Test in development
npm run dev

# Test production build
npm start
```

## 🐳 Docker Deployment

### Build Image
```bash
docker build -t sol-bullet-backend .
```

### Run Container
```bash
docker run -d \
  --name sol-bullet \
  -p 3001:3001 \
  --env-file .env \
  --restart unless-stopped \
  sol-bullet-backend
```

### Docker Compose
```yaml
version: '3.8'
services:
  sol-bullet:
    build: .
    ports:
      - "3001:3001"
    env_file:
      - .env
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
```

## ☁️ Cloud Deployment

### AWS EC2
```bash
# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2
sudo npm install -g pm2

# Clone and setup
git clone <your-repo>
cd sol-bullet/backend
npm install
npm run build

# Start with PM2
pm2 start dist/server.js --name sol-bullet
pm2 startup
pm2 save
```

### DigitalOcean App Platform
1. Connect GitHub repository
2. Set build command: `npm run build`
3. Set run command: `npm start`
4. Add environment variables
5. Deploy

### Railway
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

## 🔧 Production Configuration

### Environment Variables
```env
NODE_ENV=production
DEBUG_MODE=false
DRY_RUN_MODE=false
LOG_LEVEL=info
LOG_TO_FILE=true

# Performance
MAX_CONCURRENT_TRANSACTIONS=5
TRANSACTION_RETRY_ATTEMPTS=3
POOL_MONITOR_INTERVAL_MS=500

# Security
CORS_ORIGIN=https://your-frontend-domain.com
```

### Nginx Reverse Proxy
```nginx
server {
    listen 80;
    server_name your-api-domain.com;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 📊 Monitoring

### PM2 Monitoring
```bash
# Monitor processes
pm2 monit

# View logs
pm2 logs sol-bullet

# Restart
pm2 restart sol-bullet

# Stop
pm2 stop sol-bullet
```

### Health Checks
```bash
# Check API health
curl http://localhost:3001/health

# Check status
curl http://localhost:3001/status
```

### Log Monitoring
```bash
# View real-time logs
tail -f logs/combined.log

# View trade logs
tail -f logs/trades.log

# View error logs
tail -f logs/error.log
```

## 🔒 Security Checklist

- [ ] Strong JWT secret (32+ characters)
- [ ] Unique encryption key (32 characters)
- [ ] Supabase RLS policies enabled
- [ ] CORS configured for production domain
- [ ] Environment variables secured
- [ ] Database backups configured
- [ ] SSL/TLS certificate installed
- [ ] Firewall configured (only ports 80, 443, 22)

## 🚨 Troubleshooting

### Common Issues

#### "All RPC connections failed"
- Check Helius API key and credits
- Verify RPC endpoints in `.env`
- Check network connectivity

#### "Database connection failed"
- Verify Supabase credentials
- Check database schema is created
- Ensure RLS policies are set

#### "Webhook setup failed"
- Check Helius webhook URL is accessible
- Verify webhook endpoint is running
- Check firewall allows incoming connections

#### "Transaction failed"
- Check wallet has sufficient SOL balance
- Verify slippage settings
- Check if token is tradeable

### Debug Mode
```bash
DEBUG_MODE=true npm run dev
```

### Dry Run Mode
```bash
DRY_RUN_MODE=true npm run dev
```

## 📈 Performance Optimization

### RPC Optimization
- Use Helius or other premium RPC providers
- Configure multiple backup endpoints
- Monitor RPC response times

### Database Optimization
- Enable connection pooling
- Add appropriate indexes
- Regular database maintenance

### Memory Management
- Monitor memory usage with PM2
- Configure appropriate heap limits
- Regular log rotation

## 🔄 Updates and Maintenance

### Update Dependencies
```bash
npm update
npm audit fix
```

### Database Migrations
```bash
# Run new migrations in Supabase SQL editor
# Always backup before migrations
```

### Log Rotation
```bash
# Setup logrotate for log files
sudo nano /etc/logrotate.d/sol-bullet
```

## 📞 Support

For issues and support:
1. Check logs in `logs/` directory
2. Review configuration in `.env`
3. Test with `DRY_RUN_MODE=true`
4. Monitor system resources

The backend is designed for 24/7 operation with automatic recovery and comprehensive monitoring.
