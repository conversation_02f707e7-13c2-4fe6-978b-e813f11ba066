import { EventEmitter } from 'events';
import { PublicKey } from '@solana/web3.js';
import cron from 'node-cron';
import {
  TokenDetectedEvent,
  UserWallet,
  Position,
  PriceAlert,
  LiquidityPool
} from './types';
import RaydiumPoolMonitor from './listeners/raydium';
import TokenFilterEngine from './filters/tokenChecks';
import BuyExecutor from './actions/buy';
import SellExecutor from './actions/sell';
import SupabaseManager from './database/supabase';
import { getUserWallet, getTokenBalance } from './utils/wallet';
import { userManager } from './utils/userManager';
import { notificationManager } from './utils/notifications';
import { logger, logError, tradeLogger } from './utils/logger';
import config from './config';

export class SniperBot extends EventEmitter {
  private poolMonitor: RaydiumPoolMonitor;
  private tokenFilter: TokenFilterEngine;
  private buyExecutor: BuyExecutor;
  private sellExecutor: SellExecutor;
  private database: SupabaseManager;
  private activePositions = new Map<string, Position>();
  private priceAlerts = new Map<string, PriceAlert[]>();
  private isRunning = false;

  constructor() {
    super();
    this.poolMonitor = new RaydiumPoolMonitor();
    this.tokenFilter = new TokenFilterEngine();
    this.buyExecutor = new BuyExecutor();
    this.sellExecutor = new SellExecutor();
    this.database = new SupabaseManager();
    
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Listen for new token detections
    this.poolMonitor.on('tokenDetected', this.handleTokenDetected.bind(this));
    
    // Setup price monitoring cron job
    this.setupPriceMonitoring();
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Sniper bot already running');
      return;
    }

    try {
      this.isRunning = true;
      
      // Start pool monitoring
      await this.poolMonitor.startMonitoring();
      
      // Load active positions
      await this.loadActivePositions();
      
      // Load price alerts
      await this.loadPriceAlerts();

      logger.info('🎯 Sniper bot started successfully');
      
      // Send startup notification
      await notificationManager.sendNotification({
        type: 'alert',
        title: 'Sniper Bot Started',
        message: 'Sol Bullet sniper bot is now monitoring for new tokens',
        timestamp: new Date(),
      });

    } catch (error) {
      this.isRunning = false;
      logError(error as Error, { context: 'sniperStart' });
      throw error;
    }
  }

  async stop(): Promise<void> {
    this.isRunning = false;
    await this.poolMonitor.stopMonitoring();
    
    logger.info('🛑 Sniper bot stopped');
    
    await notificationManager.sendNotification({
      type: 'alert',
      title: 'Sniper Bot Stopped',
      message: 'Sol Bullet sniper bot has been stopped',
      timestamp: new Date(),
    });
  }

  private async handleTokenDetected(event: TokenDetectedEvent): Promise<void> {
    try {
      logger.info('🔍 New token detected, running filters...', {
        tokenAddress: event.tokenAddress,
        poolId: event.poolId,
        liquidity: event.liquidity,
      });

      // Create mock liquidity pool for filtering
      const liquidityPool: LiquidityPool = {
        poolId: event.poolId,
        baseMint: event.tokenAddress,
        quoteMint: 'So11111111111111111111111111111111111111112', // SOL
        baseReserve: new (await import('decimal.js')).Decimal(0),
        quoteReserve: new (await import('decimal.js')).Decimal(event.liquidity),
        lpSupply: new (await import('decimal.js')).Decimal(0),
        price: new (await import('decimal.js')).Decimal(0),
        liquidity: {
          sol: event.liquidity,
          usd: event.liquidity * 100, // Mock USD conversion
        },
        volume24h: 0,
        createdAt: new Date(),
        isActive: true,
      };

      // Run token filters
      const filterResult = await this.tokenFilter.filterToken(
        event.tokenAddress,
        liquidityPool,
        event.metadata
      );

      if (!filterResult.passed) {
        logger.info('❌ Token filtered out', {
          tokenAddress: event.tokenAddress,
          reason: filterResult.reason,
          score: filterResult.score,
        });

        tradeLogger.filtered({
          tokenAddress: event.tokenAddress,
          reason: filterResult.reason,
          score: filterResult.score,
          checks: filterResult.checks,
        });

        await notificationManager.notifyTokenDetected({
          tokenAddress: event.tokenAddress,
          poolId: event.poolId,
          liquidity: event.liquidity,
          action: 'skipped',
          reason: filterResult.reason || 'Unknown reason',
        });

        return;
      }

      logger.info('✅ Token passed all filters, executing buys...', {
        tokenAddress: event.tokenAddress,
        score: filterResult.score,
      });

      // Execute buys for all active users
      await this.executeBuysForAllUsers(event.tokenAddress);

      await notificationManager.notifyTokenDetected({
        tokenAddress: event.tokenAddress,
        poolId: event.poolId,
        liquidity: event.liquidity,
        action: 'buying',
      });

    } catch (error) {
      logError(error as Error, { context: 'handleTokenDetected', event });
    }
  }

  private async executeBuysForAllUsers(tokenAddress: string): Promise<void> {
    try {
      logger.info('🚀 EXECUTING AUTOMATIC BUYS FOR ALL USERS', { tokenAddress });

      // Get all active users with wallets
      const activeUsers = await this.getActiveUsers();

      const buyPromises = activeUsers.map(async (user) => {
        try {
          const wallets = await this.database.getUserWallets(user.id);

          for (const walletData of wallets) {
            try {
              const userWallet = await getUserWallet(walletData);

              // Check if user has enough SOL balance
              if (userWallet.balance.sol < user.settings.trading.buyAmount + 0.01) {
                logger.warn('Insufficient balance for auto-buy', {
                  userId: user.id,
                  walletAddress: userWallet.publicKey.toString(),
                  balance: userWallet.balance.sol,
                  required: user.settings.trading.buyAmount,
                });
                continue;
              }

              // Execute automatic buy
              logger.info('🎯 EXECUTING AUTO-BUY', {
                userId: user.id,
                walletAddress: userWallet.publicKey.toString(),
                tokenAddress,
                amount: user.settings.trading.buyAmount,
              });

              const result = await this.buyExecutor.quickBuy(
                userWallet,
                tokenAddress,
                user.settings.trading.buyAmount
              );

              if (result.success) {
                logger.info('✅ AUTO-BUY SUCCESSFUL', {
                  userId: user.id,
                  tokenAddress,
                  signature: result.signature,
                  amount: result.amount,
                  price: result.price,
                });

                // Save transaction to database
                await this.database.saveTransaction({
                  userId: user.id,
                  walletAddress: userWallet.publicKey.toString(),
                  type: 'buy',
                  tokenAddress,
                  amount: result.amount || 0,
                  price: result.price || 0,
                  slippage: result.slippage || 0,
                  signature: result.signature || '',
                  status: 'confirmed',
                });

                // Create position tracking for auto-sell
                await this.createPosition(user.id, userWallet, tokenAddress, result);

                // Setup automatic price alerts for selling
                await this.setupPriceAlerts(user.id, tokenAddress, result.price || 0);

                // Send success notification
                await notificationManager.notifyBuy({
                  tokenAddress,
                  amount: result.amount || 0,
                  price: result.price || 0,
                  signature: result.signature || '',
                });

              } else {
                logger.error('❌ AUTO-BUY FAILED', {
                  userId: user.id,
                  tokenAddress,
                  error: result.error,
                });

                // Save failed transaction
                await this.database.saveTransaction({
                  userId: user.id,
                  walletAddress: userWallet.publicKey.toString(),
                  type: 'buy',
                  tokenAddress,
                  amount: 0,
                  price: 0,
                  slippage: 0,
                  signature: '',
                  status: 'failed',
                  error: result.error || 'Unknown error',
                });
              }

            } catch (walletError) {
              logError(walletError as Error, {
                context: 'executeBuyForUserWallet',
                userId: user.id,
                walletId: walletData.id
              });
            }
          }
        } catch (userError) {
          logError(userError as Error, { context: 'executeBuyForUser', userId: user.id });
        }
      });

      // Execute all buys concurrently (up to max concurrent limit)
      const chunks = this.chunkArray(buyPromises, config.limits.maxConcurrentTransactions);

      for (const chunk of chunks) {
        await Promise.allSettled(chunk);
        // Small delay between chunks to avoid overwhelming the RPC
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      logger.info('🎯 AUTO-BUY EXECUTION COMPLETED', {
        tokenAddress,
        totalUsers: activeUsers.length,
        executionTime: Date.now() - Date.now(),
      });

    } catch (error) {
      logError(error as Error, { context: 'executeBuysForAllUsers', tokenAddress });
    }
  }

  private async createPosition(
    userId: string,
    wallet: UserWallet,
    tokenAddress: string,
    buyResult: any
  ): Promise<void> {
    try {
      const position: Omit<Position, 'id' | 'createdAt'> = {
        userId,
        walletAddress: wallet.publicKey.toString(),
        tokenAddress,
        amount: buyResult.amount,
        averageBuyPrice: buyResult.price,
        currentPrice: buyResult.price,
        pnl: 0,
        pnlPercent: 0,
        status: 'active',
      };

      const savedPosition = await this.database.savePosition(position);
      this.activePositions.set(`${userId}-${tokenAddress}`, savedPosition);

      logger.info('Position created', {
        userId,
        tokenAddress,
        amount: buyResult.amount,
        price: buyResult.price,
      });
    } catch (error) {
      logError(error as Error, { context: 'createPosition', userId, tokenAddress });
    }
  }

  private async setupPriceAlerts(
    userId: string,
    tokenAddress: string,
    buyPrice: number
  ): Promise<void> {
    try {
      const alerts: PriceAlert[] = [];

      // Take profit alert
      if (config.profitLoss.takeProfitPercent > 0) {
        const takeProfitPrice = buyPrice * (1 + config.profitLoss.takeProfitPercent / 100);
        alerts.push({
          id: `${userId}-${tokenAddress}-tp`,
          userId,
          tokenAddress,
          type: 'take_profit',
          targetPrice: takeProfitPrice,
          currentPrice: buyPrice,
          percentage: config.profitLoss.takeProfitPercent,
          isActive: true,
          triggeredAt: undefined,
        });
      }

      // Stop loss alert
      if (config.profitLoss.stopLossPercent > 0) {
        const stopLossPrice = buyPrice * (1 - config.profitLoss.stopLossPercent / 100);
        alerts.push({
          id: `${userId}-${tokenAddress}-sl`,
          userId,
          tokenAddress,
          type: 'stop_loss',
          targetPrice: stopLossPrice,
          currentPrice: buyPrice,
          percentage: -config.profitLoss.stopLossPercent,
          isActive: true,
          triggeredAt: undefined,
        });
      }

      // Store alerts
      this.priceAlerts.set(`${userId}-${tokenAddress}`, alerts);

      logger.info('Price alerts setup', {
        userId,
        tokenAddress,
        alertCount: alerts.length,
      });
    } catch (error) {
      logError(error as Error, { context: 'setupPriceAlerts', userId, tokenAddress });
    }
  }

  private setupPriceMonitoring(): void {
    // Monitor prices every 2 seconds
    cron.schedule('*/2 * * * * *', async () => {
      if (!this.isRunning) return;
      
      try {
        await this.checkPriceAlerts();
      } catch (error) {
        logError(error as Error, { context: 'priceMonitoring' });
      }
    });
  }

  private async checkPriceAlerts(): Promise<void> {
    for (const [key, alerts] of this.priceAlerts.entries()) {
      const parts = key.split('-');
      if (parts.length < 2) continue;

      const [userId, tokenAddress] = parts;

      try {
        // Get current token price (would implement price fetching)
        const currentPrice = await this.getCurrentTokenPrice(tokenAddress);
        if (!currentPrice) continue;

        for (const alert of alerts) {
          if (!alert.isActive) continue;

          const shouldTrigger = this.shouldTriggerAlert(alert, currentPrice);
          
          if (shouldTrigger) {
            await this.triggerPriceAlert(alert, currentPrice);
          }
        }
      } catch (error) {
        logError(error as Error, { context: 'checkPriceAlert', userId, tokenAddress });
      }
    }
  }

  private async getCurrentTokenPrice(tokenAddress: string): Promise<number | null> {
    try {
      // Get price from Jupiter API
      const response = await fetch(
        `${config.jupiter.apiUrl}/price?ids=${tokenAddress}&vsToken=So11111111111111111111111111111111111111112`
      );

      if (!response.ok) {
        throw new Error(`Price API error: ${response.status}`);
      }

      const data = await response.json();
      const priceData = data.data?.[tokenAddress];

      if (!priceData) {
        // Fallback: try to get price via quote
        return this.getPriceViaQuote(tokenAddress);
      }

      return parseFloat(priceData.price);
    } catch (error) {
      logError(error as Error, { context: 'getCurrentTokenPrice', tokenAddress });
      return null;
    }
  }

  private async getPriceViaQuote(tokenAddress: string): Promise<number | null> {
    try {
      // Get a small quote to determine price
      const response = await fetch(`${config.jupiter.apiUrl}/quote?` + new URLSearchParams({
        inputMint: tokenAddress,
        outputMint: 'So11111111111111111111111111111111111111112', // SOL
        amount: '1000000', // 1 token (assuming 6 decimals)
        slippageBps: '100', // 1% slippage
      }));

      if (!response.ok) return null;

      const quote = await response.json();
      const inputAmount = parseFloat(quote.inAmount);
      const outputAmount = parseFloat(quote.outAmount);

      if (inputAmount > 0 && outputAmount > 0) {
        return outputAmount / inputAmount; // Price in SOL
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  private shouldTriggerAlert(alert: PriceAlert, currentPrice: number): boolean {
    switch (alert.type) {
      case 'take_profit':
        return currentPrice >= alert.targetPrice;
      case 'stop_loss':
        return currentPrice <= alert.targetPrice;
      case 'trailing_stop':
        // Implement trailing stop logic
        return false;
      default:
        return false;
    }
  }

  private async triggerPriceAlert(alert: PriceAlert, currentPrice: number): Promise<void> {
    try {
      // Mark alert as triggered
      alert.isActive = false;
      alert.triggeredAt = new Date();

      logger.info('🚨 PRICE ALERT TRIGGERED - EXECUTING AUTO-SELL', {
        userId: alert.userId,
        tokenAddress: alert.tokenAddress,
        type: alert.type,
        currentPrice,
        targetPrice: alert.targetPrice,
      });

      // Execute automatic sell
      if (config.profitLoss.autoSellEnabled) {
        await this.executeAutoSell(alert, currentPrice);
      }

      // Send notification
      await notificationManager.notifyPriceAlert({
        tokenAddress: alert.tokenAddress,
        type: alert.type,
        currentPrice,
        targetPrice: alert.targetPrice,
        percentage: ((currentPrice - alert.targetPrice) / alert.targetPrice) * 100,
      });

    } catch (error) {
      logError(error as Error, { context: 'triggerPriceAlert', alert });
    }
  }

  private async executeAutoSell(alert: PriceAlert, currentPrice: number): Promise<void> {
    try {
      // Get user and wallet data
      const user = await this.database.getUser(alert.userId);
      if (!user) {
        logger.error('User not found for auto-sell', { userId: alert.userId });
        return;
      }

      const wallets = await this.database.getUserWallets(alert.userId);
      const walletData = wallets.find(_w =>
        this.activePositions.has(`${alert.userId}-${alert.tokenAddress}`)
      );

      if (!walletData) {
        logger.error('Wallet not found for auto-sell', { userId: alert.userId });
        return;
      }

      const userWallet = await getUserWallet(walletData);

      // Check token balance
      const tokenBalance = await getTokenBalance(
        userWallet.publicKey,
        new PublicKey(alert.tokenAddress)
      );

      if (tokenBalance <= 0) {
        logger.warn('No token balance for auto-sell', {
          userId: alert.userId,
          tokenAddress: alert.tokenAddress,
        });
        return;
      }

      // Add delay if configured
      if (config.profitLoss.autoSellDelayMs > 0) {
        logger.info(`⏳ Auto-sell delay: ${config.profitLoss.autoSellDelayMs}ms`);
        await new Promise(resolve => setTimeout(resolve, config.profitLoss.autoSellDelayMs));
      }

      // Execute sell order
      logger.info('🔴 EXECUTING AUTO-SELL', {
        userId: alert.userId,
        tokenAddress: alert.tokenAddress,
        balance: tokenBalance,
        triggerType: alert.type,
        currentPrice,
      });

      const sellResult = await this.sellExecutor.quickSell(
        userWallet,
        alert.tokenAddress,
        100 // Sell 100% of holdings
      );

      if (sellResult.success) {
        // Calculate P&L
        const position = this.activePositions.get(`${alert.userId}-${alert.tokenAddress}`);
        const buyPrice = position?.averageBuyPrice || 0;
        const pnlPercent = buyPrice > 0 ? ((currentPrice - buyPrice) / buyPrice) * 100 : 0;

        logger.info('✅ AUTO-SELL SUCCESSFUL', {
          userId: alert.userId,
          tokenAddress: alert.tokenAddress,
          signature: sellResult.signature,
          amount: sellResult.amount,
          price: sellResult.price,
          pnl: pnlPercent,
        });

        // Save sell transaction
        await this.database.saveTransaction({
          userId: alert.userId,
          walletAddress: userWallet.publicKey.toString(),
          type: 'sell',
          tokenAddress: alert.tokenAddress,
          amount: sellResult.amount || 0,
          price: sellResult.price || 0,
          slippage: sellResult.slippage || 0,
          signature: sellResult.signature || '',
          status: 'confirmed',
        });

        // Close position
        if (position) {
          position.status = 'closed';
          position.closedAt = new Date();
          position.currentPrice = currentPrice;
          position.pnl = (currentPrice - position.averageBuyPrice) * position.amount;
          position.pnlPercent = pnlPercent;

          // Remove from active positions
          this.activePositions.delete(`${alert.userId}-${alert.tokenAddress}`);
        }

        // Send success notification with P&L
        await notificationManager.notifySell({
          tokenAddress: alert.tokenAddress,
          amount: sellResult.amount || 0,
          price: sellResult.price || 0,
          pnl: pnlPercent,
          signature: sellResult.signature || '',
        });

        // Remove all price alerts for this token
        this.priceAlerts.delete(`${alert.userId}-${alert.tokenAddress}`);

      } else {
        logger.error('❌ AUTO-SELL FAILED', {
          userId: alert.userId,
          tokenAddress: alert.tokenAddress,
          error: sellResult.error,
        });

        // Save failed transaction
        await this.database.saveTransaction({
          userId: alert.userId,
          walletAddress: userWallet.publicKey.toString(),
          type: 'sell',
          tokenAddress: alert.tokenAddress,
          amount: 0,
          price: 0,
          slippage: 0,
          signature: '',
          status: 'failed',
          error: sellResult.error || 'Unknown error',
        });

        // Retry alert (reactivate for another attempt)
        alert.isActive = true;
        alert.triggeredAt = undefined;
      }

    } catch (error) {
      logError(error as Error, { context: 'executeAutoSell', alert });
    }
  }

  private async loadActivePositions(): Promise<void> {
    try {
      // Load all active positions from database
      // This would be implemented with proper database queries
      logger.info('Active positions loaded', { count: this.activePositions.size });
    } catch (error) {
      logError(error as Error, { context: 'loadActivePositions' });
    }
  }

  private async loadPriceAlerts(): Promise<void> {
    try {
      // Load all active price alerts from database
      // This would be implemented with proper database queries
      logger.info('Price alerts loaded', { count: this.priceAlerts.size });
    } catch (error) {
      logError(error as Error, { context: 'loadPriceAlerts' });
    }
  }

  private async getActiveUsers(): Promise<any[]> {
    try {
      // Get all users with sniper enabled from user manager
      return await userManager.getActiveSniperUsers();
    } catch (error) {
      logError(error as Error, { context: 'getActiveUsers' });
      return [];
    }
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  // Public methods for manual control
  async addUserWallet(userId: string, _privateKeyHex: string): Promise<void> {
    try {
      // This would create a new wallet for a user
      logger.info('Adding user wallet', { userId });
    } catch (error) {
      logError(error as Error, { context: 'addUserWallet', userId });
      throw error;
    }
  }

  async removeUserWallet(userId: string, walletAddress: string): Promise<void> {
    try {
      // This would remove a wallet for a user
      logger.info('Removing user wallet', { userId, walletAddress });
    } catch (error) {
      logError(error as Error, { context: 'removeUserWallet', userId, walletAddress });
      throw error;
    }
  }

  getStatus(): any {
    return {
      isRunning: this.isRunning,
      activePositions: this.activePositions.size,
      priceAlerts: Array.from(this.priceAlerts.values()).flat().length,
      config: {
        minLiquidity: config.trading.minLiquiditySol,
        autoSellEnabled: config.profitLoss.autoSellEnabled,
        takeProfitPercent: config.profitLoss.takeProfitPercent,
        stopLossPercent: config.profitLoss.stopLossPercent,
      },
    };
  }
}

// Create singleton instance
export const sniperBot = new SniperBot();

export default sniperBot;
