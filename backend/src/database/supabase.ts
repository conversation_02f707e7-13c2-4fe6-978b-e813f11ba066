import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { WalletData, User, Transaction, Position, UserSettings } from '../types';
import { encryptPrivateKey } from '../utils/wallet';
import { logError } from '../utils/logger';
import config from '../config';

export class SupabaseManager {
  private supabase: SupabaseClient;

  constructor() {
    if (!config.database.supabaseUrl || !config.database.supabaseServiceKey) {
      throw new Error('Supabase configuration missing');
    }

    this.supabase = createClient(
      config.database.supabaseUrl,
      config.database.supabaseServiceKey
    );
  }

  // User Management
  async createUser(email?: string): Promise<User> {
    try {
      const userData = {
        email,
        wallets: [],
        settings: this.getDefaultUserSettings(),
        created_at: new Date(),
        updated_at: new Date(),
      };

      const { data, error } = await this.supabase
        .from('users')
        .insert(userData)
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        email: data.email,
        wallets: data.wallets,
        settings: data.settings,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };
    } catch (error) {
      logError(error as Error, { context: 'createUser', email });
      throw error;
    }
  }

  async getUser(userId: string): Promise<User | null> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        throw error;
      }

      return {
        id: data.id,
        email: data.email,
        wallets: data.wallets,
        settings: data.settings,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };
    } catch (error) {
      logError(error as Error, { context: 'getUser', userId });
      throw error;
    }
  }

  async updateUserSettings(userId: string, settings: UserSettings): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('users')
        .update({ 
          settings,
          updated_at: new Date(),
        })
        .eq('id', userId);

      if (error) throw error;
    } catch (error) {
      logError(error as Error, { context: 'updateUserSettings', userId });
      throw error;
    }
  }

  // Wallet Management
  async createWallet(userId: string, publicKey: string, privateKey: Uint8Array): Promise<WalletData> {
    try {
      const encryptedPrivateKey = encryptPrivateKey(privateKey, config.security.encryptionKey);
      
      const walletData = {
        user_id: userId,
        public_key: publicKey,
        encrypted_private_key: encryptedPrivateKey,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const { data, error } = await this.supabase
        .from('wallets')
        .insert(walletData)
        .select()
        .single();

      if (error) throw error;

      // Update user's wallet list
      await this.addWalletToUser(userId, data.id);

      return {
        id: data.id,
        userId: data.user_id,
        publicKey: data.public_key,
        encryptedPrivateKey: data.encrypted_private_key,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };
    } catch (error) {
      logError(error as Error, { context: 'createWallet', userId, publicKey });
      throw error;
    }
  }

  async getWallet(walletId: string): Promise<WalletData | null> {
    try {
      const { data, error } = await this.supabase
        .from('wallets')
        .select('*')
        .eq('id', walletId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null;
        throw error;
      }

      return {
        id: data.id,
        userId: data.user_id,
        publicKey: data.public_key,
        encryptedPrivateKey: data.encrypted_private_key,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };
    } catch (error) {
      logError(error as Error, { context: 'getWallet', walletId });
      throw error;
    }
  }

  async getWalletByPublicKey(publicKey: string): Promise<WalletData | null> {
    try {
      const { data, error } = await this.supabase
        .from('wallets')
        .select('*')
        .eq('public_key', publicKey)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null;
        throw error;
      }

      return {
        id: data.id,
        userId: data.user_id,
        publicKey: data.public_key,
        encryptedPrivateKey: data.encrypted_private_key,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };
    } catch (error) {
      logError(error as Error, { context: 'getWalletByPublicKey', publicKey });
      throw error;
    }
  }

  async getUserWallets(userId: string): Promise<WalletData[]> {
    try {
      const { data, error } = await this.supabase
        .from('wallets')
        .select('*')
        .eq('user_id', userId);

      if (error) throw error;

      return data.map(wallet => ({
        id: wallet.id,
        userId: wallet.user_id,
        publicKey: wallet.public_key,
        encryptedPrivateKey: wallet.encrypted_private_key,
        createdAt: new Date(wallet.created_at),
        updatedAt: new Date(wallet.updated_at),
      }));
    } catch (error) {
      logError(error as Error, { context: 'getUserWallets', userId });
      throw error;
    }
  }

  private async addWalletToUser(userId: string, walletId: string): Promise<void> {
    try {
      const user = await this.getUser(userId);
      if (!user) throw new Error('User not found');

      const updatedWallets = [...user.wallets, walletId];

      const { error } = await this.supabase
        .from('users')
        .update({ 
          wallets: updatedWallets,
          updated_at: new Date(),
        })
        .eq('id', userId);

      if (error) throw error;
    } catch (error) {
      logError(error as Error, { context: 'addWalletToUser', userId, walletId });
      throw error;
    }
  }

  // Transaction Management
  async saveTransaction(transaction: Omit<Transaction, 'id' | 'createdAt'>): Promise<Transaction> {
    try {
      const transactionData = {
        ...transaction,
        created_at: new Date(),
      };

      const { data, error } = await this.supabase
        .from('transactions')
        .insert(transactionData)
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        userId: data.user_id,
        walletAddress: data.wallet_address,
        type: data.type,
        tokenAddress: data.token_address,
        amount: data.amount,
        price: data.price,
        slippage: data.slippage,
        signature: data.signature,
        status: data.status,
        error: data.error,
        createdAt: new Date(data.created_at),
        confirmedAt: data.confirmed_at ? new Date(data.confirmed_at) : new Date(),
      };
    } catch (error) {
      logError(error as Error, { context: 'saveTransaction', transaction });
      throw error;
    }
  }

  async updateTransactionStatus(
    transactionId: string, 
    status: 'confirmed' | 'failed', 
    error?: string
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
        updated_at: new Date(),
      };

      if (status === 'confirmed') {
        updateData.confirmed_at = new Date();
      }

      if (error) {
        updateData.error = error;
      }

      const { error: updateError } = await this.supabase
        .from('transactions')
        .update(updateData)
        .eq('id', transactionId);

      if (updateError) throw updateError;
    } catch (error) {
      logError(error as Error, { context: 'updateTransactionStatus', transactionId, status });
      throw error;
    }
  }

  // Position Management
  async savePosition(position: Omit<Position, 'id' | 'createdAt'>): Promise<Position> {
    try {
      const positionData = {
        ...position,
        created_at: new Date(),
      };

      const { data, error } = await this.supabase
        .from('positions')
        .insert(positionData)
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        userId: data.user_id,
        walletAddress: data.wallet_address,
        tokenAddress: data.token_address,
        amount: data.amount,
        averageBuyPrice: data.average_buy_price,
        currentPrice: data.current_price,
        pnl: data.pnl,
        pnlPercent: data.pnl_percent,
        status: data.status,
        createdAt: new Date(data.created_at),
        closedAt: data.closed_at ? new Date(data.closed_at) : new Date(),
      };
    } catch (error) {
      logError(error as Error, { context: 'savePosition', position });
      throw error;
    }
  }

  private getDefaultUserSettings(): UserSettings {
    return {
      trading: {
        buyAmount: config.trading.defaultBuyAmountSol,
        slippage: config.trading.defaultSlippagePercent,
        autoSell: config.profitLoss.autoSellEnabled,
        takeProfitPercent: config.profitLoss.takeProfitPercent,
        stopLossPercent: config.profitLoss.stopLossPercent,
      },
      safety: {
        minLiquidity: config.trading.minLiquiditySol,
        minHolders: config.safety.minHolders,
        maxBuyTax: config.safety.maxBuyTaxPercent,
        maxSellTax: config.safety.maxSellTaxPercent,
        minLiquidityLockDays: config.safety.minLiquidityLockDays,
        honeypotCheck: config.safety.honeypotCheckEnabled,
        rugPullCheck: config.safety.rugPullProtectionEnabled,
        metadataCheck: true,
        blacklistedTokens: [],
        blacklistedCreators: [],
      },
      notifications: {
        telegram: false,
        discord: false,
        email: false,
      },
    };
  }
}

export default SupabaseManager;
