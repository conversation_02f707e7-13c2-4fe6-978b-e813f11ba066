import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { PublicKey } from '@solana/web3.js';
import { ApiResponse } from './types';
import { getSolBalance, getTokenBalances, isValidPublicKey } from './utils/wallet';
import { autoSniperEngine } from './engine/autoSniper';
import { userManager } from './utils/userManager';
import { logger, logError, systemLogger } from './utils/logger';
import config from './config';

const app = express();

// Function to find an available port
async function findAvailablePort(startPort: number = 3000): Promise<number> {
  const net = await import('net');

  const isPortAvailable = (port: number): Promise<boolean> => {
    return new Promise((resolve) => {
      const server = net.createServer();

      server.listen(port, () => {
        server.close(() => {
          resolve(true);
        });
      });

      server.on('error', () => {
        resolve(false);
      });
    });
  };

  let currentPort = startPort;
  while (currentPort < startPort + 100) { // Try up to 100 ports
    if (await isPortAvailable(currentPort)) {
      return currentPort;
    }
    currentPort++;
  }

  throw new Error(`No available port found starting from ${startPort}`);
}

// Middleware
app.use(helmet());
app.use(cors({
  origin: config.api.corsOrigin,
  credentials: true,
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Request logging middleware
app.use((req, _res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.method === 'POST' ? req.body : undefined,
  });
  next();
});

// Health check endpoint
app.get('/health', (_req, res) => {
  res.json({
    success: true,
    message: '🎯 Sol Bullet Automated Sniper Bot is running',
    timestamp: new Date(),
    version: '1.0.0',
    sniper: autoSniperEngine.getStatus(),
  });
});

// Get wallet balance
app.get('/balance/:wallet', async (req, res) => {
  try {
    const { wallet } = req.params;

    if (!isValidPublicKey(wallet)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid wallet address',
      });
    }

    const publicKey = new PublicKey(wallet);
    const [solBalance, tokenBalances] = await Promise.all([
      getSolBalance(publicKey),
      getTokenBalances(publicKey),
    ]);

    const response: ApiResponse = {
      success: true,
      data: {
        wallet,
        balances: {
          sol: solBalance,
          tokens: tokenBalances,
        },
      },
      timestamp: new Date(),
    };

    return res.json(response);
  } catch (error) {
    logError(error as Error, { context: 'getBalance', wallet: req.params.wallet });
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch balance',
      message: (error as Error).message,
    });
  }
});

// Start automated sniper for user
app.post('/sniper/start', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required',
      });
    }

    await userManager.enableSniperForUser(userId);

    return res.json({
      success: true,
      message: '🎯 Automated sniper enabled for user',
      data: { userId },
      timestamp: new Date(),
    });

  } catch (error) {
    logError(error as Error, { context: 'startSniperEndpoint', body: req.body });
    return res.status(500).json({
      success: false,
      error: 'Failed to start sniper',
      message: (error as Error).message,
    });
  }
});

// Stop automated sniper for user
app.post('/sniper/stop', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required',
      });
    }

    await userManager.disableSniperForUser(userId);

    return res.json({
      success: true,
      message: '🛑 Automated sniper disabled for user',
      data: { userId },
      timestamp: new Date(),
    });

  } catch (error) {
    logError(error as Error, { context: 'stopSniperEndpoint', body: req.body });
    return res.status(500).json({
      success: false,
      error: 'Failed to stop sniper',
      message: (error as Error).message,
    });
  }
});

// Add wallet to user for sniper
app.post('/user/wallet', async (req, res) => {
  try {
    const { userId, privateKey } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required',
      });
    }

    const walletData = await userManager.addWalletToUser(userId, privateKey);

    return res.json({
      success: true,
      message: '💰 Wallet added to user for automated trading',
      data: {
        walletId: walletData.id,
        publicKey: walletData.publicKey,
      },
      timestamp: new Date(),
    });

  } catch (error) {
    logError(error as Error, { context: 'addWalletEndpoint', body: req.body });
    return res.status(500).json({
      success: false,
      error: 'Failed to add wallet',
      message: (error as Error).message,
    });
  }
});

// Helius webhook endpoint for automated detection
app.post('/webhook/helius', async (_req, res) => {
  try {
    // The autoSniperEngine handles this automatically
    res.json({
      success: true,
      message: 'Webhook received - automated processing active'
    });
  } catch (error) {
    logError(error as Error, { context: 'heliusWebhook' });
    res.status(500).json({ success: false, error: 'Webhook processing failed' });
  }
});

// Get automated sniper status
app.get('/status', (_req, res) => {
  const sniperStatus = autoSniperEngine.getStatus();

  res.json({
    success: true,
    message: '🎯 Automated Sniper Status',
    data: {
      ...sniperStatus,
      performance: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
      },
      jupiterIntegration: {
        apiUrl: config.jupiter.apiUrl,
        swapApiUrl: config.jupiter.swapApiUrl,
        enabled: true,
      },
    },
    timestamp: new Date(),
  });
});

// API version of status endpoint for frontend
app.get('/api/status', (_req, res) => {
  const sniperStatus = autoSniperEngine.getStatus();

  res.json({
    isRunning: sniperStatus.isRunning,
    poolsDetected: sniperStatus.poolsDetected || 0,
    recentTokens: sniperStatus.recentTokens || 0,
    config: {
      raydiumV4: sniperStatus.isRunning ? 'Connected' : 'Not connected',
      minLiquidity: config.trading.minLiquiditySol,
      buyAmount: config.trading.defaultBuyAmountSol,
      takeProfitPercent: config.profitLoss.takeProfitPercent,
      stopLossPercent: config.profitLoss.stopLossPercent,
      jupiterEnabled: true,
    },
    performance: {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
    },
  });
});

// Start sniper endpoint
app.post('/api/sniper/start', async (_req, res) => {
  try {
    if (autoSniperEngine.getStatus().isRunning) {
      return res.status(400).json({ error: 'Sniper is already running' });
    }

    await autoSniperEngine.start();
    return res.json({ success: true, message: 'Sniper started successfully' });
  } catch (error) {
    logError(error as Error, { context: 'startSniper' });
    return res.status(500).json({ error: 'Failed to start sniper' });
  }
});

// Stop sniper endpoint
app.post('/api/sniper/stop', async (_req, res) => {
  try {
    if (!autoSniperEngine.getStatus().isRunning) {
      return res.status(400).json({ error: 'Sniper is not running' });
    }

    await autoSniperEngine.stop();
    return res.json({ success: true, message: 'Sniper stopped successfully' });
  } catch (error) {
    logError(error as Error, { context: 'stopSniper' });
    return res.status(500).json({ error: 'Failed to stop sniper' });
  }
});

// Error handling middleware
app.use((error: Error, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  logError(error, {
    context: 'expressErrorHandler',
    method: req.method,
    path: req.path,
    body: req.body,
  });

  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: config.development.debugMode ? error.message : 'Something went wrong',
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    message: `${req.method} ${req.originalUrl} is not a valid endpoint`,
  });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  systemLogger.shutdown({ reason: 'SIGTERM' });
  await autoSniperEngine.stop();
  process.exit(0);
});

process.on('SIGINT', async () => {
  systemLogger.shutdown({ reason: 'SIGINT' });
  await autoSniperEngine.stop();
  process.exit(0);
});

// Start server with automated sniper
async function startServer(): Promise<void> {
  try {
    logger.info('🎯 STARTING SOL BULLET AUTOMATED SNIPER BOT');

    // Start the automated sniper engine
    await autoSniperEngine.start();

    // Find an available port starting from the configured port
    let availablePort: number;
    try {
      availablePort = await findAvailablePort(config.api.port);
    } catch (error) {
      logger.error('Failed to find available port', { error });
      availablePort = config.api.port; // Fallback to original port
    }

    // Start HTTP server
    app.listen(availablePort, () => {
      systemLogger.startup({
        port: availablePort,
        nodeEnv: config.api.nodeEnv,
        pid: process.pid,
        sniperMode: 'AUTOMATED',
      });

      logger.info(`🎯 Sol Bullet AUTOMATED Sniper Bot running on port ${availablePort}`);
      if (availablePort !== config.api.port) {
        logger.info(`⚠️  Port ${config.api.port} was in use, using port ${availablePort} instead`);
      }
      logger.info(`🚀 AUTOMATED MODE: Bot will automatically detect and trade tokens`);
      logger.info(`📊 Jupiter Integration: ${config.jupiter.apiUrl}`);
      logger.info(`💰 Auto-Buy Amount: ${config.trading.defaultBuyAmountSol} SOL`);
      logger.info(`📈 Take Profit: ${config.profitLoss.takeProfitPercent}%`);
      logger.info(`📉 Stop Loss: ${config.profitLoss.stopLossPercent}%`);
      logger.info(`Environment: ${config.api.nodeEnv}`);
      logger.info(`Debug mode: ${config.development.debugMode}`);
      logger.info(`Dry run mode: ${config.development.dryRunMode}`);

      console.log('\n🎯 SOL BULLET AUTOMATED SNIPER BOT READY!');
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      console.log('🔍 MONITORING: Raydium pools for new token launches');
      console.log('⚡ AUTO-BUY: Tokens that pass safety filters');
      console.log('📈 AUTO-SELL: When profit/loss targets are hit');
      console.log('🔄 JUPITER: All trades executed via Jupiter for best prices');
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
    });

  } catch (error) {
    logError(error as Error, { context: 'startServer' });
    process.exit(1);
  }
}

// Start the server
startServer().catch((error) => {
  logError(error, { context: 'serverStartup' });
  process.exit(1);
});

export default app;
