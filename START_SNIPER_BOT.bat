@echo off
echo 🎯 Starting Sol Bullet Sniper Bot...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo 🤖 MULTI-USER AUTOMATED SNIPER BOT
echo 🗄️ Database: Supabase "sniper1" project
echo 🔍 Monitoring: Real Raydium pools for new tokens
echo ⚡ Auto-Buy: Promising tokens via Jupiter
echo 📈 Auto-Sell: When profit/loss targets hit
echo 🔄 Multi-User: Supports unlimited users
echo 📡 API: Real-time streaming to frontend
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.

cd backend
echo 📁 Changed to backend directory...
echo 🚀 Starting multi-user sniper bot...
echo 📡 API will be available at: http://localhost:3001
echo 🔗 Frontend can connect to: http://localhost:3001/stream
echo.

node live-sniper.js

pause
