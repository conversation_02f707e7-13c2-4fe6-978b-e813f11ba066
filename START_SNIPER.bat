@echo off
echo 🎯 Starting Sol Bullet Automated Sniper Bot...
echo.
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo 🤖 FULLY AUTOMATED SNIPER BOT
echo 🗄️ Database: Supabase "sniper1" project connected
echo 🔍 Monitoring: Raydium pools for new tokens
echo ⚡ Auto-Buy: Promising tokens via Jupiter
echo 📈 Auto-Sell: When profit/loss targets hit
echo 🔄 Continuous: Never stops trading
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.

cd backend
echo 📁 Changed to backend directory...
echo 🚀 Starting automated sniper...
echo.

node_modules\.bin\nodemon src/server.ts

pause
