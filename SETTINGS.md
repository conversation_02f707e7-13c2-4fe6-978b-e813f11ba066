# ⚙️ Sol Bullet Settings System

## Overview

The Sol Bullet frontend now includes a comprehensive settings system that allows users to configure all aspects of the sniper bot's behavior. Settings are automatically saved to localStorage and synchronized with the backend API calls.

## Settings Categories

### 💰 Buy Settings

Configure how the sniper bot handles buy orders:

- **Buy Amount Type**: Fixed SOL amount or percentage of balance
- **Buy Amount**: Specific SOL amount (0.001 - unlimited)
- **Percentage of Balance**: 1-100% of current SOL balance
- **Buy Slippage**: 0-100% (recommended: 3-10%)
- **Max Slippage**: Maximum allowed slippage limit
- **Priority Fee**: SOL amount for transaction priority
- **Max Priority Fee**: Maximum fee limit
- **Auto Buy**: Enable/disable automatic buy execution
- **Auto Buy Delay**: Delay in milliseconds before execution

### 💸 Sell Settings

Configure sell orders and profit-taking strategies:

- **Sell Amount Type**: Percentage of holdings or fixed token amount
- **Sell Percentage**: 1-100% of token holdings
- **Sell Slippage**: 0-100% (recommended: 3-10%)
- **Take Profit**: Enable automatic profit-taking at specified percentage
- **Stop Loss**: Enable automatic loss-cutting at specified percentage
- **Trailing Stop**: Enable trailing stop-loss functionality
- **Auto Sell**: Enable/disable automatic sell execution
- **Auto Sell Delay**: Delay in milliseconds before execution

### 🛡️ Safety Settings

Risk management and protection features:

- **MEV Protection**: Protect against MEV attacks
- **Rug Pull Protection**: Detect and prevent rug pull tokens
- **Honeypot Detection**: Identify honeypot tokens
- **Transaction Limits**: Max transaction size and daily limits
- **Token Validation**: Validate metadata and liquidity
- **Minimum Liquidity**: Required USD liquidity before trading
- **Tax Limits**: Maximum buy/sell tax percentages
- **Blacklists**: Block specific tokens and creators
- **Confirmation**: Require manual confirmation for trades

### ⚙️ General Settings

Application-wide preferences:

- **RPC Endpoint**: Custom Solana RPC endpoint
- **Theme**: Dark/Light mode (currently dark only)
- **Notifications**: Sound and browser notifications
- **Transaction Simulation**: Test transactions before execution
- **Logging**: Enable transaction logging
- **Performance**: Concurrent transactions and retry attempts
- **Import/Export**: Backup and restore settings

## Usage

### Accessing Settings

1. Click the ⚙️ gear icon in the top-right corner of the main interface
2. Navigate through the tabs: Buy, Sell, Safety, General
3. Modify settings as needed
4. Settings are automatically saved

### Settings Integration

The settings are automatically integrated with:

- **Trading Buttons**: Use configured buy/sell amounts and slippage
- **API Calls**: Send settings parameters to backend
- **Real-time Updates**: Settings changes immediately affect trading behavior

### Import/Export Settings

- **Export**: Click "Export Settings" to copy JSON to clipboard
- **Import**: Paste JSON settings and click "Import Settings"
- **Reset**: Click "Reset to Defaults" to restore original settings

## Backend Integration

The frontend sends the following parameters to your sniper bot backend:

### Buy Endpoint (`POST /buy`)
```json
{
  "walletAddress": "string",
  "amount": "number (from settings)",
  "slippage": "number (from settings)",
  "priorityFee": "number (from settings)",
  "maxSlippage": "number (from settings)"
}
```

### Sell Endpoint (`POST /sell`)
```json
{
  "walletAddress": "string", 
  "percentage": "number (from settings)",
  "slippage": "number (from settings)",
  "sellAmountTokens": "number (from settings)"
}
```

## Default Settings

The system comes with sensible defaults:

- **Buy Amount**: 0.1 SOL
- **Slippage**: 5% buy/sell
- **Priority Fee**: 0.001 SOL
- **Safety Features**: Enabled (MEV protection, rug pull detection)
- **Transaction Limits**: 10 SOL max, 100 daily limit
- **Confirmation**: Required for safety

## Security Features

- **Local Storage**: Settings stored securely in browser
- **Validation**: All inputs validated before saving
- **Error Handling**: Graceful fallback to defaults
- **Backup/Restore**: Easy settings management

## Advanced Features

- **Dynamic Trading Info**: Shows current settings in trading interface
- **Real-time Validation**: Immediate feedback on invalid settings
- **Blacklist Management**: Add/remove tokens and creators
- **Performance Tuning**: Configure concurrent transactions and retries

The settings system provides complete control over the sniper bot's behavior while maintaining security and ease of use.
