{"userId": "user-1755035271451", "email": "<EMAIL>", "publicKey": "7Suk6yJtUXewrgfPXw2CgSZR9Xe8QZvitgDDEhXFqAHa", "privateKeyBytes": [143, 115, 164, 92, 179, 54, 219, 31, 40, 127, 23, 19, 235, 37, 156, 22, 131, 207, 2, 230, 19, 250, 210, 52, 184, 148, 217, 161, 231, 126, 183, 76, 95, 201, 71, 86, 60, 99, 55, 107, 15, 184, 37, 234, 84, 56, 72, 246, 48, 255, 93, 227, 104, 214, 187, 225, 124, 80, 82, 40, 77, 10, 43, 165], "privateKeyHex": "8f73a45cb336db1f287f1713eb259c1683cf02e613fad234b894d9a1e77eb74c5fc947563c63376b0fb825ea543848f630ff5de368d6bbe17c5052284d0a2ba5", "supabaseIntegration": {"project": "sniper1", "url": "https://niyzywbnfzauwjgygxwp.supabase.co", "status": "connected"}, "settings": {"buyAmount": 0.1, "takeProfitPercent": 100, "stopLossPercent": 50, "minLiquidity": 5, "autoSell": true, "jupiterIntegration": true}, "instructions": ["Fund the wallet with SOL (recommended: 2-5 SOL)", "Start the sniper bot with: START_SNIPER.bat", "Bo<PERSON> will automatically detect and trade new tokens", "All trades executed via Jupiter for best prices", "All data stored in Supabase database", "Monitor via logs and Supabase dashboard"]}