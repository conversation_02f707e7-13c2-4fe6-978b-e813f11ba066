import React, { createContext, useContext, useEffect, useState } from 'react'
import { User as SupabaseUser } from '@supabase/supabase-js'
import { supabase, User, Wallet } from '@/lib/supabase'
import { Keypair } from '@solana/web3.js'
import { toast } from 'sonner'

interface AuthContextType {
  user: SupabaseUser | null
  userProfile: User | null
  userWallets: Wallet[]
  loading: boolean
  signUp: (email: string, password: string) => Promise<void>
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  createWallet: () => Promise<Wallet>
  refreshUserData: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<SupabaseUser | null>(null)
  const [userProfile, setUserProfile] = useState<User | null>(null)
  const [userWallets, setUserWallets] = useState<Wallet[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null)
      if (session?.user) {
        loadUserData(session.user.id)
      } else {
        setLoading(false)
      }
    })

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null)
        
        if (session?.user) {
          await loadUserData(session.user.id)
        } else {
          setUserProfile(null)
          setUserWallets([])
          setLoading(false)
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const loadUserData = async (userId: string) => {
    try {
      // Load user profile
      const { data: profile, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      if (profileError) {
        console.error('Error loading user profile:', profileError)
        return
      }

      setUserProfile(profile)

      // Load user wallets
      const { data: wallets, error: walletsError } = await supabase
        .from('wallets')
        .select('*')
        .eq('user_id', userId)

      if (walletsError) {
        console.error('Error loading wallets:', walletsError)
        return
      }

      setUserWallets(wallets || [])
    } catch (error) {
      console.error('Error loading user data:', error)
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (email: string, password: string) => {
    try {
      setLoading(true)
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      })

      if (error) throw error

      if (data.user) {
        // Create user profile with default settings
        const { error: profileError } = await supabase
          .from('users')
          .insert([
            {
              id: data.user.id,
              email: data.user.email,
              wallets: [],
              settings: {
                trading: {
                  buyAmount: 0.1,
                  slippage: 5,
                  autoSell: true,
                  takeProfitPercent: 100,
                  stopLossPercent: 50
                },
                safety: {
                  minLiquidity: 5,
                  minHolders: 10,
                  maxBuyTax: 10,
                  maxSellTax: 10,
                  honeypotCheck: true,
                  rugPullCheck: true
                },
                notifications: {
                  telegram: false,
                  discord: false,
                  email: false
                }
              }
            }
          ])

        if (profileError) {
          console.error('Error creating user profile:', profileError)
        }

        // Create initial wallet
        await createWallet()
        
        toast.success('Account created successfully! Please check your email to verify your account.')
      }
    } catch (error: any) {
      toast.error(error.message || 'Error creating account')
      throw error
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true)
      
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error
      
      toast.success('Signed in successfully!')
    } catch (error: any) {
      toast.error(error.message || 'Error signing in')
      throw error
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      
      toast.success('Signed out successfully!')
    } catch (error: any) {
      toast.error(error.message || 'Error signing out')
      throw error
    }
  }

  const createWallet = async (): Promise<Wallet> => {
    if (!user) throw new Error('User not authenticated')

    try {
      // Generate new Solana keypair
      const keypair = Keypair.generate()
      const publicKey = keypair.publicKey.toString()
      const privateKeyBytes = Array.from(keypair.secretKey)
      
      // Simple encryption (in production, use proper encryption)
      const encryptedPrivateKey = Buffer.from(keypair.secretKey).toString('hex')

      // Save wallet to database
      const { data: wallet, error } = await supabase
        .from('wallets')
        .insert([
          {
            user_id: user.id,
            public_key: publicKey,
            encrypted_private_key: encryptedPrivateKey
          }
        ])
        .select()
        .single()

      if (error) throw error

      // Update user's wallets array
      const updatedWallets = [...(userProfile?.wallets || []), wallet.id]
      
      const { error: updateError } = await supabase
        .from('users')
        .update({ wallets: updatedWallets })
        .eq('id', user.id)

      if (updateError) {
        console.error('Error updating user wallets:', updateError)
      }

      // Refresh user data
      await refreshUserData()
      
      toast.success('New wallet created successfully!')
      return wallet
    } catch (error: any) {
      toast.error(error.message || 'Error creating wallet')
      throw error
    }
  }

  const refreshUserData = async () => {
    if (user) {
      await loadUserData(user.id)
    }
  }

  const value = {
    user,
    userProfile,
    userWallets,
    loading,
    signUp,
    signIn,
    signOut,
    createWallet,
    refreshUserData
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
