import React, { createContext, useContext, useEffect, useState } from 'react'
import { toast } from 'sonner'

interface TokenData {
  poolAddress: string
  tokenAddress: string
  creator: string
  liquidity: number
  holders: number
  metadata: {
    name: string
    symbol: string
    description: string
    image?: string
  }
  safetyScore: number
  action: 'BUY_EXECUTED' | 'REJECTED'
  timestamp: string
  checks: string[]
}

interface SniperEvent {
  type: 'TOKEN_DETECTED' | 'TOKEN_REJECTED' | 'CONNECTED' | 'HEARTBEAT'
  data?: TokenData
  message?: string
  timestamp?: string
  recentTokens?: TokenData[]
}

interface SniperStatus {
  isRunning: boolean
  poolsDetected: number
  recentTokens: number
  config: {
    raydiumV4: string
    minLiquidity: number
    buyAmount: number
    takeProfitPercent: number
    stopLossPercent: number
    jupiterEnabled: boolean
  }
  performance: {
    uptime: number
    memoryUsage: any
  }
}

interface SniperContextType {
  isConnected: boolean
  detectedTokens: TokenData[]
  sniperStatus: SniperStatus | null
  connectionError: string | null
  reconnect: () => void
}

const SniperContext = createContext<SniperContextType | undefined>(undefined)

export function useSniper() {
  const context = useContext(SniperContext)
  if (context === undefined) {
    throw new Error('useSniper must be used within a SniperProvider')
  }
  return context
}

export function SniperProvider({ children }: { children: React.ReactNode }) {
  const [isConnected, setIsConnected] = useState(false)
  const [detectedTokens, setDetectedTokens] = useState<TokenData[]>([])
  const [sniperStatus, setSniperStatus] = useState<SniperStatus | null>(null)
  const [connectionError, setConnectionError] = useState<string | null>(null)
  const [eventSource, setEventSource] = useState<EventSource | null>(null)

  const connectToSniper = () => {
    try {
      console.log('🔗 Connecting to sniper bot...')
      
      // Close existing connection
      if (eventSource) {
        eventSource.close()
      }

      const es = new EventSource('http://localhost:3001/stream')
      
      es.onopen = () => {
        console.log('✅ Connected to sniper stream')
        setIsConnected(true)
        setConnectionError(null)
        toast.success('Connected to sniper bot!')
      }

      es.onmessage = (event) => {
        try {
          const sniperEvent: SniperEvent = JSON.parse(event.data)
          handleSniperEvent(sniperEvent)
        } catch (error) {
          console.error('Failed to parse sniper event:', error)
        }
      }

      es.onerror = (error) => {
        console.error('❌ Sniper stream error:', error)
        setIsConnected(false)
        setConnectionError('Connection lost to sniper bot')
        
        // Auto-reconnect after 5 seconds
        setTimeout(() => {
          console.log('🔄 Attempting to reconnect...')
          connectToSniper()
        }, 5000)
      }

      setEventSource(es)
    } catch (error) {
      console.error('Failed to connect to sniper:', error)
      setConnectionError('Failed to connect to sniper bot')
    }
  }

  const handleSniperEvent = (event: SniperEvent) => {
    switch (event.type) {
      case 'CONNECTED':
        console.log('✅ Sniper stream connected')
        if (event.recentTokens) {
          setDetectedTokens(event.recentTokens)
        }
        break
        
      case 'TOKEN_DETECTED':
        if (event.data) {
          console.log('🔍 New token detected:', event.data)
          setDetectedTokens(prev => [
            event.data!,
            ...prev.slice(0, 49) // Keep last 50 tokens
          ])
          
          toast.success(
            `🎯 Token Bought: ${event.data.metadata.name} (${event.data.metadata.symbol})`,
            {
              description: `Safety Score: ${event.data.safetyScore}/100 | Liquidity: ${event.data.liquidity.toFixed(2)} SOL`
            }
          )
        }
        break
        
      case 'TOKEN_REJECTED':
        if (event.data) {
          console.log('❌ Token rejected:', event.data)
          setDetectedTokens(prev => [
            { ...event.data!, action: 'REJECTED' },
            ...prev.slice(0, 49)
          ])
          
          toast.error(
            `❌ Token Rejected: ${event.data.metadata.name} (${event.data.metadata.symbol})`,
            {
              description: `Safety Score: ${event.data.safetyScore}/100 (minimum: 70)`
            }
          )
        }
        break
        
      case 'HEARTBEAT':
        // Keep connection alive
        break
    }
  }

  const fetchSniperStatus = async () => {
    try {
      const response = await fetch('http://localhost:3001/status')
      const data = await response.json()
      setSniperStatus(data.data)
    } catch (error) {
      console.error('Failed to fetch sniper status:', error)
    }
  }

  const reconnect = () => {
    connectToSniper()
  }

  useEffect(() => {
    // Connect to sniper stream
    connectToSniper()
    
    // Fetch initial status
    fetchSniperStatus()
    
    // Fetch status every 30 seconds
    const statusInterval = setInterval(fetchSniperStatus, 30000)

    return () => {
      if (eventSource) {
        eventSource.close()
      }
      clearInterval(statusInterval)
    }
  }, [])

  const value = {
    isConnected,
    detectedTokens,
    sniperStatus,
    connectionError,
    reconnect
  }

  return (
    <SniperContext.Provider value={value}>
      {children}
    </SniperContext.Provider>
  )
}
