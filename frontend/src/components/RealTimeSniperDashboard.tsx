import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { useSniper } from '@/contexts/SniperContext';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { LogOut, Wallet, Plus, Settings, TrendingUp, Shield, Zap } from 'lucide-react';

interface TokenData {
  poolAddress: string;
  tokenAddress: string;
  creator: string;
  liquidity: number;
  holders: number;
  metadata: {
    name: string;
    symbol: string;
    description: string;
    image?: string;
  };
  safetyScore: number;
  action: 'BUY_EXECUTED' | 'REJECTED';
  timestamp: string;
}

interface SniperEvent {
  type: 'TOKEN_DETECTED' | 'TOKEN_REJECTED' | 'POSITION_UPDATE' | 'CONNECTED' | 'HEARTBEAT';
  data?: TokenData;
  message?: string;
  timestamp?: string;
}

export default function RealTimeSniperDashboard() {
  const { user, userProfile, userWallets, signOut, createWallet } = useAuth();
  const { isConnected, detectedTokens, sniperStatus, connectionError, reconnect } = useSniper();

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-6)}`;
  };

  const getSafetyBadgeColor = (score: number) => {
    if (score >= 90) return 'bg-green-500';
    if (score >= 70) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const handleCreateWallet = async () => {
    try {
      await createWallet();
    } catch (error) {
      console.error('Failed to create wallet:', error);
    }
  };

  const toggleSniperStatus = async () => {
    if (!userProfile) return;

    try {
      const newStatus = !userProfile.settings.trading.enabled;

      // Update user settings in Supabase
      const { error } = await supabase
        .from('users')
        .update({
          settings: {
            ...userProfile.settings,
            trading: {
              ...userProfile.settings.trading,
              enabled: newStatus
            }
          }
        })
        .eq('id', user?.id);

      if (error) throw error;

      // Refresh user data
      await refreshUserData();

      // Show notification
      if (newStatus) {
        toast.success('🚀 Automated sniper activated! Bot will now trade automatically.');
      } else {
        toast.success('🛑 Automated sniper stopped. No more automatic trades.');
      }

    } catch (error: any) {
      toast.error(error.message || 'Failed to update sniper status');
    }
  };



  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold mb-2">🎯 Sol Bullet - Real-Time Sniper</h1>
              <div className="flex items-center gap-4">
                <Badge variant={isConnected ? "default" : "destructive"}>
                  {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
                </Badge>
                {connectionError && (
                  <Button onClick={reconnect} size="sm" variant="outline">
                    🔄 Reconnect
                  </Button>
                )}
                {sniperStatus && (
                  <>
                    <Badge variant="outline">
                      📊 Pools: {sniperStatus.poolsDetected}
                    </Badge>
                    <Badge variant="outline">
                      💰 Tokens: {sniperStatus.recentTokens}
                    </Badge>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="text-right">
                <p className="text-sm text-gray-400">Welcome back</p>
                <p className="font-semibold">{user?.email}</p>
              </div>
              <Button onClick={signOut} variant="outline" size="sm">
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>

        {/* User Wallets */}
        <Card className="bg-gray-800 border-gray-700 mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wallet className="h-5 w-5" />
              Your Trading Wallets
              <Badge variant="outline" className="ml-auto">
                {userWallets.length} wallet{userWallets.length !== 1 ? 's' : ''}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {userWallets.length === 0 ? (
              <div className="text-center py-8">
                <Wallet className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400 mb-4">No wallets found. Create your first wallet to start trading.</p>
                <Button onClick={handleCreateWallet}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Wallet
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {userWallets.map((wallet, index) => (
                  <div key={wallet.id} className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                    <div>
                      <p className="font-semibold">Wallet {index + 1}</p>
                      <p className="text-sm text-gray-400 font-mono">{formatAddress(wallet.public_key)}</p>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        📊 View Balance
                      </Button>
                      <Button size="sm" variant="outline">
                        📋 Copy Address
                      </Button>
                    </div>
                  </div>
                ))}
                <Button onClick={handleCreateWallet} variant="outline" className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Wallet
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Sniper Control Panel */}
        <Card className="bg-gray-800 border-gray-700 mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Automated Sniper Control
              <Badge variant={userProfile?.settings?.trading?.enabled ? "default" : "outline"} className="ml-auto">
                {userProfile?.settings?.trading?.enabled ? '🟢 ACTIVE' : '🔴 STOPPED'}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Sniper Controls */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Automated Trading</span>
                  <Button
                    onClick={() => toggleSniperStatus()}
                    variant={userProfile?.settings?.trading?.enabled ? "destructive" : "default"}
                    size="sm"
                  >
                    {userProfile?.settings?.trading?.enabled ? (
                      <>🛑 Stop Sniper</>
                    ) : (
                      <>🚀 Start Sniper</>
                    )}
                  </Button>
                </div>

                <div className="text-xs text-gray-400">
                  {userProfile?.settings?.trading?.enabled ? (
                    '🤖 Bot is actively monitoring and trading tokens automatically'
                  ) : (
                    '⏸️ Bot is stopped - no automatic trading will occur'
                  )}
                </div>
              </div>

              {/* Trading Settings Display */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Buy Amount:</span>
                  <div className="font-bold text-blue-400">
                    {userProfile?.settings?.trading?.buyAmount || 0.1} SOL
                  </div>
                </div>
                <div>
                  <span className="text-gray-400">Take Profit:</span>
                  <div className="font-bold text-green-400">
                    +{userProfile?.settings?.trading?.takeProfitPercent || 100}%
                  </div>
                </div>
                <div>
                  <span className="text-gray-400">Stop Loss:</span>
                  <div className="font-bold text-red-400">
                    -{userProfile?.settings?.trading?.stopLossPercent || 50}%
                  </div>
                </div>
                <div>
                  <span className="text-gray-400">Min Liquidity:</span>
                  <div className="font-bold text-yellow-400">
                    {userProfile?.settings?.safety?.minLiquidity || 5} SOL
                  </div>
                </div>
              </div>
            </div>

            {/* Status Cards */}
            {sniperStatus && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                <div className="bg-gray-700 p-3 rounded-lg text-center">
                  <div className="text-xs text-gray-400">Detection</div>
                  <div className="text-sm font-bold text-green-400">
                    {sniperStatus.isRunning ? '🟢 Active' : '🔴 Stopped'}
                  </div>
                </div>
                <div className="bg-gray-700 p-3 rounded-lg text-center">
                  <div className="text-xs text-gray-400">Pools Scanned</div>
                  <div className="text-sm font-bold text-blue-400">
                    {sniperStatus.poolsDetected || 0}
                  </div>
                </div>
                <div className="bg-gray-700 p-3 rounded-lg text-center">
                  <div className="text-xs text-gray-400">Tokens Found</div>
                  <div className="text-sm font-bold text-purple-400">
                    {sniperStatus.recentTokens || 0}
                  </div>
                </div>
                <div className="bg-gray-700 p-3 rounded-lg text-center">
                  <div className="text-xs text-gray-400">Uptime</div>
                  <div className="text-sm font-bold text-yellow-400">
                    {Math.floor((sniperStatus.performance?.uptime || 0) / 60)}m
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Real-Time Token Feed */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🔍 Real-Time Token Detection
              <Badge variant="outline" className="ml-auto">
                {detectedTokens.length} tokens detected
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {detectedTokens.length === 0 ? (
              <Alert>
                <AlertDescription>
                  🔍 Monitoring Raydium pools for new tokens... Data will appear here in real-time.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {detectedTokens.map((token, index) => (
                  <div
                    key={`${token.poolAddress}-${index}`}
                    className={`p-4 rounded-lg border ${
                      token.action === 'BUY_EXECUTED' 
                        ? 'bg-green-900/20 border-green-500/30' 
                        : 'bg-red-900/20 border-red-500/30'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-bold text-lg">
                          {token.metadata.name} ({token.metadata.symbol})
                        </h3>
                        <p className="text-sm text-gray-400">
                          {new Date(token.timestamp).toLocaleTimeString()}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Badge 
                          className={`${getSafetyBadgeColor(token.safetyScore)} text-white`}
                        >
                          {token.safetyScore}/100
                        </Badge>
                        <Badge variant={token.action === 'BUY_EXECUTED' ? 'default' : 'destructive'}>
                          {token.action === 'BUY_EXECUTED' ? '✅ BOUGHT' : '❌ REJECTED'}
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400">Pool:</span>
                        <br />
                        <code className="text-blue-400">{formatAddress(token.poolAddress)}</code>
                      </div>
                      <div>
                        <span className="text-gray-400">Token:</span>
                        <br />
                        <code className="text-green-400">{formatAddress(token.tokenAddress)}</code>
                      </div>
                      <div>
                        <span className="text-gray-400">Creator:</span>
                        <br />
                        <code className="text-purple-400">{formatAddress(token.creator)}</code>
                      </div>
                      <div>
                        <span className="text-gray-400">Liquidity:</span>
                        <br />
                        <span className="text-yellow-400">{token.liquidity} SOL</span>
                      </div>
                    </div>

                    <div className="mt-3 flex gap-4 text-sm">
                      <span>👥 {token.holders} holders</span>
                      {token.metadata.description && (
                        <span className="text-gray-400">📝 {token.metadata.description}</span>
                      )}
                    </div>

                    <div className="mt-3 flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(`https://solscan.io/account/${token.tokenAddress}`, '_blank')}
                      >
                        📊 Solscan
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(`https://dexscreener.com/solana/${token.poolAddress}`, '_blank')}
                      >
                        📈 DEX Screener
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => navigator.clipboard.writeText(token.tokenAddress)}
                      >
                        📋 Copy Address
                      </Button>
                    </div>

                    {/* Automated Action Status */}
                    <div className="mt-3 p-2 bg-gray-700 rounded text-xs">
                      {token.action === 'BUY_EXECUTED' ? (
                        <div className="text-green-400">
                          🤖 <strong>AUTOMATED BUY EXECUTED</strong> - Bot purchased this token automatically based on safety analysis
                        </div>
                      ) : (
                        <div className="text-red-400">
                          🤖 <strong>AUTOMATED REJECTION</strong> - Bot rejected this token due to safety concerns
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-8 text-center text-gray-400">
          <p>🎯 Sol Bullet Automated Sniper - Real-time Raydium V4 monitoring</p>
          <p>🔍 Detecting actual tokens • 🛡️ Running safety checks • 💰 Auto-trading via Jupiter</p>
        </div>
      </div>
    </div>
  );
}
