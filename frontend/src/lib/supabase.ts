import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://niyzywbnfzauwjgygxwp.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5peXp5d2JuZnphdXdqZ3lneHdwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQwNDU2NzEsImV4cCI6MjA0OTYyMTY3MX0.4kKNYFTyLGkbT8VQjKqGhkqGhkqGhkqGhkqGhkqGhkq'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface User {
  id: string
  email: string
  wallets: string[]
  settings: {
    trading: {
      buyAmount: number
      slippage: number
      autoSell: boolean
      takeProfitPercent: number
      stopLossPercent: number
    }
    safety: {
      minLiquidity: number
      minHolders: number
      maxBuyTax: number
      maxSellTax: number
      honeypotCheck: boolean
      rugPullCheck: boolean
    }
    notifications: {
      telegram: boolean
      discord: boolean
      email: boolean
    }
  }
  created_at: string
  updated_at: string
}

export interface Wallet {
  id: string
  user_id: string
  public_key: string
  encrypted_private_key: string
  created_at: string
  updated_at: string
}

export interface Transaction {
  id: string
  user_id: string
  wallet_address: string
  type: 'buy' | 'sell'
  token_address: string
  amount: number
  price: number
  slippage: number
  signature: string
  status: 'pending' | 'success' | 'failed'
  error?: string
  created_at: string
}

export interface Position {
  id: string
  user_id: string
  wallet_address: string
  token_address: string
  token_name: string
  token_symbol: string
  amount: number
  entry_price: number
  current_price: number
  pnl: number
  pnl_percent: number
  status: 'active' | 'closed'
  created_at: string
  updated_at: string
}
