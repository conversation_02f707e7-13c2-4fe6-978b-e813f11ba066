import React from 'react'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import { SniperProvider } from './contexts/SniperContext'
import AuthPage from './components/auth/AuthPage'
import RealTimeSniperDashboard from './components/RealTimeSniperDashboard'
import { Toaster } from 'sonner'
import { Loader2 } from 'lucide-react'

function AppContent() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-4" />
          <p className="text-white">Loading Sol Bullet...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return <AuthPage />
  }

  return (
    <SniperProvider>
      <RealTimeSniperDashboard />
    </SniperProvider>
  )
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
      <Toaster 
        theme="dark" 
        position="top-right"
        toastOptions={{
          style: {
            background: '#1f2937',
            border: '1px solid #374151',
            color: '#f9fafb',
          },
        }}
      />
    </AuthProvider>
  )
}

export default App
