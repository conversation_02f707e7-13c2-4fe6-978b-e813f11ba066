import React, { useState } from 'react'

// Simple test component to verify the frontend is working
export default function TestApp() {
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault()
    if (email && password) {
      setIsLoggedIn(true)
    }
  }

  const handleLogout = () => {
    setIsLoggedIn(false)
    setEmail('')
    setPassword('')
  }

  if (isLoggedIn) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold mb-2">🎯 Sol Bullet - Automated Sniper</h1>
                <div className="flex items-center gap-4">
                  <span className="px-3 py-1 bg-green-500 text-white rounded-full text-sm">
                    🟢 Connected
                  </span>
                  <span className="px-3 py-1 bg-gray-700 text-white rounded-full text-sm">
                    📊 Pools: 1,234
                  </span>
                  <span className="px-3 py-1 bg-gray-700 text-white rounded-full text-sm">
                    💰 Tokens: 56
                  </span>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="text-right">
                  <p className="text-sm text-gray-400">Welcome back</p>
                  <p className="font-semibold">{email}</p>
                </div>
                <button 
                  onClick={handleLogout}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-white"
                >
                  Sign Out
                </button>
              </div>
            </div>
          </div>

          {/* Sniper Control Panel */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-8">
            <div className="flex items-center gap-2 mb-4">
              <span className="text-xl">⚡</span>
              <h2 className="text-xl font-bold">Automated Sniper Control</h2>
              <span className="ml-auto px-3 py-1 bg-green-500 text-white rounded-full text-sm">
                🟢 ACTIVE
              </span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Sniper Controls */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Automated Trading</span>
                  <button className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white text-sm">
                    🛑 Stop Sniper
                  </button>
                </div>
                
                <div className="text-xs text-gray-400">
                  🤖 Bot is actively monitoring and trading tokens automatically
                </div>
              </div>

              {/* Trading Settings Display */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Buy Amount:</span>
                  <div className="font-bold text-blue-400">0.1 SOL</div>
                </div>
                <div>
                  <span className="text-gray-400">Take Profit:</span>
                  <div className="font-bold text-green-400">+100%</div>
                </div>
                <div>
                  <span className="text-gray-400">Stop Loss:</span>
                  <div className="font-bold text-red-400">-50%</div>
                </div>
                <div>
                  <span className="text-gray-400">Min Liquidity:</span>
                  <div className="font-bold text-yellow-400">5 SOL</div>
                </div>
              </div>
            </div>
          </div>

          {/* Real-Time Token Feed */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold flex items-center gap-2">
                🔍 Real-Time Token Detection
              </h2>
              <span className="px-3 py-1 bg-gray-700 text-white rounded-full text-sm">
                3 tokens detected
              </span>
            </div>
            
            <div className="space-y-4">
              {/* Sample Token 1 - BOUGHT */}
              <div className="p-4 rounded-lg border bg-green-900/20 border-green-500/30">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="font-bold text-lg">RealToken123 (RT1)</h3>
                    <p className="text-sm text-gray-400">2 minutes ago</p>
                  </div>
                  <div className="flex gap-2">
                    <span className="px-2 py-1 bg-green-500 text-white rounded text-xs">100/100</span>
                    <span className="px-2 py-1 bg-green-600 text-white rounded text-xs">✅ BOUGHT</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                  <div>
                    <span className="text-gray-400">Pool:</span><br />
                    <code className="text-blue-400">VnJoHz...D5W</code>
                  </div>
                  <div>
                    <span className="text-gray-400">Token:</span><br />
                    <code className="text-green-400">Wv8Gp2...ykia</code>
                  </div>
                  <div>
                    <span className="text-gray-400">Creator:</span><br />
                    <code className="text-purple-400">DBPp76...ftS</code>
                  </div>
                  <div>
                    <span className="text-gray-400">Liquidity:</span><br />
                    <span className="text-yellow-400">24.48 SOL</span>
                  </div>
                </div>

                <div className="mb-3 flex gap-4 text-sm">
                  <span>👥 90 holders</span>
                  <span className="text-gray-400">📝 Newly detected token on Raydium</span>
                </div>

                <div className="p-2 bg-gray-700 rounded text-xs">
                  <div className="text-green-400">
                    🤖 <strong>AUTOMATED BUY EXECUTED</strong> - Bot purchased this token automatically based on safety analysis
                  </div>
                </div>
              </div>

              {/* Sample Token 2 - REJECTED */}
              <div className="p-4 rounded-lg border bg-red-900/20 border-red-500/30">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="font-bold text-lg">BadToken456 (BT4)</h3>
                    <p className="text-sm text-gray-400">5 minutes ago</p>
                  </div>
                  <div className="flex gap-2">
                    <span className="px-2 py-1 bg-red-500 text-white rounded text-xs">45/100</span>
                    <span className="px-2 py-1 bg-red-600 text-white rounded text-xs">❌ REJECTED</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                  <div>
                    <span className="text-gray-400">Pool:</span><br />
                    <code className="text-blue-400">AbCdEf...XyZ</code>
                  </div>
                  <div>
                    <span className="text-gray-400">Token:</span><br />
                    <code className="text-green-400">123456...789</code>
                  </div>
                  <div>
                    <span className="text-gray-400">Creator:</span><br />
                    <code className="text-purple-400">Unknown</code>
                  </div>
                  <div>
                    <span className="text-gray-400">Liquidity:</span><br />
                    <span className="text-yellow-400">2.1 SOL</span>
                  </div>
                </div>

                <div className="mb-3 flex gap-4 text-sm">
                  <span>👥 3 holders</span>
                  <span className="text-gray-400">🚫 Failed safety checks</span>
                </div>

                <div className="p-2 bg-gray-700 rounded text-xs">
                  <div className="text-red-400">
                    🤖 <strong>AUTOMATED REJECTION</strong> - Bot rejected this token due to safety concerns
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-8 text-center text-gray-400">
            <p>🎯 Sol Bullet Automated Sniper - No manual trading required</p>
            <p>🤖 Bot handles everything automatically • 🔍 Real-time detection • 💰 Auto-trading via Jupiter</p>
          </div>
        </div>
      </div>
    )
  }

  // Login Page
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-full">
              <span className="text-2xl">⚡</span>
            </div>
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">Sol Bullet</h1>
          <p className="text-gray-300">Automated Solana Sniper Bot</p>
        </div>

        <div className="bg-gray-800/50 border border-gray-700 backdrop-blur-sm rounded-lg p-6">
          <div className="text-center mb-6">
            <h2 className="text-xl font-bold text-white">Welcome Back</h2>
            <p className="text-gray-400">Sign in to access your automated trading dashboard</p>
          </div>
          
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <label className="text-white text-sm font-medium">Email</label>
              <div className="relative">
                <span className="absolute left-3 top-3 text-gray-400">📧</span>
                <input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-white text-sm font-medium">Password</label>
              <div className="relative">
                <span className="absolute left-3 top-3 text-gray-400">🔒</span>
                <input
                  type="password"
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>
            
            <button 
              type="submit" 
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium py-3 rounded-lg transition-all"
            >
              Sign In
            </button>
          </form>

          <div className="mt-6 text-center text-sm text-gray-400">
            <p>🎯 Automated Token Detection • 🛡️ Advanced Safety Checks</p>
            <p>⚡ Jupiter Integration • 🔄 24/7 Automated Trading</p>
          </div>
        </div>
      </div>
    </div>
  )
}
