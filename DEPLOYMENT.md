# 🚀 Deployment Guide for Sol Bullet

## Vercel Deployment (Recommended)

### Prerequisites
- GitHub account
- Vercel account (free tier available)
- Helius API key for Solana RPC

### Steps

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial Sol Bullet frontend"
   git push origin main
   ```

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Select the `sol-bullet` folder as the root directory

3. **Configure Environment Variables**
   In Vercel dashboard, add these environment variables:
   ```
   NEXT_PUBLIC_HELIUS_RPC=https://mainnet.helius-rpc.com/?api-key=YOUR_API_KEY
   NEXT_PUBLIC_API_URL=https://your-backend-api.com
   ```

4. **Deploy**
   - Click "Deploy"
   - Your app will be live at `https://your-project.vercel.app`

## Manual Deployment

### Build for Production
```bash
npm run build
npm start
```

### Using PM2 (for VPS)
```bash
npm install -g pm2
npm run build
pm2 start npm --name "sol-bullet" -- start
```

## Environment Configuration

### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `NEXT_PUBLIC_HELIUS_RPC` | Helius RPC endpoint | `https://mainnet.helius-rpc.com/?api-key=abc123` |
| `NEXT_PUBLIC_API_URL` | Backend API URL | `https://api.yourbot.com` |

### Getting a Helius API Key

1. Visit [helius.xyz](https://helius.xyz/)
2. Sign up for a free account
3. Create a new project
4. Copy your API key
5. Use it in the RPC URL format above

## Backend Integration

Your sniper bot backend should expose these endpoints:

### POST /buy
```json
{
  "walletAddress": "string",
  "tokenAddress": "string (optional)",
  "amount": "number (optional, default: 0.1)",
  "slippage": "number (optional, default: 5)"
}
```

### POST /sell
```json
{
  "walletAddress": "string",
  "tokenAddress": "string (optional)",
  "percentage": "number (optional, default: 100)"
}
```

### Response Format
```json
{
  "success": boolean,
  "message": "string",
  "data": {} // optional
}
```

## Security Considerations

- Private keys never leave the browser
- Use HTTPS in production
- Validate all inputs on the backend
- Implement rate limiting on your API
- Consider implementing user authentication for production use

## Troubleshooting

### Common Issues

1. **RPC Connection Failed**
   - Check your Helius API key
   - Verify the RPC URL format
   - Ensure you have sufficient API credits

2. **Backend Connection Failed**
   - Verify your backend is running
   - Check the API URL in environment variables
   - Ensure CORS is configured on your backend

3. **Wallet Not Loading**
   - Clear browser localStorage
   - Check browser console for errors
   - Ensure JavaScript is enabled

### Development Tips

- Use browser dev tools to inspect localStorage
- Check Network tab for API call failures
- Monitor console for error messages
- Test with small amounts first
