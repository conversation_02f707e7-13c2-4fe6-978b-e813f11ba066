# 🔧 Quick Setup Guide

## 1. Get Your Helius API Key (Recommended)

1. **Visit He<PERSON>**: Go to [helius.xyz](https://helius.xyz/)
2. **Sign Up**: Create a free account
3. **Create Project**: Click "Create New Project"
4. **Copy API Key**: Copy your API key from the dashboard

## 2. Configure Environment Variables

1. **Open `.env.local`** in your project root
2. **Replace the placeholder**:
   ```env
   # Change this line:
   NEXT_PUBLIC_HELIUS_RPC=https://mainnet.helius-rpc.com/?api-key=YOUR_API_KEY_HERE
   
   # To this (with your actual API key):
   NEXT_PUBLIC_HELIUS_RPC=https://mainnet.helius-rpc.com/?api-key=abc123def456
   ```

## 3. Start the Development Server

```bash
npm run dev
```

## 4. Test the Application

1. **Open**: [http://localhost:3000](http://localhost:3000)
2. **Check**: Wallet should generate automatically
3. **Verify**: SOL balance should load (may be 0.0000 for new wallets)

## 🔄 Fallback Mode

If you don't configure Helius, the app will automatically use public RPC endpoints:
- ✅ Still works for testing
- ⚠️ May be slower and less reliable
- ⚠️ May have rate limits

## 🎯 Backend Integration

Make sure your sniper bot backend has these endpoints:

### POST /buy
```json
{
  "walletAddress": "B5QFGFvFoBQCbN9xRqrgAswz2MzxoXvpDupVqxKkKcDn",
  "amount": 0.1,
  "slippage": 5
}
```

### POST /sell  
```json
{
  "walletAddress": "B5QFGFvFoBQCbN9xRqrgAswz2MzxoXvpDupVqxKkKcDn",
  "percentage": 100
}
```

## 🚨 Troubleshooting

### "Invalid API Key" Error
- Check your Helius API key is correct
- Ensure no extra spaces in the .env.local file
- Restart the dev server after changing .env.local

### Balance Shows 0.0000
- This is normal for new wallets
- Send some SOL to test (use devnet for testing)
- Check the RPC status indicator in the UI

### Backend Connection Failed
- Verify your backend is running
- Check the API URL in .env.local
- Ensure CORS is configured on your backend

## 💡 Pro Tips

- Use devnet for testing: `https://api.devnet.solana.com`
- Monitor browser console for detailed error messages
- Test with small amounts first
- Keep your private keys secure
