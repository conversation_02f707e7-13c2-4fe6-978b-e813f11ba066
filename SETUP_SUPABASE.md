# 🗄️ SUPABASE DATABASE SETUP

## 🚀 **CRITICAL: SET UP YOUR DATABASE FIRST**

Before you can use the multi-user sniper bot, you MUST set up the Supabase database schema.

---

## 📋 **STEP-BY-STEP SETUP:**

### **Step 1: Go to Supabase Dashboard**
1. Open your browser and go to: https://supabase.com/dashboard
2. Sign in to your account
3. Select your **"sniper1"** project

### **Step 2: Open SQL Editor**
1. In the left sidebar, click **"SQL Editor"**
2. Click **"New Query"**

### **Step 3: Run Database Schema**
1. Copy the entire contents of: `backend/setup-supabase-schema.sql`
2. Paste it into the SQL Editor
3. Click **"Run"** button

### **Step 4: Verify Tables Created**
1. In the left sidebar, click **"Table Editor"**
2. You should see these tables:
   - ✅ `users`
   - ✅ `wallets` 
   - ✅ `transactions`
   - ✅ `positions`
   - ✅ `price_alerts`
   - ✅ `token_metadata`
   - ✅ `liquidity_pools`

### **Step 5: Get Your Anon Key**
1. In the left sidebar, click **"Settings"**
2. Click **"API"**
3. Copy the **"anon public"** key
4. Update `frontend/src/lib/supabase.ts` with your real key

---

## 🔐 **AUTHENTICATION SETUP:**

### **Enable Email Authentication:**
1. Go to **"Authentication"** in the sidebar
2. Click **"Settings"**
3. Make sure **"Enable email confirmations"** is turned ON
4. Set **"Site URL"** to: `http://localhost:5173`

### **Configure Email Templates (Optional):**
1. Go to **"Authentication"** → **"Email Templates"**
2. Customize the signup confirmation email
3. Customize the password reset email

---

## 🛡️ **SECURITY FEATURES:**

### **Row Level Security (RLS):**
✅ **Enabled** - Users can only see their own data
✅ **Policies Created** - Automatic data isolation
✅ **Secure** - No user can access another user's wallets

### **Data Isolation:**
- Each user has their own wallets
- Each user has their own transaction history
- Each user has their own trading settings
- Shared data: token detection (everyone sees same opportunities)

---

## 📊 **WHAT THE TABLES DO:**

### **`users` Table:**
- Stores user profiles and settings
- Links to Supabase auth.users
- Contains trading preferences
- Stores safety settings

### **`wallets` Table:**
- Stores encrypted private keys
- Links wallets to users
- Supports multiple wallets per user
- Cross-device access

### **`transactions` Table:**
- Records all buy/sell transactions
- Links to users and wallets
- Tracks success/failure status
- Complete audit trail

### **`positions` Table:**
- Tracks active token positions
- Calculates P&L in real-time
- Links buy and sell transactions
- Position management

### **`price_alerts` Table:**
- Manages take profit alerts
- Manages stop loss alerts
- Triggers automated selling
- User-configurable targets

### **`token_metadata` Table:**
- Caches token information
- Shared across all users
- Improves performance
- Reduces API calls

### **`liquidity_pools` Table:**
- Stores pool information
- Tracks liquidity changes
- Shared market data
- Real-time updates

---

## 🔧 **CONFIGURATION REQUIRED:**

### **Update Frontend Config:**
```typescript
// frontend/src/lib/supabase.ts
const supabaseUrl = 'https://niyzywbnfzauwjgygxwp.supabase.co'
const supabaseAnonKey = 'YOUR_REAL_ANON_KEY_HERE' // ← UPDATE THIS!
```

### **Update Backend Config:**
```javascript
// backend/.env
SUPABASE_URL=https://niyzywbnfzauwjgygxwp.supabase.co
SUPABASE_SERVICE_KEY=YOUR_SERVICE_KEY_HERE
SUPABASE_ANON_KEY=YOUR_ANON_KEY_HERE
```

---

## ✅ **VERIFICATION CHECKLIST:**

Before starting the sniper bot, verify:

- [ ] ✅ Supabase project "sniper1" exists
- [ ] ✅ All 7 tables created successfully
- [ ] ✅ Row Level Security enabled
- [ ] ✅ Email authentication configured
- [ ] ✅ Frontend has correct anon key
- [ ] ✅ Backend has correct service key
- [ ] ✅ Site URL set to localhost:5173

---

## 🚨 **IMPORTANT NOTES:**

### **Security:**
- **NEVER** share your service key publicly
- **ALWAYS** use the anon key in frontend
- **ENABLE** Row Level Security for user data
- **TEST** that users can't see each other's data

### **Multi-User:**
- Each user gets isolated data automatically
- Shared token detection across all users
- Individual wallet management per user
- Personal trading settings per user

### **Production:**
- Update Site URL to your production domain
- Configure proper email templates
- Set up custom SMTP (optional)
- Enable additional security features

---

## 🎯 **AFTER SETUP:**

Once your database is set up:

1. **✅ Database Ready** - All tables created
2. **🚀 Start Backend** - Run `START_SNIPER_BOT.bat`
3. **🌐 Start Frontend** - Run `START_FRONTEND.bat`
4. **📱 Register Users** - Go to `http://localhost:5173`
5. **🤖 Start Trading** - Enable automated sniper per user

## 🎯 **YOUR MULTI-USER DATABASE IS READY!**

With this setup, you have a **production-ready multi-user system** with complete data isolation and security! 🚀
