const { Keypair } = require('@solana/web3.js');
const crypto = require('crypto');

console.log('🎯 Sol Bullet - Creating New Automated Sniper User\n');

// Generate a new wallet
const keypair = Keypair.generate();
const publicKey = keypair.publicKey.toString();
const privateKeyBytes = Array.from(keypair.secretKey);
const privateKeyHex = Buffer.from(keypair.secretKey).toString('hex');

// Generate unique user ID and email
const timestamp = Date.now();
const userId = `user-${timestamp}`;
const userEmail = `sniper-${timestamp}@solbullet.com`;

console.log('✅ NEW AUTOMATED SNIPER USER CREATED!');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log(`👤 User ID: ${userId}`);
console.log(`📧 Email: ${userEmail}`);
console.log(`💰 Wallet Address: ${publicKey}`);
console.log(`🔑 Private Key (bytes): [${privateKeyBytes.join(', ')}]`);
console.log(`🔑 Private Key (hex): ${privateKeyHex}`);
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

console.log('\n🎯 AUTOMATED SNIPER CONFIGURATION:');
console.log('🗄️ Database: Connected to Supabase "sniper1" project');
console.log('💰 Buy Amount: 0.1 SOL per token');
console.log('📈 Take Profit: 100% (sell when doubled)');
console.log('📉 Stop Loss: 50% (sell if down 50%)');
console.log('🔍 Min Liquidity: 5 SOL required');
console.log('⚡ All trades via Jupiter for best prices');
console.log('🔄 Fully automated 24/7 operation');

console.log('\n📋 NEXT STEPS:');
console.log('1. 📤 Fund the wallet with SOL for automated trading');
console.log('2. 🚀 Start the bot: Double-click START_SNIPER.bat');
console.log('3. 👀 Watch logs: Bot shows all automated actions');
console.log('4. 💰 Profit: Bot trades automatically via Jupiter!');

// Save to file
const userInfo = {
  userId: userId,
  email: userEmail,
  publicKey: publicKey,
  privateKeyBytes: privateKeyBytes,
  privateKeyHex: privateKeyHex,
  supabaseIntegration: {
    project: 'sniper1',
    url: 'https://niyzywbnfzauwjgygxwp.supabase.co',
    status: 'connected'
  },
  settings: {
    buyAmount: 0.1,
    takeProfitPercent: 100,
    stopLossPercent: 50,
    minLiquidity: 5,
    autoSell: true,
    jupiterIntegration: true
  },
  instructions: [
    'Fund the wallet with SOL (recommended: 2-5 SOL)',
    'Start the sniper bot with: START_SNIPER.bat',
    'Bot will automatically detect and trade new tokens',
    'All trades executed via Jupiter for best prices',
    'All data stored in Supabase database',
    'Monitor via logs and Supabase dashboard'
  ],
};

const fs = require('fs');
fs.writeFileSync('new-sniper-user.json', JSON.stringify(userInfo, null, 2));

console.log('\n💾 User info saved to: new-sniper-user.json');
console.log('\n🚀 YOUR AUTOMATED SNIPER IS READY!');
console.log('\n🎯 TO START AUTOMATED TRADING:');
console.log('   1. 📤 Fund wallet: Send SOL to the address above');
console.log('   2. 🚀 Start bot: Double-click START_SNIPER.bat');
console.log('   3. 📊 Monitor: Watch console logs and Supabase dashboard');
console.log('   4. 💰 Profit: Bot trades automatically 24/7!');

console.log('\n🗄️ DATABASE INTEGRATION:');
console.log('   ✅ Supabase "sniper1" project connected');
console.log('   ✅ All trades logged to database');
console.log('   ✅ Position tracking in real-time');
console.log('   ✅ Secure encrypted wallet storage');
console.log('   ✅ Complete trade history and P&L tracking');
