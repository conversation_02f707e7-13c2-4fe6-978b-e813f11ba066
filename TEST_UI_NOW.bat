@echo off
echo 🎯 Sol Bullet - TEST THE UI NOW!
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo 🚀 QUICK TEST - SEE THE UI IMMEDIATELY
echo.
echo This will show you the UI without any complex setup
echo No database required for this test!
echo.

echo 📦 Step 1: Installing frontend dependencies...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Installation failed!
    pause
    exit /b 1
)

echo.
echo 🌐 Step 2: Starting test frontend...
echo 📱 Opening browser to: http://localhost:5173
echo.
echo 🎯 WHAT YOU'LL SEE:
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo 🔐 LOGIN PAGE: Modern auth interface
echo 📊 DASHBOARD: Automated sniper controls (NO BUY/SELL BUTTONS!)
echo 🤖 SNIPER CONTROL: Start/Stop automated trading
echo 🔍 TOKEN FEED: Real-time detection with contract addresses
echo 💰 AUTOMATED STATUS: Shows bot actions, not manual trading
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.

start http://localhost:5173
npm run dev

pause
