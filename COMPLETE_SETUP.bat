@echo off
echo 🎯 Sol Bullet - Complete Multi-User Sniper Setup
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo 🚀 Setting up your multi-user automated sniper bot...
echo.

echo 📦 Step 1: Installing frontend dependencies...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Frontend installation failed!
    pause
    exit /b 1
)

echo.
echo 📦 Step 2: Installing backend dependencies...
cd ..\backend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Backend installation failed!
    pause
    exit /b 1
)

echo.
echo ✅ Installation complete!
echo.
echo 🎯 NEXT STEPS:
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.
echo 🚨 CRITICAL: SET UP DATABASE FIRST!
echo    Read: SETUP_SUPABASE.md
echo    Run the SQL schema in your Supabase dashboard
echo.
echo 1. 🗄️ Setup Supabase database:
echo    Follow instructions in: SETUP_SUPABASE.md
echo.
echo 2. 🔧 Update configuration:
echo    Edit: frontend\src\lib\supabase.ts
echo    Add your real Supabase anon key
echo.
echo 3. 🚀 Start the sniper bot:
echo    Run: START_SNIPER_BOT.bat
echo.
echo 4. 🌐 Start the frontend:
echo    Run: START_FRONTEND.bat
echo.
echo 5. 📱 Open your browser:
echo    Go to: http://localhost:5173
echo.
echo 🎯 Your multi-user sniper bot will be ready!
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
pause
