@echo off
echo 🌐 Starting Sol Bullet Frontend...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo 🎨 MULTI-USER FRONTEND INTERFACE
echo 🔐 Authentication: Supabase login/register
echo 💰 Wallet Management: Multi-wallet support
echo 📡 Real-time: Live token detection stream
echo 📱 Responsive: Works on all devices
echo 🌙 Theme: Professional dark trading UI
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.

cd frontend
echo 📁 Changed to frontend directory...
echo 🚀 Starting development server...
echo 🌐 Frontend will be available at: http://localhost:5173
echo.

npm run dev

pause
