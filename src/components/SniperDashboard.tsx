import React, { useState, useEffect } from 'react';

interface SniperStatus {
  isRunning: boolean;
  poolsDetected: number;
  recentTokens: number;
  config: {
    raydiumV4: string;
    minLiquidity: number;
    buyAmount: number;
    takeProfitPercent: number;
    stopLossPercent: number;
    jupiterEnabled: boolean;
  };
  performance: {
    uptime: number;
    memoryUsage: {
      rss: number;
      heapUsed: number;
      heapTotal: number;
      external: number;
    };
  };
}

interface TokenData {
  poolAddress: string;
  tokenAddress: string;
  creator: string;
  liquidity: number;
  holders: number;
  metadata: {
    name: string;
    symbol: string;
    description: string;
    image?: string;
  };
  safetyScore: number;
  action: 'BUY_EXECUTED' | 'REJECTED';
  timestamp: string;
}

export default function SniperDashboard() {
  const [isConnected, setIsConnected] = useState(false);
  const [sniperStatus, setSniperStatus] = useState<SniperStatus | null>(null);
  const [detectedTokens] = useState<TokenData[]>([]);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  useEffect(() => {
    // Try to connect to the sniper backend
    fetchSniperStatus();
    
    // Set up periodic status checks
    const interval = setInterval(fetchSniperStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const fetchSniperStatus = async () => {
    // Try multiple possible backend ports (3002 is the correct backend port)
    const possiblePorts = [3002, 3001, 3000];

    for (const port of possiblePorts) {
      try {
        const backendUrl = `http://localhost:${port}/api/status`;
        const response = await fetch(backendUrl);

        if (response.ok) {
          const data = await response.json();
          // Transform backend response to match frontend interface
          const transformedStatus: SniperStatus = {
            isRunning: data.sniper?.isRunning || false,
            poolsDetected: data.sniper?.poolsDetected || 0,
            recentTokens: data.sniper?.recentTokens || 0,
            config: {
              raydiumV4: data.config?.raydiumV4 || 'Not connected',
              minLiquidity: data.config?.minLiquidity || 5,
              buyAmount: data.config?.buyAmount || 0.1,
              takeProfitPercent: data.config?.takeProfitPercent || 100,
              stopLossPercent: data.config?.stopLossPercent || 50,
              jupiterEnabled: data.config?.jupiterEnabled || false,
            },
            performance: {
              uptime: data.uptime || 0,
              memoryUsage: { rss: 0, heapUsed: 0, heapTotal: 0, external: 0 }
            }
          };
          setSniperStatus(transformedStatus);
          setIsConnected(true);
          setConnectionError(null);
          return; // Success, exit the function
        }
      } catch (error) {
        // Continue to next port
        continue;
      }
    }

    // If we get here, none of the ports worked
    setIsConnected(false);
    setConnectionError('Unable to connect to sniper bot. Make sure the backend is running.');
    console.log('Sniper bot backend not running - this is normal if you haven\'t started it yet.');

    // Set a mock status for demonstration when backend is not running
    setSniperStatus({
      isRunning: false,
      poolsDetected: 0,
      recentTokens: 0,
      config: {
        raydiumV4: 'Not connected',
        minLiquidity: 5,
        buyAmount: 0.1,
        takeProfitPercent: 100,
        stopLossPercent: 50,
        jupiterEnabled: true
      },
      performance: {
        uptime: 0,
        memoryUsage: { rss: 0, heapUsed: 0, heapTotal: 0, external: 0 }
      }
    });
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-6)}`;
  };

  const getSafetyBadgeColor = (score: number) => {
    if (score >= 90) return 'bg-green-500';
    if (score >= 70) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const handleStartStop = async () => {
    if (!sniperStatus) return;

    try {
      const action = sniperStatus.isRunning ? 'stop' : 'start';
      const response = await fetch(`http://localhost:3002/api/sniper/${action}`, {
        method: 'POST',
      });

      if (response.ok) {
        // Refresh status after action
        setTimeout(fetchSniperStatus, 1000);
      } else {
        console.error(`Failed to ${action} sniper`);
      }
    } catch (error) {
      console.error('Error controlling sniper:', error);
    }
  };

  return (
    <div className="w-full max-w-4xl space-y-6">
      {/* Connection Status */}
      <div className={`p-4 rounded-lg border ${
        isConnected
          ? 'bg-green-900/20 border-green-700 text-green-100'
          : 'bg-yellow-900/20 border-yellow-700 text-yellow-100'
      }`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              isConnected ? 'bg-green-500' : 'bg-yellow-500'
            }`}></div>
            <span className="font-semibold">
              {isConnected ? 'Connected to Sniper Bot' : 'Backend Not Running'}
            </span>
          </div>
          <div className="flex space-x-2">
            {sniperStatus && (
              <button
                onClick={handleStartStop}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  sniperStatus.isRunning
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-green-600 hover:bg-green-700 text-white'
                }`}
              >
                {sniperStatus.isRunning ? '⏹️ Stop' : '▶️ Start'}
              </button>
            )}
            <button
              onClick={fetchSniperStatus}
              className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
            >
              Refresh
            </button>
          </div>
        </div>
        {connectionError && (
          <div className="mt-2 text-sm opacity-90">
            <p>{connectionError}</p>
            <p className="mt-1 text-xs opacity-75">
              To start the backend: <code className="bg-gray-800 px-1 rounded">cd backend && npm start</code>
            </p>
            <p className="mt-1 text-xs opacity-75">
              Backend will run on port 3000, 3002, or 3003 (whichever is available)
            </p>
          </div>
        )}
      </div>

      {/* Sniper Status */}
      {sniperStatus && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
            <h3 className="text-sm font-medium text-gray-400 mb-1">Status</h3>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                sniperStatus.isRunning ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              <span className="text-white font-semibold">
                {sniperStatus.isRunning ? 'Running' : 'Stopped'}
              </span>
            </div>
          </div>

          <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
            <h3 className="text-sm font-medium text-gray-400 mb-1">Pools Detected</h3>
            <p className="text-2xl font-bold text-white">{sniperStatus.poolsDetected}</p>
          </div>

          <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
            <h3 className="text-sm font-medium text-gray-400 mb-1">Recent Tokens</h3>
            <p className="text-2xl font-bold text-white">{sniperStatus.recentTokens}</p>
          </div>

          <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
            <h3 className="text-sm font-medium text-gray-400 mb-1">Uptime</h3>
            <p className="text-lg font-bold text-white">
              {formatUptime(sniperStatus.performance.uptime)}
            </p>
          </div>
        </div>
      )}

      {/* Configuration */}
      {sniperStatus && (
        <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">Configuration</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Buy Amount:</span>
              <span className="text-white ml-2">{sniperStatus.config.buyAmount} SOL</span>
            </div>
            <div>
              <span className="text-gray-400">Min Liquidity:</span>
              <span className="text-white ml-2">{sniperStatus.config.minLiquidity} SOL</span>
            </div>
            <div>
              <span className="text-gray-400">Take Profit:</span>
              <span className="text-white ml-2">{sniperStatus.config.takeProfitPercent}%</span>
            </div>
            <div>
              <span className="text-gray-400">Stop Loss:</span>
              <span className="text-white ml-2">{sniperStatus.config.stopLossPercent}%</span>
            </div>
            <div>
              <span className="text-gray-400">Jupiter Enabled:</span>
              <span className={`ml-2 ${sniperStatus.config.jupiterEnabled ? 'text-green-400' : 'text-red-400'}`}>
                {sniperStatus.config.jupiterEnabled ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Recent Token Activity */}
      <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Token Activity</h3>
        {detectedTokens.length === 0 ? (
          <p className="text-gray-400 text-center py-8">
            No recent token activity. The sniper bot will show detected tokens here.
          </p>
        ) : (
          <div className="space-y-3">
            {detectedTokens.slice(0, 5).map((token, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-white">{token.metadata.symbol}</span>
                    <span className={`px-2 py-1 rounded text-xs ${getSafetyBadgeColor(token.safetyScore)} text-white`}>
                      {token.safetyScore}%
                    </span>
                  </div>
                  <p className="text-sm text-gray-400">{formatAddress(token.tokenAddress)}</p>
                </div>
                <div className="text-right">
                  <div className={`px-2 py-1 rounded text-xs ${
                    token.action === 'BUY_EXECUTED' 
                      ? 'bg-green-600 text-white' 
                      : 'bg-red-600 text-white'
                  }`}>
                    {token.action === 'BUY_EXECUTED' ? 'BOUGHT' : 'REJECTED'}
                  </div>
                  <p className="text-xs text-gray-400 mt-1">
                    {new Date(token.timestamp).toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="bg-blue-900/20 border border-blue-700 p-4 rounded-lg">
        <h4 className="text-blue-100 font-semibold mb-2">🤖 Automated Sniper Bot</h4>
        <p className="text-blue-200 text-sm">
          This is an automated sniper bot that detects new tokens on Raydium and executes trades based on your configured settings.
          The bot runs independently and will show activity here when tokens are detected.
        </p>
        <div className="mt-3 text-blue-200 text-sm">
          <p className="font-medium mb-1">To start the sniper bot:</p>
          <ol className="list-decimal list-inside space-y-1 text-xs bg-gray-800/50 p-2 rounded">
            <li>Open a new terminal</li>
            <li>Navigate to backend: <code className="bg-gray-700 px-1 rounded">cd backend</code></li>
            <li>Start the bot: <code className="bg-gray-700 px-1 rounded">npm start</code> or <code className="bg-gray-700 px-1 rounded">node live-sniper.js</code></li>
            <li>The backend will automatically find an available port</li>
            <li>Refresh this page to see the connection</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
